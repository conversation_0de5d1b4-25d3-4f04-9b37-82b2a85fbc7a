import { useCandleStickStore } from "@/store/candleStickStore";
import { useChartStore } from "@/store/chartStore";

import {
  DEFAULT_ADDITIONAL_LOAD_COUNT,
  loadCandleStickData
} from "@/utilities/candle-sticks";
import { isDevOrStaging } from "@/utilities/env-check";

import { indexFromTime } from "./utils";

let isMoreDataLoading = false;

export const loadMoreOHLCData = async (data: {
  hi: number;
  lo: number;
  count: number;
  indexBased: boolean;
  broker: string;
}): Promise<number> => {
  if (isMoreDataLoading) {
    return 0;
  }

  isMoreDataLoading = true;

  try {
    const chartStore = useChartStore();
    const candleStickStore = useCandleStickStore();

    if (!chartStore.chart) {
      throw new Error("Cannot find chart instance");
    }

    const chart = chartStore.chart!;

    let lastTimestamp: number = chart.hub.mainOv.data[0][0];

    let { loIndex, hiIndex } = indexFromTime(
      data,
      new Date(lastTimestamp).valueOf()
    );

    if (data.indexBased) {
      loIndex = data.lo;
      hiIndex = data.hi;
    }

    if (loIndex > DEFAULT_ADDITIONAL_LOAD_COUNT || chartStore.endOfData) {
      return lastTimestamp;
    }

    let fetchCount;

    if (loIndex < 0) {
      fetchCount = DEFAULT_ADDITIONAL_LOAD_COUNT - Math.floor(loIndex);
    }

    try {
      const { candleStick, last_timestamp, count, message } =
        await loadCandleStickData(
          candleStickStore.symbol,
          candleStickStore.interval,
          data.broker,
          lastTimestamp,
          fetchCount
        );

      if (message === "End of OHLC data.") {
        chartStore.endOfData = true;
      }

      if (last_timestamp) {
        lastTimestamp = last_timestamp;
      }

      const d = [...candleStick.slice(0), ...chart.hub.mainOv.data];
      chart.hub.mainOv.data = d;

      if (!chartStore.shapeToolInstance) {
        throw new Error(
          "Shape tool instance not found, cannot update shape coordinates"
        );
      }

      try {
        chartStore.shapeToolInstance.updateShapeCoordinates();
      } catch (e) {
        console.error(e);
      }

      chartStore.updateMarketClosingLines(d);

      if (data.indexBased && candleStickStore.interval !== "PERIOD_W1") {
        chart.range = [chart.range[0] + count, chart.range[1] + count];
      }

      chart.update("data");

      let newData;

      if (data.indexBased) {
        newData = {
          ...data,
          lo: chart.range[0] + count,
          hi: chart.range[1] + count
        };
      } else {
        newData = {
          ...data,
          lo: chart.range[0],
          hi: chart.range[1]
        };
      }

      return await loadMoreOHLCData(newData);
    } catch (e) {
      if (isDevOrStaging()) {
        console.error(e);
      }
      return lastTimestamp;
    }
  } finally {
    isMoreDataLoading = false;
  }
};
