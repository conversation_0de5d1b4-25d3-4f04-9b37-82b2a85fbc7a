@import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-primary: 26 86 219; /* bg-blue-700 (#1a56db) */
    --color-secondary: 30 66 159; /* bg-blue-800 (#1e429f) */
    --color-tertiary: 35 56 118; /* bg-blue-900 (#233876) */
    --color-accent: 243 244 246; /* bg-gray-100 (#f3f4f6) */
    --color-selected: 26 86 219; /* bg-blue-700 (#1a56db) */
    --color-info: 26 86 219; /* bg-blue-700 (#1a56db) */
    --color-success: 49 196 141; /* bg-green-400 (#31c48d) */
    --color-warning: 255 138 76 /* bg-orange-400 (#ff8a4c) */;
    --color-danger: 225 0 0; /* red (#ff0000) */
    --color-backdrop: 0 0 0; /* bg-black (#000000) */

    --toastify-color-info: #1a56db;
    --toastify-toast-width: 450px !important;
  }
}

#app-bar,
#right-nav-drawer,
#right-nav-content-area,
#bottom-nav-drawer,
#botton-nav-content-area,
#left-nav-drawer {
  cursor: default;
}

html,
body {
  height: 100%;
}

#app {
  height: 100%;
}

button {
  cursor: default;
}

button:focus {
  outline: none;
}

table thead tr {
  user-select: none;
}

table tbody tr {
  cursor: default;
}

.Toastify__toast {
  border-radius: 0.75rem;
  border: 1px solid #dbdbdb;
}

.Toastify__toast-body {
  align-items: start;
  font-size: 0.875rem;
  color: #4b5563;
}

.Toastify__toast-icon {
  margin-top: 2px;
}

.scrollbar {
  scrollbar-width: thin;
}
