<template>
  <div class="bg-white py-16">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="flex flex-col md:flex-row gap-6 items-center">
        <div class="flex flex-1 flex-col justify-center">
          <h2
            class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"
          >
            Why choose us?
          </h2>
          <div class="mt-10 space-y-6">
            <div class="flex gap-4">
              <Target class="h-6 w-6 flex-shrink-0 text-blue-600" />
              <p class="text-base text-gray-600">
                <span class="font-bold">Our platform</span> – Pinpoint trades
                from the charts, utilize auto position sizing, automated partial
                take profit, and trailing RR. All based on percentages not pip.
              </p>
            </div>
            <div class="flex gap-4">
              <BarChart2 class="h-6 w-6 flex-shrink-0 text-blue-600" />
              <p class="text-base text-gray-600">
                <span class="font-bold">Analytics</span> – Access real-time
                trade and account analytics. Our algorithm tracks your trading
                performance and provides feedback to help you increase
                profitability and consistency.
              </p>
            </div>
            <div class="flex gap-4">
              <Activity class="h-6 w-6 flex-shrink-0 text-blue-600" />
              <p class="text-base text-gray-600">
                <span class="font-bold">Our Intra-day strategy</span> covers all
                forex pairs, not just one, and provides instant alerts on daily
                trends to highlight pairs with the most movement. We use tools
                like daily range theory, forex session theory, and pattern
                formation to identify high-probability setups. With unique
                strategies for each trading session and data-driven tools, we
                make these strategies accessible for anyone to learn and master.
              </p>
            </div>
            <div class="flex gap-4">
              <Shield class="h-6 w-6 flex-shrink-0 text-blue-600" />
              <p class="text-base text-gray-600">
                <span class="font-bold">Risk Management</span>
                - Our trading system prioritizes risk management, focusing on
                factors like position sizing, market risk news, portfolio risk,
                and adjusting risk in open positions. The main goal is to
                protect capital by actively managing risk, and we continuously
                seek automated tools to enhance this process.
              </p>
            </div>
            <div class="flex gap-4">
              <BookOpen class="h-6 w-6 flex-shrink-0 text-blue-600" />
              <p class="text-base text-gray-600">
                <span class="font-bold">Fundamentals</span> – We guide students
                in understanding the key factors influencing price action,
                including risk sentiment, economic news, political events, and
                yield market trends. We also deliver daily market calls with an
                overview and insights into the anticipated market movements for
                the day.
              </p>
            </div>
            <div class="flex gap-4">
              <LifeBuoy class="h-6 w-6 flex-shrink-0 text-blue-600" />
              <p class="text-base text-gray-600">
                <span class="font-bold">Ongoing support</span> - if you find
                yourself stuck in a significant drawdown, we offer advice to
                help you get back on track and improve your trading.
              </p>
            </div>
          </div>
        </div>

        <div class="relative flex-1 mt-10 lg:mt-0">
          <div class="relative overflow-hidden rounded-2xl">
            <img
              src="https://images.unsplash.com/photo-1491555103944-7c647fd857e6?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
              alt="Trading goals visualization"
              class="h-[600px] w-full object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Activity,
  BarChart2,
  BookOpen,
  LifeBuoy,
  Shield,
  Target
} from "lucide-vue-next";
</script>
