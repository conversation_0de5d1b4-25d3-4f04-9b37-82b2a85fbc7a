import { loadEnv } from "vite";

export function viteRawPlugin({ fileRegex }: { fileRegex: RegExp }) {
  return {
    name: "vite-raw-plugin",
    transform(code: any, id: string) {
      if (fileRegex.test(id)) {
        const json = JSON.stringify(code)
          .replace(/\u2028/g, "\\u2028")
          .replace(/\u2029/g, "\\u2029");

        return {
          code: `export default ${json}`
        };
      }
    }
  };
}

export function seo() {
  const seoTags = [
    '<link rel="canonical" href="https://www.tradeawaay.com" />\n',
    '<meta name="robots" content="index, follow"/>\n',
    '<meta name="description" content="Tradeawaay is a web-based trading application powered by data analytics for traders to make calculated decisions in trading. Tradeawaay provides full trading experience by connecting to your MetaTrader 5 through Expert Advisor."/>\n',
    '<meta name="twitter:url" content="https://www.tradeawaay.com"/>\n',
    '<meta name="twitter:title" content="Tradeawaay – Maximize Your Decision Making In Trading"/>\n',
    '<meta name="twitter:description" content="Tradeawaay is a web-based trading application powered by data analytics for traders to make calculated decisions in day trading. Tradeawaay provides full trading experience by connecting to your MetaTrader 5 through Expert Advisor."/>\n',
    '<meta name="twitter:card" content="summary"/>\n',
    '<meta property="og:url" content="https://www.tradeawaay.com"/>',
    '<meta property="og:title" content="Tradeawaay – Maximize Your Decision Making In Trading"/>\n',
    ' <meta property="og:description" content="Tradeawaay is a web-based trading application powered by data analytics for traders to make calculated decisions in day trading. Tradeawaay provides full trading experience by connecting to your MetaTrader 5 through Expert Advisor."/>\n',
    '<meta property="og:type" content="website"/>'
  ];

  const env = loadEnv("production", process.cwd(), "");

  return {
    name: "html-transform",
    transformIndexHtml(html: string) {
      if (env.VITE_APP_ENV === "production") {
        let tags = "";

        seoTags.forEach((t) => {
          tags += t;
        });

        html = html.replace(
          '<meta name="robots" content="noindex, nofollow" />',
          tags
        );
      }

      return html;
    }
  };
}
