<script setup lang="ts">
import { ref } from "vue";

import VideoModal from "./VideoModal.vue";
import HeroSection from "./HeroSection.vue";
import ValueProposition from "./ValueProposition.vue";

const toggleVideoModal = ref(false);
</script>

<template>
  <Teleport to="body">
    <VideoModal
      link="https://www.youtube.com/embed/itYyY7JVfUg"
      @close="toggleVideoModal = false"
      v-if="toggleVideoModal"
    />
  </Teleport>

  <section id="home">
    <HeroSection />

    <ValueProposition />
  </section>
</template>
