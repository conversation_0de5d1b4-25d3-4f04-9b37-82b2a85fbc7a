import { countDecimals } from "./numberUtils";
import { MarketWatchSymbol } from "@/types";
import { EActiveOrders, EOrderType, SymbolCalcMode } from "@/types/enums";

interface ExpectedProfit {
  orderType: EOrderType | EActiveOrders;
  symbol: string;
  price: number;
  tp: number;
  volume: number;
  marketWatchSymbolList: MarketWatchSymbol[];
  accountCurrency?: string;
}

interface ExpectedLoss {
  orderType: EOrderType | EActiveOrders;
  symbol: string;
  price: number;
  sl: number;
  volume: number;
  marketWatchSymbolList: MarketWatchSymbol[];
  accountCurrency?: string;
}

interface MarginCalculation {
  orderType: EActiveOrders;
  marketPrice: number;
  refMarginCurrency: number;
  symbol: string;
  volume: number;
  leverage?: number;
  marketWatchSymbolList: MarketWatchSymbol[];
}

export function calculatePips(price1: number, price2: number, digit?: number) {
  let decimalCount = 0;

  if (digit) {
    decimalCount = digit;
  } else {
    decimalCount = countDecimals(price1);
  }

  const FACTOR = decimalCount >= 4 ? 10000 : 100;

  return parseFloat((Math.abs(price1 - price2) * FACTOR).toFixed(decimalCount));
}

/**
 *
 * @param symbol Market Watch symbol.
 * @param marketWatchSymbolList Market Watch Symbol List.
 * @param accountCurrency EA Account Currency (default: USD).
 * @returns Ref Price and Contract Size.
 */
export function getRefPriceAndContractSize(
  symbol: string,
  marketWatchSymbolList: MarketWatchSymbol[],
  accountCurrency = "USD"
) {
  let refPrice = 1;
  let contractSize = "";

  const item = marketWatchSymbolList.find((v) => v.symbol === symbol);

  if (!item) {
    return {
      refPrice: 1,
      contractSize: 1
    };
  }

  const refCurrency = item.symbol_profit_currency;

  if (refCurrency === accountCurrency) {
    refPrice = 1;
  } else {
    const temp = marketWatchSymbolList.find(
      (v) =>
        v.symbol_margin_currency === accountCurrency &&
        v.symbol_profit_currency === refCurrency
    );

    if (temp) {
      refPrice = temp.bid;
    } else {
      const temp1 = marketWatchSymbolList.find(
        (v) =>
          v.symbol_margin_currency === refCurrency &&
          v.symbol_profit_currency === accountCurrency
      );

      if (temp1) {
        refPrice = 1 / temp1.bid;
      }
    }
  }

  contractSize = item.symbol_contract_size;

  return {
    refPrice,
    contractSize: parseInt(contractSize)
  };
}

export function calculateExpectedProfit({
  orderType,
  symbol,
  price,
  tp,
  volume,
  marketWatchSymbolList,
  accountCurrency = "USD"
}: ExpectedProfit) {
  const { refPrice, contractSize } = getRefPriceAndContractSize(
    symbol,
    marketWatchSymbolList,
    accountCurrency
  );

  let expProfit = 0;

  if (tp === 0) {
    return expProfit;
  } else if (
    orderType === EOrderType.ORDER_TYPE_SELL ||
    orderType === EActiveOrders.POSITION_TYPE_SELL
  ) {
    expProfit = ((price - tp) * (contractSize * volume)) / refPrice;
  } else if (
    orderType === EOrderType.ORDER_TYPE_BUY ||
    orderType === EActiveOrders.POSITION_TYPE_BUY
  ) {
    expProfit = ((tp - price) * (contractSize * volume)) / refPrice;
  }

  return parseFloat(expProfit.toFixed(2));
}

export function calculateExpectedLoss({
  orderType,
  symbol,
  price,
  sl,
  volume,
  marketWatchSymbolList,
  accountCurrency = "USD"
}: ExpectedLoss) {
  const { refPrice, contractSize } = getRefPriceAndContractSize(
    symbol,
    marketWatchSymbolList,
    accountCurrency
  );

  let expLoss = 0;

  if (sl === 0) {
    return expLoss;
  } else if (
    orderType === EOrderType.ORDER_TYPE_SELL ||
    orderType === EActiveOrders.POSITION_TYPE_SELL
  ) {
    expLoss = ((price - sl) * (contractSize * volume)) / refPrice;
  } else if (
    orderType === EOrderType.ORDER_TYPE_BUY ||
    orderType === EActiveOrders.POSITION_TYPE_BUY
  ) {
    expLoss = ((sl - price) * (contractSize * volume)) / refPrice;
  }

  return parseFloat(expLoss.toFixed(2));
}

export function calculateMarginForActiveOrders({
  orderType,
  refMarginCurrency,
  marketPrice,
  symbol,
  volume,
  leverage = 100,
  marketWatchSymbolList
}: MarginCalculation) {
  let margin = 1;

  const item = marketWatchSymbolList.find((v) => v.symbol === symbol);

  if (!item) {
    return margin;
  }

  const contractSize = parseInt(item.symbol_contract_size);

  let marginRate = 1;

  if (orderType === EActiveOrders.POSITION_TYPE_SELL) {
    marginRate = item.symbol_initial_margin_sell;
  } else if (orderType === EActiveOrders.POSITION_TYPE_BUY) {
    marginRate = item.symbol_initial_margin_buy;
  }

  switch (item.symbol_trade_calc_mode) {
    case SymbolCalcMode.SYMBOL_CALC_MODE_FOREX:
      margin = (volume * contractSize) / (leverage * marginRate);
      break;
    case SymbolCalcMode.SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE:
      margin = volume * contractSize * marketPrice * marginRate;
      break;
    case SymbolCalcMode.SYMBOL_CALC_MODE_CFD:
      margin = volume * contractSize * marketPrice * marginRate;
      break;
    case SymbolCalcMode.SYMBOL_CALC_MODE_CFDINDEX:
      margin =
        volume *
        contractSize *
        marketPrice *
        (item.symbol_tick_value / (item.symbol_tick_size * (1 / marginRate)));
      break;
    case SymbolCalcMode.SYMBOL_CALC_MODE_CFDLEVERAGE:
      margin =
        (volume * contractSize * marketPrice) / (leverage * (1 / marginRate));
      break;
    default:
      margin = 1;
  }

  margin = margin * refMarginCurrency;

  return parseFloat(margin.toFixed(2));
}
