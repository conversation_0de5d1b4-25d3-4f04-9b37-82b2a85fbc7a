<script setup lang="ts">
import { ref } from "vue";

import ProfileHeader from "./ProfileHeader.vue";

import Alert from "@/components/Alert.vue";
import Select from "@/components/Select.vue";
import InputLabel from "@/components/InputLabel.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const country = ref("");
const state = ref("");
</script>

<template>
  <ProfileHeader text="Change Location" route-name="edit" />

  <div class="px-5">
    <Alert variant="info">
      Your location helps us to customize your trading experience.
      <br />
      Only change locaton when move from one country/state to another.
    </Alert>

    <div class="mt-5">
      <InputLabel for="country">Country</InputLabel>

      <Select id="country" v-model="country">
        <option value="usa">USA</option>
        <option value="canada">Canada</option>
        <option value="mexico">Mexico</option>
        <option value="uk">United Kingdom</option>
        <option value="australia">Australia</option>
      </Select>
    </div>

    <div class="mt-5">
      <InputLabel for="state">State/Province</InputLabel>

      <Select id="state" v-model="state">
        <option value="ontario">Ontario</option>
        <option value="quebec">Quebec</option>
        <option value="nova_scotia">Nova Scotia</option>
        <option value="new_brunswick">New Brunswick</option>
        <option value="british_columbia">British Columbia</option>
        <option value="prince_edward_island">Prince Edward Island</option>
        <option value="alberta">Alberta</option>
        <option value="newfoundland_and_labrador">
          Newfoundland and Labrador
        </option>
      </Select>
    </div>

    <div class="mt-5">
      <PrimaryButton class="w-full">Save Changes</PrimaryButton>
    </div>
  </div>
</template>
