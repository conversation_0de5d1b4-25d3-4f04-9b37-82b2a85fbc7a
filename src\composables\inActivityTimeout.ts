import { onMounted, onUnmounted, ref } from "vue";

export function useInActivityTimeout() {
  const events = ref(["click", "mousemove", "keypress", "scroll"]);
  const seconds = ref(60);
  const minutes = ref(15);
  const warningTimer = ref<NodeJS.Timeout>();
  const warningStatus = ref(false);

  onMounted(() => {
    events.value.forEach((event) => {
      window.addEventListener(event, resetTimer);
    });

    setTimers();
  });

  onUnmounted(() => {
    events.value.forEach((event) => {
      window.removeEventListener(event, resetTimer);
    });

    clearTimeout(warningTimer.value);
  });

  function setTimers() {
    warningTimer.value = setTimeout(
      () => {
        warningStatus.value = true;
      },
      minutes.value * seconds.value * 1000
    );
  }

  function resetTimer() {
    clearTimeout(warningTimer.value);

    warningStatus.value = false;

    setTimers();
  }

  return {
    warningStatus
  };
}
