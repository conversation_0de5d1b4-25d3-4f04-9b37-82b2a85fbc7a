import { DateTime } from "luxon";

export function convertToRelative(isoString: string) {
  const dt = DateTime.fromISO(isoString);
  const now = DateTime.now();

  // Return null for future timestamps
  if (dt > now) {
    return null;
  }

  const diff = now.diff(dt, ["weeks", "days", "hours", "minutes"]).toObject();

  if ((diff.weeks || 0) >= 1) {
    return Math.round(diff.weeks || 0) + "w";
  } else if ((diff.days || 0) >= 1) {
    return Math.round(diff.days || 0) + "d";
  } else if ((diff.hours || 0) >= 1) {
    return Math.round(diff.hours || 0) + "h";
  } else {
    return Math.round(diff.minutes || 0) + "m";
  }
}
