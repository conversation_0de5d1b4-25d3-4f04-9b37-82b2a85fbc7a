<script setup lang="ts">
import { ref } from "vue";
import {
  ArrowLeftCircle,
  ArrowRightCircle,
  GraduationCap,
  Box,
  Target
} from "lucide-vue-next";

const currentIndex = ref(0);
const touchStart = ref<number | null>(null);
const touchEnd = ref<number | null>(null);
const minSwipeDistance = 50;

const cards = [
  {
    title: "Intro",
    list1: [
      "Explore the FX Market",
      "Market Pricing Mechanism",
      "Market Dynamics",
      "Candlestick chart and patterns",
      "Using Fibonacci in trading",
      "Read the calendar of economic events"
    ],
    title2: "Intro to Trading Operation",
    list2: [
      "Order types - instant and pending",
      "Trading terminology - buy, sell, ask, big, spread, equity, low",
      "Understand margin trading, leverage, swaps, slippage, swap"
    ],
    icon: GraduationCap,
    iconClass: "size-14 text-blue-500",
    gradient: "from-blue-500 to-cyan-400"
  },
  {
    title: "The Sandbox",
    subtitle: "PRACTICE WHILE OUR ALGOS TRACK YOUR PROGRESS",
    list1: [
      "Overview of Tradeaway's Web Trading Platform/setting up with Metatrader5",
      "Charting 1: drawing zigzag lines, trendlines, pennants, Double Tops & Bottoms, trendline channels",
      "Charting 2: Identifying accumulation zone, support & resistance, swing/inflection points"
    ],
    title2: "Learn to Trade",
    list2: [
      "Understanding trade parameters: stoploss, take profit, partial close",
      "Deep dive into Risk Management and Tradeawaay's built in risk management tools",
      "How to read Advanced Heatmap to identify strength in currency pairs",
      "Develop a rule-based trading system",
      "Test one of our intra day trading strategy",
      "Daily Market Morning calls"
    ],
    icon: Box,
    iconClass: "size-14 text-purple-500",
    gradient: "from-purple-500 to-pink-400"
  },
  {
    title: "The Arena",
    subtitle: "Time to shine",
    description:
      "Once you are successful in sandbox, Tradeawaay will help you navigate the prop firm industry and its rules.",
    title2: "For Challenge Phase:",
    description2:
      "You can continue to trade on our platform. we will track your progress. If you hit a drawdown, we will provide feedback.",
    title3: "Funded Phase:",
    description3:
      "It is crucial not to breach rules in the Funded Phase. Any breach will require you to redo the Challenge Phase, which is unpaid. If you experience a drawdown, our algorithms will identify the risk, and you can return to sandbox mode to practice until you regain your confidence.",
    icon: Target,
    iconClass: "size-14 text-orange-500",
    gradient: "from-orange-500 to-red-400"
  }
];

const handlePrevious = () => {
  currentIndex.value =
    currentIndex.value === 0 ? cards.length - 1 : currentIndex.value - 1;
};

const handleNext = () => {
  currentIndex.value =
    currentIndex.value === cards.length - 1 ? 0 : currentIndex.value + 1;
};

const onTouchStart = (e: TouchEvent) => {
  touchEnd.value = null;
  touchStart.value = e.targetTouches[0].clientX;
};

const onTouchMove = (e: TouchEvent) => {
  touchEnd.value = e.targetTouches[0].clientX;
};

const onTouchEnd = () => {
  if (!touchStart.value || !touchEnd.value) return;

  const distance = touchStart.value - touchEnd.value;
  const isLeftSwipe = distance > minSwipeDistance;
  const isRightSwipe = distance < -minSwipeDistance;

  if (isLeftSwipe) {
    handleNext();
  }
  if (isRightSwipe) {
    handlePrevious();
  }
};
</script>

<template>
  <div class="mx-auto w-full max-w-6xl px-4 py-12">
    <div class="relative">
      <button
        @click="handlePrevious"
        class="absolute left-0 top-1/2 z-10 hidden h-12 w-12 -translate-x-12 -translate-y-1/2 items-center justify-center rounded-full bg-white text-blue-500 shadow-lg hover:text-blue-800 hover:shadow-xl md:flex"
        aria-label="Previous card"
      >
        <ArrowLeftCircle class="h-6 w-6 transition-colors" />
      </button>

      <button
        @click="handleNext"
        class="absolute right-0 top-1/2 z-10 hidden h-12 w-12 -translate-y-1/2 translate-x-12 items-center justify-center rounded-full bg-white text-blue-500 shadow-xl hover:text-blue-800 hover:shadow-xl md:flex"
        aria-label="Next card"
      >
        <ArrowRightCircle class="h-6 w-6 transition-colors" />
      </button>

      <div
        class="overflow-hidden"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
      >
        <div
          class="flex transition-transform duration-300 ease-in-out"
          :style="{ transform: `translateX(-${currentIndex * 100}%)` }"
        >
          <div
            v-for="(card, index) in cards"
            :key="index"
            class="w-full flex-shrink-0"
          >
            <div
              class="mx-auto min-h-[30rem] max-w-4xl rounded-lg bg-gray-50 px-6 shadow-xl transition-shadow duration-300 hover:shadow-2xl md:p-14"
            >
              <div class="flex items-start space-x-4">
                <div class="flex-1 space-y-4">
                  <div>
                    <div
                      class="mb-6 flex flex-col items-center justify-center gap-2"
                    >
                      <div class="flex items-center justify-center gap-4">
                        <component :is="card.icon" :class="card.iconClass" />
                        <h3
                          class="text-3xl font-bold text-gray-900 md:text-4xl"
                        >
                          {{ card.title }}
                        </h3>
                      </div>
                      <p v-if="card.subtitle" class="mt-1 text-gray-500">
                        {{ card.subtitle }}
                      </p>
                    </div>

                    <p v-if="card.description" class="mt-2 text-gray-600">
                      {{ card.description }}
                    </p>
                  </div>

                  <div v-if="card.list1" class="space-y-2">
                    <ul class="list-disc space-y-1">
                      <li
                        v-for="(item, itemIndex) in card.list1"
                        :key="itemIndex"
                        class="text-gray-600"
                      >
                        {{ item }}
                      </li>
                    </ul>
                  </div>

                  <div v-if="card.title2" class="space-y-2">
                    <h4 class="text-lg font-bold text-gray-900">
                      {{ card.title2 }}
                    </h4>
                    <p v-if="card.description2" class="text-gray-600">
                      {{ card.description2 }}
                    </p>
                    <ul v-if="card.list2" class="list-disc space-y-1">
                      <li
                        v-for="(item, itemIndex) in card.list2"
                        :key="itemIndex"
                        class="text-gray-600"
                      >
                        {{ item }}
                      </li>
                    </ul>
                  </div>

                  <div v-if="card.title3" class="space-y-2">
                    <h4 class="text-lg font-bold text-gray-900">
                      {{ card.title3 }}
                    </h4>
                    <p v-if="card.description3" class="text-gray-600">
                      {{ card.description3 }}
                    </p>
                  </div>

                  <div class="flex items-center justify-between pt-4">
                    <span class="text-sm text-gray-500">
                      {{ index + 1 }} / {{ cards.length }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-4 flex justify-center space-x-2 md:hidden">
        <button
          v-for="(_, index) in cards"
          :key="index"
          @click="currentIndex = index"
          class="focus:outline-none"
          :aria-label="`Go to slide ${index + 1}`"
        >
          <div
            :class="[
              'h-2 w-2 rounded-full transition-colors duration-300',
              index === currentIndex ? 'bg-blue-500' : 'bg-gray-300'
            ]"
          />
        </button>
      </div>
    </div>
  </div>
</template>
