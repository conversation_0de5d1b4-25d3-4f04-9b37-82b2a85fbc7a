import { DateTime } from "luxon";

import { useCandleStickStore } from "@/store/candleStickStore";
import { useChartStore } from "@/store/chartStore";

import { initializeOHLCSocket, ohlcSocket } from "@/socketio";
import { IIncompleteOHLC } from "@/types";

export function handleOHLCBars(accessToken: string) {
  const candleStickStore = useCandleStickStore();
  const chartStore = useChartStore();

  initializeOHLCSocket(accessToken);

  ohlcSocket?.emit("subscribe", {
    symbol: candleStickStore.symbol,
    period: candleStickStore.interval,
    broker: candleStickStore.symbolBroker
  });

  // Box Plot
  ohlcSocket?.emit("subscribe", {
    symbol: candleStickStore.symbol,
    period: "PERIOD_D1",
    broker: candleStickStore.symbolBroker
  });

  ohlcSocket?.on("ohlc_update", (d) => {
    if (!chartStore.chart) {
      return;
    }

    //Box Plot
    if (d.period === "PERIOD_D1") {
      candleStickStore.boxPlot.high_price = d["ohlc_data"][1]["high"];
      candleStickStore.boxPlot.low_price = d["ohlc_data"][1]["low"];
    }

    if (
      candleStickStore.symbol !== d.symbol ||
      candleStickStore.interval !== d.period
    ) {
      return;
    }

    /*
      Here, ohlc time is based on Eastern European Time (EET, +2 UTC).
      We need to convert it to UTC time.
      E.g. 2024.11.15 18:25 => 2024-11-15T16:25:00.000Z (ISO)
    */
    const serverPrevOHLC: IIncompleteOHLC = d["ohlc_data"][0];
    const serverLatestOHLC: IIncompleteOHLC = d["ohlc_data"][1];

    const ohlcPrevDate = serverPrevOHLC.time;
    const ohlcLatestDate = serverLatestOHLC.time;

    const serverPrevTime = DateTime.fromISO(ohlcPrevDate!).toMillis();
    const serverLatestTime = DateTime.fromISO(ohlcLatestDate!).toMillis();

    const { data: chartData } = chartStore.chart.hub.mainOv;

    const lastIndex = chartData.length - 1;
    const lastOHLCData = chartData[lastIndex];
    const lastOHLCTime = lastOHLCData[0];

    if (serverLatestTime > lastOHLCTime) {
      if (serverPrevTime > lastOHLCTime) {
        // OHLC bar is complete but it is not inserted into database yet.
        // So we have to fill the latest second ohlc bar manually to remove the ohlc bar gap.
        const ohlc = [
          serverPrevTime,
          serverPrevOHLC["open"],
          serverPrevOHLC["high"],
          serverPrevOHLC["low"],
          serverPrevOHLC["close"],
          serverPrevOHLC["tickVolume"]
        ];

        chartStore.chart.hub.mainOv.data.push(ohlc);

        candleStickStore.ohlc = [
          {
            open: serverPrevOHLC["open"],
            high: serverPrevOHLC["high"],
            low: serverPrevOHLC["low"],
            close: serverPrevOHLC["close"],
            tickVolume: serverPrevOHLC["tickVolume"],
            realVolume: serverPrevOHLC["realVolume"],
            timestamp: ohlcPrevDate!,
            meta: {
              symbol: candleStickStore.symbol
            }
          },
          ...candleStickStore.ohlc
        ];
      } else {
        chartData[lastIndex][1] = serverPrevOHLC["open"];
        chartData[lastIndex][2] = serverPrevOHLC["high"];
        chartData[lastIndex][3] = serverPrevOHLC["low"];
        chartData[lastIndex][4] = serverPrevOHLC["close"];
        chartData[lastIndex][5] = serverPrevOHLC["tickVolume"];

        candleStickStore.ohlc[0] = {
          open: serverPrevOHLC["open"],
          high: serverPrevOHLC["high"],
          low: serverPrevOHLC["low"],
          close: serverPrevOHLC["close"],
          tickVolume: serverPrevOHLC["tickVolume"],
          realVolume: serverPrevOHLC["realVolume"],
          timestamp: ohlcPrevDate!,
          meta: {
            symbol: candleStickStore.symbol
          }
        };
      }

      // Push the ohlc data
      const ohlc = [
        serverLatestTime,
        serverLatestOHLC["open"],
        serverLatestOHLC["high"],
        serverLatestOHLC["low"],
        serverLatestOHLC["close"],
        serverLatestOHLC["tickVolume"]
      ];

      chartStore.chart.hub.mainOv.data.push(ohlc);

      candleStickStore.ohlc = [
        {
          open: serverLatestOHLC["open"],
          high: serverLatestOHLC["high"],
          low: serverLatestOHLC["low"],
          close: serverLatestOHLC["close"],
          tickVolume: serverLatestOHLC["tickVolume"],
          realVolume: serverLatestOHLC["realVolume"],
          timestamp: ohlcLatestDate!,
          meta: {
            symbol: candleStickStore.symbol
          }
        },
        ...candleStickStore.ohlc
      ];
    } else if (serverLatestTime === lastOHLCTime) {
      chartData[lastIndex][1] = serverLatestOHLC["open"];
      chartData[lastIndex][2] = serverLatestOHLC["high"];
      chartData[lastIndex][3] = serverLatestOHLC["low"];
      chartData[lastIndex][4] = serverLatestOHLC["close"];
      chartData[lastIndex][5] = serverLatestOHLC["tickVolume"];

      candleStickStore.ohlc[0] = {
        open: serverLatestOHLC["open"],
        high: serverLatestOHLC["high"],
        low: serverLatestOHLC["low"],
        close: serverLatestOHLC["close"],
        tickVolume: serverLatestOHLC["tickVolume"],
        realVolume: serverLatestOHLC["realVolume"],
        timestamp: ohlcLatestDate!,
        meta: {
          symbol: candleStickStore.symbol
        }
      };
    }

    chartStore.chart?.update("data");
  });
}

export function closeOHLCSocketConnection() {
  ohlcSocket?.disconnect();
}
