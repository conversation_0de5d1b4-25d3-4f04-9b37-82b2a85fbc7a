# Tradeawaay Refactor Roadmap

This roadmap gives a high-level overview of the tasks that needs to complete before moving on to Phase 2 of the project.

## Developer Experience

- Add ESLint to project for strict coding practices
- Manage library import order rule (via Prettier or some extension)
- ~~Enforce semicolons for code~~
- Update project dependencies
- Support auto intellisense for custom functions (e.g. helper functions)
- Add CI/CD support for both staging and production servers.
- Change project name to Tradeawaay - Frontend.
- Create new branch name `ui`
- Use `kebab-case` for files except vue components

## Chart Library

- Extend TypeScript Support
- Design APIs to be exposed for frontend
- Optimize code for calculating chart labels (place new order and open trade).
-

## Frontend

- Rename Vue component file with proper name (if possible)
- Optimize interfaces in `types.ts` file (e.g. removing `I` suffix)
- Completely remove null assertion operator `!` from Vue code.
- Switch validation to modal itself in place new order for better UX.
- Re-define error handling with appropriate message.
- Re-define website colors in style.css with comments.
-
