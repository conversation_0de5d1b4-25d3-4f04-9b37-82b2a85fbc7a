<script setup lang="ts">
defineOptions({
  inheritAttrs: false
});

const modal = defineModel();
</script>

<template>
  <div class="relative">
    <div
      class="pointer-events-none absolute inset-y-0 start-0 flex items-center ps-3"
    >
      <svg
        fill="none"
        aria-hidden="true"
        viewBox="0 0 20 20"
        class="h-4 w-4 text-gray-500"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          stroke-width="2"
          stroke="currentColor"
          stroke-linecap="round"
          stroke-linejoin="round"
          d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
        />
      </svg>
    </div>

    <input
      type="text"
      v-bind="$attrs"
      class="block w-full border-none bg-accent px-3 pb-1.5 ps-10 pt-2 text-sm focus:ring-0"
      v-model="modal"
    />
  </div>
</template>
