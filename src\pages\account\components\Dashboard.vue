<script setup lang="ts">
import { onMounted, ref } from "vue";
import { DateTime } from "luxon";

import { useUserStore } from "@/store/userStore";
import { EUserRole } from "@/types/enums";

import PrimaryButton from "@/components/PrimaryButton.vue";
import { axios } from "@/api";
import { SubscriptionPlan } from "@/types";

const userStore = useUserStore();

const cardHeight = ref(window.innerHeight - 55 - 40);
const subscriptionPlan = ref({
  plan: "",
  start_date: "",
  end_date: ""
});

const isUserCustomer = ref(userStore.user?.roles.includes(EUserRole.CUSTOMER));

onMounted(() => {
  handleSubscriptionPeriod();

  getSubscriptionPlans();
});

function handleSubscriptionPeriod() {
  const periodStart = userStore.user?.payment?.current_period_start;
  const periodEnd = userStore.user?.payment?.current_period_end;

  if (!periodStart || !periodEnd) {
    return;
  }

  const p1 = parseInt(periodStart);
  const p2 = parseInt(periodEnd);

  const subscriptionStart = DateTime.fromSeconds(p1).toFormat("dd MMM, yyyy");
  const subscriptionEnd = DateTime.fromSeconds(p2).toFormat("dd MMM, yyyy");

  subscriptionPlan.value.start_date = subscriptionStart;
  subscriptionPlan.value.end_date = subscriptionEnd;
}

async function getSubscriptionPlans() {
  if (userStore.subscriptionPlans.length !== 0) {
    userStore.subscriptionPlans.forEach((v) => {
      if (v.name === userStore.user?.payment?.subscription_plan) {
        subscriptionPlan.value.plan = `${v.description} (${v.service_name})`;
      }
    });

    return;
  }

  try {
    const resp = await axios.get("/payments/subscription-plan");

    const data: SubscriptionPlan[] = resp.data.data;

    data.forEach((v) => {
      if (v.name === userStore.user?.payment?.subscription_plan) {
        subscriptionPlan.value.plan = `${v.description} (${v.service_name})`;
      }
    });

    userStore.subscriptionPlans = data;
  } catch (e) {
    console.error(e);
  }
}
</script>

<template>
  <div
    class="mx-auto w-10/12 overflow-auto rounded-md border bg-white p-5 text-sm shadow-lg"
    :style="{ height: cardHeight + 'px' }"
  >
    <div class="rounded-md border p-3">
      <div class="flex items-center justify-between">
        <div class="text-lg font-bold">Account Information</div>

        <PrimaryButton class="px-5" @click="$router.push({ name: 'edit' })">
          Edit
        </PrimaryButton>
      </div>

      <div class="mt-1 grid grid-cols-3 gap-y-3">
        <div>
          <div>Name</div>
          <div class="font-semibold">
            {{ userStore.user?.name }}
          </div>
        </div>

        <div>
          <div>Email</div>
          <div class="font-semibold">
            {{ userStore.user?.email }}
          </div>
        </div>

        <div>
          <div>Phone Number</div>
          <div class="font-semibold">
            {{ userStore.user?.phone_number ?? "--" }}
          </div>
        </div>

        <div>
          <div>Gender</div>
          <div class="font-semibold">
            {{ userStore.user?.gender ?? "--" }}
          </div>
        </div>
      </div>
    </div>

    <div class="mt-5 rounded-md border p-3" v-if="isUserCustomer">
      <div class="flex items-center justify-between">
        <div class="text-lg font-bold">Payment Information</div>

        <PrimaryButton
          class="px-5"
          @click="$router.push({ name: 'edit-payment-information' })"
        >
          Edit
        </PrimaryButton>
      </div>

      <div class="mt-1 grid grid-cols-3">
        <div>
          <div>Subscription Type</div>
          <div class="font-semibold">
            {{ subscriptionPlan.plan }}
          </div>
        </div>

        <div>
          <div>Subscription Period</div>

          <div class="flex gap-x-1 font-semibold">
            {{ subscriptionPlan.start_date }}

            <span>-</span>

            {{ subscriptionPlan.end_date }}
          </div>
        </div>

        <div v-if="userStore.user?.payment?.subscription_status === 'ACTIVE'">
          <div>Next Payment Date</div>

          <div class="font-semibold">
            {{ subscriptionPlan.end_date }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
