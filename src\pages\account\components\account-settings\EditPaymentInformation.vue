<script setup lang="ts">
import { onMounted, ref } from "vue";
import { DateTime } from "luxon";

import { axios } from "@/api";
import { useUserStore } from "@/store/userStore";
import { SubscriptionPlan } from "@/types";

import CancelSubscription from "./CancelSubscription.vue";

import Alert from "@/components/Alert.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const userStore = useUserStore();

const isAccountOnTrial = ref(false);
const trialPeriod = ref({
  start_date: "",
  end_date: ""
});
const subscriptionPlan = ref({
  plan: "",
  start_date: "",
  end_date: ""
});
const cancelSubscription = ref(false);
const alertMessages = ref<string[]>([]);

onMounted(() => {
  handleTrialPeriod();

  handleSubscriptionPeriod();

  getSubscriptionPlans();
});

async function getSubscriptionPlans() {
  if (userStore.subscriptionPlans.length !== 0) {
    userStore.subscriptionPlans.forEach((v) => {
      if (v.name === userStore.user?.payment?.subscription_plan) {
        subscriptionPlan.value.plan = `${v.description} (${v.service_name})`;
      }
    });

    return;
  }

  try {
    const resp = await axios.get("/payments/subscription-plan");

    const data: SubscriptionPlan[] = resp.data.data;

    data.forEach((v) => {
      if (v.name === userStore.user?.payment?.subscription_plan) {
        subscriptionPlan.value.plan = `${v.description} (${v.service_name})`;
      }
    });

    userStore.subscriptionPlans = data;
  } catch (e) {
    console.error(e);
  }
}

function handleTrialPeriod() {
  const trialStart = userStore.user?.payment?.trial_start;
  const trialEnd = userStore.user?.payment?.trial_end;

  if (!trialStart || !trialEnd) {
    return;
  }

  const t1 = parseInt(trialStart);
  const t2 = parseInt(trialEnd);

  const trialStartDate = DateTime.fromSeconds(t1).toFormat("dd MMM, yyyy");
  const trialEndDate = DateTime.fromSeconds(t2).toFormat("dd MMM, yyyy");

  trialPeriod.value = {
    start_date: trialStartDate,
    end_date: trialEndDate
  };

  const currentTimestamp = Math.floor(Date.now() / 1000);

  isAccountOnTrial.value = t2 > currentTimestamp;
}

function handleSubscriptionPeriod() {
  const periodStart = userStore.user?.payment?.current_period_start;
  const periodEnd = userStore.user?.payment?.current_period_end;

  if (!periodStart || !periodEnd) {
    return;
  }

  const p1 = parseInt(periodStart);
  const p2 = parseInt(periodEnd);

  const subscriptionStart = DateTime.fromSeconds(p1).toFormat("dd MMM, yyyy");
  const subscriptionEnd = DateTime.fromSeconds(p2).toFormat("dd MMM, yyyy");

  subscriptionPlan.value = {
    start_date: subscriptionStart,
    end_date: subscriptionEnd
  };
}

function handleCancelSubscription(messages: string[]) {
  cancelSubscription.value = false;

  alertMessages.value = messages;
}
</script>

<template>
  <CancelSubscription
    :subscription-end-date="subscriptionPlan.end_date"
    @back="cancelSubscription = false"
    @cancel="handleCancelSubscription"
    v-if="cancelSubscription"
  />

  <div class="px-5 text-sm" v-else>
    <div class="text-lg font-bold">Payment Information</div>

    <Alert
      class="mt-2"
      variant="danger"
      :close-icon="true"
      @close="alertMessages = []"
      v-if="alertMessages.length !== 0"
    >
      <div v-for="message in alertMessages">
        {{ message }}
      </div>
    </Alert>

    <div class="mt-3 rounded-lg border p-3">
      <div class="text-base font-semibold">Credit Card Information</div>

      <div class="mt-1 grid grid-cols-3 gap-y-3">
        <div>
          <div>Account Holder Name</div>

          <div class="font-semibold">
            {{ userStore.user?.name }}
          </div>
        </div>

        <div>
          <div>Card Type</div>

          <div class="font-semibold">
            <template v-if="userStore.user?.payment">
              {{
                userStore.user?.payment.card_type[0].toUpperCase() +
                userStore.user?.payment.card_type.slice(1)
              }}
            </template>
          </div>
        </div>

        <div>
          <div>Email</div>

          <div class="font-semibold">
            {{ userStore.user?.payment?.email }}
          </div>
        </div>

        <div>
          <div>Country</div>

          <div class="font-semibold">
            {{ userStore.user?.payment?.billing_address?.country ?? "--" }}
          </div>
        </div>

        <div>
          <div>City</div>

          <div class="font-semibold">
            {{ userStore.user?.payment?.billing_address?.city ?? "--" }}
          </div>
        </div>

        <div>
          <div>Address</div>

          <div class="font-semibold">
            {{ userStore.user?.payment?.billing_address?.line1 ?? "--" }}
          </div>
        </div>
      </div>
    </div>

    <div class="mt-4 rounded-lg border p-3" v-if="isAccountOnTrial">
      <div class="text-base font-semibold">Trial Information</div>

      <div class="mt-1 grid grid-cols-3">
        <div>
          <div>Trial Status</div>

          <div class="font-semibold">Active</div>
        </div>

        <div>
          <div>Trial Period</div>

          <div class="flex items-center gap-x-1 font-semibold">
            {{ trialPeriod.start_date }}

            <span>-</span>

            {{ trialPeriod.end_date }}
          </div>
        </div>
      </div>
    </div>

    <div class="mt-4 rounded-lg border p-3">
      <div class="text-base font-semibold">Subscription Information</div>

      <div class="mt-1 grid grid-cols-3 gap-y-3">
        <div>
          <div>Subscription Type</div>

          <div class="font-semibold">
            {{ subscriptionPlan.plan }}
          </div>
        </div>

        <div>
          <div>Subscription Status</div>

          <div class="flex items-center gap-x-2 font-semibold">
            {{ userStore.user?.payment?.subscription_status }}

            <div
              class="h-1.5 w-1.5 rounded-full bg-green-400"
              v-if="userStore.user?.payment?.subscription_status === 'ACTIVE'"
            ></div>
          </div>
        </div>

        <template
          v-if="userStore.user?.payment?.subscription_status === 'ACTIVE'"
        >
          <div>
            <div>Subscription Period</div>

            <div class="flex gap-x-1 font-semibold">
              {{ subscriptionPlan.start_date }}

              <span>-</span>

              {{ subscriptionPlan.end_date }}
            </div>
          </div>

          <div>
            <div>Next Payment Date</div>

            <div class="font-semibold">
              {{ subscriptionPlan.end_date }}
            </div>
          </div>
        </template>

        <div v-else>
          <div>End Date</div>

          <div class="font-semibold">
            {{
              DateTime.fromSeconds(
                parseInt(userStore.user?.payment?.cancel_effective_date!)
              ).toFormat("dd MMM, yyyy")
            }}
          </div>
        </div>
      </div>
    </div>

    <div
      class="mt-4 flex items-center justify-between rounded-lg bg-gray-200 px-3 py-5"
      v-if="userStore.user?.payment?.subscription_status === 'ACTIVE'"
    >
      <div class="font-medium">Want to cancel your subscription?</div>

      <div>
        <PrimaryButton
          class="!bg-red-600 hover:!bg-red-700"
          @click="cancelSubscription = true"
        >
          Cancel Subscription
        </PrimaryButton>
      </div>
    </div>
  </div>
</template>
