<script setup lang="ts">
import { onMounted } from "vue";
import { Drawer, DrawerInterface, DrawerOptions, initFlowbite } from "flowbite";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { useUserStore } from "@/store/userStore";

import CompanyLogoSVG from "@/assets/logo.svg";
import CompanyLogoWhiteSVG from "@/assets/logo_white.svg";

const userStore = useUserStore();

let navDrawer: DrawerInterface;

onMounted(() => {
  initFlowbite();

  const $targetEl = document.getElementById("navigation-drawer");

  const options: DrawerOptions = {
    placement: "right",
    backdrop: true,
    bodyScrolling: false,
    edge: false,
    backdropClasses: "bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-30"
  };

  navDrawer = new Drawer($targetEl, options);
});
</script>

<template>
  <nav id="appbar" class="bg-tertiary py-5 text-white">
    <div
      class="mx-auto flex max-w-screen-2xl items-center justify-between px-5"
    >
      <router-link :to="{ name: 'home' }" class="flex items-center gap-x-2">
        <CompanyLogoWhiteSVG class="size-12" />

        <span class="text-3xl font-semibold">Tradeawaay</span>
      </router-link>

      <div class="text-md hidden items-center gap-x-4 lg:flex lg:gap-x-6">
        <router-link
          :to="{ name: 'home', hash: '#home' }"
          class="underline-offset-8 hover:underline"
        >
          <span class="font-semibold">Home</span>
        </router-link>

        <router-link
          :to="{ name: 'home', hash: '#features' }"
          class="underline-offset-8 hover:underline"
        >
          <span class="font-semibold">Features</span>
        </router-link>

        <router-link
          :to="{ name: 'home', hash: '#pricing' }"
          class="underline-offset-8 hover:underline"
        >
          <span class="font-semibold">Pricing</span>
        </router-link>

        <router-link
          :to="{ name: 'home', hash: '#faq' }"
          class="underline-offset-8 hover:underline"
        >
          <span class="font-semibold">FAQ</span>
        </router-link>

        <router-link
          :to="{ name: 'home', hash: '#contact-us' }"
          class="underline-offset-8 hover:underline"
        >
          <span class="font-semibold">Contact Us</span>
        </router-link>

        <router-link
          :to="{ name: 'community-videos' }"
          class="underline-offset-8 hover:underline"
        >
          <span class="font-semibold">Education</span>
        </router-link>

        <router-link
          :to="{ name: 'trading' }"
          class="flex items-center gap-x-2 rounded-lg bg-primary px-5 pb-2.5 pt-3 hover:bg-secondary"
          v-if="userStore.user"
        >
          Trading Console
          <FontAwesomeIcon icon="fa-solid fa-arrow-right" />
        </router-link>

        <router-link :to="{ name: 'login' }" v-else>
          <button
            class="group flex cursor-pointer items-center space-x-2 rounded-lg bg-blue-700 px-6 py-3 text-white transition-all hover:bg-blue-800"
          >
            <span>Log In</span>
          </button>
        </router-link>
      </div>

      <div
        class="px-2 py-1 lg:hidden"
        aria-controls="navigation-drawer"
        @click="navDrawer.show()"
      >
        <button type="button">
          <FontAwesomeIcon class="size-7" icon="fa-solid fa-bars" />
        </button>
      </div>

      <div
        tabindex="-1"
        id="navigation-drawer"
        aria-labelledby="navigation-drawer"
        class="fixed right-0 top-0 z-40 flex h-screen w-80 translate-x-full flex-col overflow-y-auto bg-white text-black transition-transform"
      >
        <div class="flex items-center justify-between px-4 pt-4">
          <div class="flex items-center gap-x-1">
            <CompanyLogoSVG />

            <h1 class="text-2xl font-bold">Tradewaay</h1>
          </div>

          <button
            type="button"
            class="mt-1.5"
            aria-controls="drawer-close"
            @click="navDrawer.hide()"
          >
            <FontAwesomeIcon size="xl" icon="fa-solid fa-xmark" />

            <span class="sr-only">Close Drawer</span>
          </button>
        </div>

        <div class="grow">
          <router-link
            :to="{ name: 'home', hash: '#home' }"
            class="flex items-center justify-between border-b p-4 pr-5"
            @click="navDrawer.hide()"
          >
            <span class="font-medium">Home</span>

            <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
          </router-link>

          <router-link
            :to="{ name: 'home', hash: '#features' }"
            class="flex items-center justify-between border-b p-4 pr-5"
            @click="navDrawer.hide()"
          >
            <span class="font-medium">Features</span>

            <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
          </router-link>

          <router-link
            :to="{ name: 'home', hash: '#pricing' }"
            class="flex items-center justify-between border-b p-4 pr-5"
            @click="navDrawer.hide()"
          >
            <span class="font-medium">Pricing</span>

            <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
          </router-link>

          <router-link
            :to="{ name: 'home', hash: '#contact-us' }"
            class="flex items-center justify-between border-b p-4 pr-5"
            @click="navDrawer.hide()"
          >
            <span class="font-medium">Contact Us</span>

            <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
          </router-link>

          <router-link
            :to="{ name: 'home', hash: '#faq' }"
            class="flex items-center justify-between border-b p-4 pr-5"
            @click="navDrawer.hide()"
          >
            <span class="font-medium">FAQ</span>

            <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
          </router-link>

          <router-link
            :to="{ name: 'community-videos' }"
            class="flex items-center justify-between border-b p-4 pr-5"
            @click="navDrawer.hide()"
          >
            <span class="font-medium">Education</span>

            <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
          </router-link>
        </div>

        <div class="pb-2">
          <p class="text-center text-sm">
            © {{ new Date().getFullYear() }} Tradeawaay
          </p>
        </div>
      </div>
    </div>
  </nav>
</template>
