<script setup lang='ts'>
import {ref,nextTick} from "vue"
const emit = defineEmits(["send-message"]);
import { SendHorizontal } from "lucide-vue-next";
const messageText = ref("");
const textarea = ref<HTMLTextAreaElement>();

const sendMessage = () => {
  if (messageText.value.trim()) {
    emit("send-message", messageText.value);
    messageText.value = "";
    resetTextareaHeight();
  }
}
const handleInput = (e: Event) => {
  const inputValue = (e.target as HTMLTextAreaElement).value;
  messageText.value = inputValue;
  autoResize();
  
};
const resetTextareaHeight = async () => {
  await nextTick();
  if (textarea.value) {
    textarea.value.style.height = "auto";
  }
};
const autoResize = async () => {
  await nextTick();
  if (textarea.value) {
    textarea.value.style.height = "auto";
    textarea.value.style.height =
      Math.min(textarea.value.scrollHeight, 120) + "px";
  }
};
const handleKeyDown = (e: KeyboardEvent) => {
  
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault();
    sendMessage();
  }
};
</script>
<template>
  <div class="bg-white p-1">
    <div class="flex items-center gap-3">
      <div class="flex-1 relative">
        <textarea
          :value="messageText"
          @input= "(e:Event)=>handleInput(e)"
          placeholder="Say Something.."
          rows="1"
          class="w-full px-4 py-3 bg-gray-50 border-1 border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:bg-white transition-all duration-200 placeholder-gray-500 resize-none overflow-y-auto"
          @keydown="handleKeyDown"
          ref="textarea"
        ></textarea>
        
        <!-- User search results dropdown (for @ button) -->
      </div>
      <!-- Send Button -->
      <button
        @click="sendMessage"
        :disabled="!messageText.trim()"
        class="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200"
        aria-label="Send message"
      >
        <SendHorizontal />
      </button>
    </div>

    <!-- Bottom Row - Action Buttons and Mode Toggle -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <!-- Emoji Button -->
        <button
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
          aria-label="Add emoji"
        >
          <Smile class="size-5" />
        </button>

        <!-- Camera Button -->
        <button
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
          aria-label="Take photo"
        >
          <Camera class="size-5" />
        </button>
      </div>
      
      
    </div>
  </div>
</template>

