<script setup lang="ts">
import { useRouter } from "vue-router";

import { useUserStore } from "@/store/userStore";

const router = useRouter();

const userStore = useUserStore();

async function logOut() {
  try {
    await userStore.logOut();

    router.push({
      name: "login"
    });
  } catch (e) {
    console.error(e);
  }
}
</script>

<template>
  <nav class="border-b shadow-lg">
    <div class="flex justify-center gap-x-8 py-3 font-medium">
      <router-link :to="{ name: 'trading' }">Trading Console</router-link>

      <router-link :to="{ name: 'how-to-setup' }">Setup</router-link>

      <router-link :to="{ name: 'dashboard' }">Dashboard</router-link>

      <router-link :to="{ name: 'settings' }">Settings</router-link>

      <router-link :to="{ name: 'faq' }">FAQ</router-link>

      <router-link :to="{ name: 'contact-us' }">Contact Us</router-link>

      <button type="button" @click="logOut" class="mb-1">Log Out</button>
    </div>
  </nav>

  <main class="grow bg-gray-100 py-5">
    <div class="container mx-auto">
      <router-view></router-view>
    </div>
  </main>
</template>

<style>
#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
</style>

<style scoped>
.router-link-active {
  padding-bottom: 4px;
  border-bottom: 2px solid #1a56db;
}
</style>
