<script setup lang="ts">
defineOptions({
  inheritAttrs: false
});

defineProps<{
  error?: string;
}>();

const model = defineModel<string>();
</script>

<template>
  <textarea
    rows="5"
    class="block w-full resize-none rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
    :class="{ '!border-danger focus:ring-red-500': error }"
    v-bind="$attrs"
    v-model="model"
  />

  <div class="mt-1 px-1 text-xs text-danger" v-if="error">
    {{ error }}
  </div>
</template>
