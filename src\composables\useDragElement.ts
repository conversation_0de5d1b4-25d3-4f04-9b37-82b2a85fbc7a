import { onMounted, onUnmounted, ref } from "vue";

export function useDragElement(elementId: string, draggableElementId: string) {
  const top = ref(0);
  const left = ref(0);

  let mainEl: HTMLElement | null;
  let draggableEl: HTMLElement | null;

  let pos1 = 0;
  let pos2 = 0;
  let pos3 = 0;
  let pos4 = 0;

  onMounted(() => {
    mainEl = document.getElementById(elementId);
    draggableEl = document.getElementById(draggableElementId);

    draggableEl?.addEventListener("mousedown", dragMouseDown);
  });

  onUnmounted(() => {
    draggableEl?.removeEventListener("mousedown", dragMouseDown);
  });

  const dragMouseDown = (e: MouseEvent) => {
    e.preventDefault();

    pos3 = e.clientX;
    pos4 = e.clientY;

    document.addEventListener("mouseup", closeDragElement);
    document.addEventListener("mousemove", elementDrag);
  };

  const elementDrag = (e: MouseEvent) => {
    e.preventDefault();

    pos1 = pos3 - e.clientX;
    pos2 = pos4 - e.clientY;
    pos3 = e.clientX;
    pos4 = e.clientY;

    const newPosTop = mainEl!.offsetTop - pos2;
    const newPosLeft = mainEl!.offsetLeft - pos1;

    top.value = newPosTop;
    left.value = newPosLeft;

    mainEl!.style.top = newPosTop + "px";
    mainEl!.style.left = newPosLeft + "px";
  };

  const closeDragElement = () => {
    document.removeEventListener("mouseup", closeDragElement);
    document.removeEventListener("mousemove", elementDrag);
  };

  return {
    top,
    left
  };
}
