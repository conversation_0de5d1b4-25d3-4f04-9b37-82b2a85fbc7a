import { AvailableShapes } from "../../groups/ShapeTool";
import { TCoreData } from "../../types/index";
import { IPoint } from "../shapes/Point";
export type BasicPoint = { x: number; y: number };

interface PointStyles {
  show: boolean;
  stroke: string;
  fill: string;
  roundRect: boolean;
  width: number;
}

interface LineStyles {
  stroke: string;
  width: number;
  dash?: number[];
}

interface SidebarStyles {
  show: boolean;
  fill: string;
  text: string;
}

interface BotbarStyles {
  show: boolean;
  fill: string;
  text: string;
}

export interface BaseShapeInterface {
  draggingPoint: BasicPoint | null;
  dragging: boolean;
  points: IPoint[];
  selected: boolean;
  hovered: boolean;
  onDragStart: () => void;
  $core: TCoreData;
}

export type TLineType = "normal" | "dashed" | "dotted";

/**
 * All available shape properties.
 */
export type TShapeProperties = {
  "line-color": string;
  "line-width": number;
  "line-type": TLineType;
  "area-color": string;
  "point-color": string;
  "point-radius": number;
  font: string;
  "font-size": number;
  "font-color": string;
};

// Expose API for frontend
export interface BaseLineInterface {
  /**
   * A unique identifier for the shape.
   */
  uuid: string;
  /**
   * Name (alias) for a shape.
   */
  name: string;
  /**
   * The type of shape selected.
   */
  type: AvailableShapes;

  $core: TCoreData;
  /**
   * Sets a name (alias) for the selected shape.
   */
  setName: (name: string) => void;

  onSelect: (line: BaseLineInterface) => void;
  /**
   * Draw shape on the canvas.
   */
  draw(ctx: CanvasRenderingContext2D): void;
  /**
   * Draw details on the shape instance
   */
  drawDetails?(ctx: CanvasRenderingContext2D): void;
  /**
   * Draw on the sidebar
   */
  drawSidebar(
    ctx: CanvasRenderingContext2D,
    _: any,
    scale: { prec: number }
  ): void;
  /**
   * Draw on the bottombar
   */
  drawBotbar(ctx: CanvasRenderingContext2D): void;

  drawLine: (
    ctx: CanvasRenderingContext2D,
    p1: BasicPoint,
    p2: BasicPoint
  ) => void;

  drawEndPoints: (ctx: CanvasRenderingContext2D) => void;
  drawMidPoint: (ctx: CanvasRenderingContext2D) => void;
  /**
   * Extract the x,y coordinates and styles from the shape to store as JSON
   */
  toJSON(): { [x: string]: any };
  /**
   * Get coordinates to change the shape from external sources. Use 'setCoordinate' to change data from external
   */
  detailsInfo: { [x: string]: boolean };
  /**
   * Show Label Details or not.
   */
  readonly showLabelDetails: boolean;
  toggleDetails: (show?: boolean) => boolean;
  getLabelProperties(): { [x: string]: boolean };
  setLabelProperty(name: string, value: boolean): boolean;
  getCoordinates(): { [x: string]: any };
  setCoordinates(name: string, value: any): boolean;
  readonly isValid: boolean;
  readonly screenPoints: BasicPoint[];
  readonly midPoint: BasicPoint;
  dragging: boolean;
  locked: boolean;
  points: IPoint[];
  selected: boolean;
  hovered: boolean;
  endPointStyles: PointStyles;
  midPointStyles: PointStyles;
  lineStyles: LineStyles;
  sidebarStyles?: SidebarStyles;
  botbarStyles?: BotbarStyles;
  /**
   * Returns shape properties for the selected shape.
   */
  // properties: Partial<TShapeProperties>
  /**
   * Sets the property for the selected shape.
   * @param name - property name
   * @param value - value to be set
   * @returns A boolean to indicate if set property was success or not.
   */
  setProperty: <F extends keyof TShapeProperties>(
    name: F,
    value: TShapeProperties[F]
  ) => boolean;
}

export interface LineInterface extends BaseLineInterface {
  dragState: "tracking" | "dragging" | "settled" | "tracking-x" | "tracking-y";

  mousedown(event: MouseEvent): void;
  mousemove(event: MouseEvent): void;
  mouseover(event: MouseEvent): void;
  mouseup(event: MouseEvent): void;
  keydown?(event: KeyboardEvent): void;
}
