import { rgbaToHex } from "@/helpers/colorConversion";
import { BasicPoint } from "../base/Types";
import { AvailableShapes } from "../../groups/ShapeTool";

function getLuminance(clr: string): number {
  const color = clr.startsWith("rgb") ? rgbaToHex(clr) : clr;
  // Ensure the color string starts with '#' and is followed by exactly 6 hexadecimal characters
  const hex = color.replace(/#/, "").substring(0, 6);
  if (hex.length !== 6) {
    throw new Error(
      "Invalid color format. Please provide a 6-character hex string."
    );
  }

  // Split the hex string into three parts (R, G, B) and convert to RGB values
  const rgb = hex.match(/.{1,2}/g);
  if (!rgb) {
    throw new Error("Error parsing hex string.");
  }

  // Convert hex to decimal and then to the luminance component
  const luminanceComponents = rgb.map((component) => {
    let decimal = parseInt(component, 16) / 255;
    return decimal <= 0.03928
      ? decimal / 12.92
      : ((decimal + 0.055) / 1.055) ** 2.4;
  });

  return (
    0.2126 * luminanceComponents[0] +
    0.7152 * luminanceComponents[1] +
    0.0722 * luminanceComponents[2]
  );
}

function chooseForeground(bkg: string): string {
  let relativeLuminance = getLuminance(bkg);
  let chooseBlack = (relativeLuminance + 0.05) / 0.05;
  let chooseWhite = 1.05 / (relativeLuminance + 0.05);
  return chooseBlack > chooseWhite ? "#000000" : "#ffffff";
}

/**
 * Draw Arrow Head at a point
 * @param ctx Canvas
 * @param p1 Point at which arrow head should be drawn
 * @param angle angle in degrees
 */
function drawArrowHead(
  ctx: CanvasRenderingContext2D,
  p1: BasicPoint,
  angle: number,
  width: number
) {
  const height = width;
  const arrowPath = [
    [-width, -height / 2],
    [0, 0],
    [-width, height / 2]
  ];
  ctx.save();
  ctx.translate(p1.x, p1.y);
  ctx.rotate(angle);

  ctx.beginPath();
  // Draw lines to each subsequent point
  for (let i = 0; i < arrowPath.length; i++) {
    ctx.lineTo(arrowPath[i][0], arrowPath[i][1]);
  }
  ctx.closePath();

  // Fill the arrow (you can use stroke() instead if you want an outline)
  ctx.fill();

  ctx.beginPath();
  const lineLength = width;
  ctx.rotate(Math.PI / 2);
  ctx.moveTo(-lineLength / 2, 0);
  ctx.lineTo(lineLength / 2, 0);
  ctx.stroke();

  ctx.restore();
}
export class DrawUtils2 {
  static pointStyles = {
    stroke: "#ffffff",
    fill: "#363556",
    roundRect: false,
    width: 3
  };
  static lineStyles = { stroke: "#ffffff", width: 2, dash: [1, 0] };
  static sidebarStyles = { fill: "#ffffff", text: "#000" };
  static botbarStyles = { fill: "#ffffff", text: "#000" };

  static drawCirclePoint(
    ctx: CanvasRenderingContext2D,
    p: BasicPoint,
    width: number,
    fillColor?: string
  ) {
    if (fillColor) {
      ctx.fillStyle = fillColor;
    }

    ctx.beginPath();
    ctx.arc(p.x, p.y, width / 2, 0, Math.PI * 2, true);
    ctx.stroke();

    if (fillColor) {
      ctx.fill();
    }

    ctx.closePath();
  }

  static drawSquarePoint(
    ctx: CanvasRenderingContext2D,
    p: BasicPoint,
    width: number
  ) {
    const x = p.x;
    const y = p.y;
    const halfWidth = width / 2;
    const borderRadius = width / 8;
    const rectX = x - halfWidth; // Adjust x to center the rectangle
    const rectY = y - halfWidth; // Adjust y to center the rectangle

    ctx.beginPath();
    ctx.moveTo(rectX + borderRadius, rectY);
    ctx.arcTo(rectX + width, rectY, rectX + width, rectY + width, borderRadius);
    ctx.arcTo(rectX + width, rectY + width, rectX, rectY + width, borderRadius);
    ctx.arcTo(rectX, rectY + width, rectX, rectY, borderRadius);
    ctx.arcTo(rectX, rectY, rectX + borderRadius, rectY, borderRadius);
    ctx.closePath();
    ctx.stroke();
  }
  /**
   * Draw point (circle or round rect depending on pointStyle)
   */
  static drawPoint(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    styles = this.pointStyles
  ) {
    ctx.strokeStyle = styles.stroke;
    ctx.fillStyle = styles.fill;
    ctx.beginPath();
    const halfWidth = styles.width / 2;

    if (styles.roundRect) {
      const borderRadius = halfWidth / 4;
      const rectX = x - halfWidth; // Adjust x to center the rectangle
      const rectY = y - halfWidth; // Adjust y to center the rectangle
      ctx.moveTo(rectX + borderRadius, rectY);
      ctx.arcTo(
        rectX + styles.width,
        rectY,
        rectX + styles.width,
        rectY + styles.width,
        borderRadius
      );
      ctx.arcTo(
        rectX + styles.width,
        rectY + styles.width,
        rectX,
        rectY + styles.width,
        borderRadius
      );
      ctx.arcTo(rectX, rectY + styles.width, rectX, rectY, borderRadius);
      ctx.arcTo(rectX, rectY, rectX + borderRadius, rectY, borderRadius);
      ctx.closePath();
    } else {
      ctx.arc(x, y, halfWidth, 0, Math.PI * 2, true);
    }
    ctx.fill();
    ctx.stroke();
    ctx.closePath();
  }
  static drawDetails(
    ctx: CanvasRenderingContext2D,
    position: BasicPoint,
    details: string[],
    _styles = this.botbarStyles
  ) {
    const styles = { fill: "#ffffff", text: "#000" };
    ctx.beginPath();
    ctx.font = "14px Roboto";
    const textHeight = 14;
    const [paddingX, paddingY] = [15, 15];
    // height is 30 since top bottom padding 15px is applied

    let [height, width] = [0, 0];
    details.forEach((d) => {
      const m = ctx.measureText(d);
      width = width < m.width ? m.width : width;
      // font height + padding between lines
      height += 14 + 4;
    });
    position.x -= width / 2 + paddingX;
    position.y += height / 2 + paddingY;
    // padding is applied
    width = width === 0 ? 0 : width + paddingX * 2;
    height = height === 0 ? 0 : height + paddingY * 2;

    // Box
    ctx.fillStyle = styles.fill + "bb";
    ctx.roundRect(position.x, position.y, width, -height, 5);
    ctx.fill();

    // Text
    ctx.fillStyle = styles.text;
    let currentY = position.y - height + paddingY + textHeight;
    const inc: number = 18;
    details.forEach((text) => {
      // x has +15 since padding, maxWidth has -30 since padding is removed here
      ctx.fillText(text, position.x + 15, currentY, width - 30);
      currentY += inc;
    });
  }
  static drawArrow(
    ctx: CanvasRenderingContext2D,
    p1: { x: number; y: number },
    p2: { x: number; y: number },
    width: number,
    shapeType?: AvailableShapes,
    skipArrowHead?: boolean
  ) {
    // End line ⊥
    let angle = Math.atan2(p2.y - p1.y, p2.x - p1.x);

    if (shapeType === "price-range" && angle === 0) {
      angle = 90 * (Math.PI / 180);
    }

    ctx.save();
    ctx.translate(p1.x, p1.y);
    ctx.rotate(angle);

    ctx.beginPath();
    // Draw lines to each subsequent point
    ctx.moveTo(0, width / 2);
    ctx.lineTo(0, -width / 2);
    ctx.closePath();
    ctx.stroke();
    ctx.restore();

    ctx.save();
    ctx.fillStyle = ctx.strokeStyle;
    if (skipArrowHead) {
      ctx.restore();
      return;
    }
    drawArrowHead(ctx, p2, angle, width);
    ctx.restore();
  }

  static getLineDash(dash: "dotted" | "solid" | "dashed") {
    switch (dash) {
      case "solid":
        return [];
      case "dashed":
        return [6, 4];
      case "dotted":
        return [2, 4];
    }
    return [];
  }
  /**
   * Draw line on canvas
   */
  static drawLine(
    ctx: CanvasRenderingContext2D,
    p1: { x: number; y: number },
    p2: { x: number; y: number }
  ) {
    ctx.beginPath();
    ctx.moveTo(p1.x, p1.y);
    ctx.lineTo(p2.x, p2.y);
    ctx.stroke();
    ctx.setLineDash([]);
  }
  /**
   * Draw rectangle on canvas
   */
  static drawRect(
    ctx: CanvasRenderingContext2D,
    p1: { x: number; y: number },
    size: { w: number; h: number },
    styles = this.lineStyles
  ) {
    ctx.strokeStyle = styles.stroke;
    ctx.lineWidth = styles.width;
    ctx.strokeRect(p1.x, p1.y, size.w, size.h);
  }
  /**
   * Draw pill shape label on sidebar
   */
  static drawSidebar(ctx: CanvasRenderingContext2D, label: string, y: number) {
    ctx.beginPath();
    const sidebarW = ctx.canvas.clientWidth || ctx.canvas.width;
    const pillH = 18;
    const rectRad = [0, 4, 4, 0];
    ctx.roundRect(1, y - pillH / 2, sidebarW - 5, pillH, rectRad);
    ctx.fill();
    ctx.closePath();

    ctx.fillStyle = chooseForeground(ctx.fillStyle as string);
    ctx.fillText(label, 7, y + 4);
  }
  /**
   * Draw pill shape label on bottom-bar
   */
  static drawBotbar(ctx: CanvasRenderingContext2D, label: string, x: number) {
    ctx.beginPath();
    const sidebarH = ctx.canvas.clientHeight || ctx.canvas.height;
    const pillW = ctx.measureText(label).width + 18;
    const rectRad = [0, 0, 4, 4];
    ctx.roundRect(x - pillW / 2, 1, pillW, sidebarH - 6, rectRad);
    ctx.fill();
    ctx.closePath();

    ctx.fillStyle = chooseForeground(ctx.fillStyle as string);
    ctx.fillText(label, x, 16);
  }
}
