import { TCoreData } from "../types";

interface NavyDefaultsInterface {
  lib: TCoreData["props"]["config"]["lib"];
  $events: TCoreData["hub"]["events"];
  $props: TCoreData["props"];
  propagate(...data: any): void;
  draw(...data: any): void;
  drawBotbar(...data: any): void;
  drawSidebar(...data: any): void;
  keydown(...data: any): void;
  keypress(...data: any): void;
  keyup(...data: any): void;
  mouseup(...data: any): void;
  mousedown(...data: any): void;
  mousemove(...data: any): void;
  mouseout(...data: any): void;
  mouseover(...data: any): void;
  click(...data: any): void;
  legend(...data: any): void;
  meta(...data: any): void;
  dataFormat(...data: any): void;
  yRange(...data: any): void;
  preSampler(...data: any): void;
  legendHtml(...data: any): void;
  valueTracker(...data: any): void;
  ohlc(...data: any): void;
}

export class NavyDefaults implements NavyDefaultsInterface {
  protected $core: TCoreData;
  lib: TCoreData["props"]["config"]["lib"];
  $events: TCoreData["hub"]["events"];
  $props: TCoreData["props"];

  constructor(env: any) {
    this.$core = env.$core;
    this.lib = env.lib;
    this.$events = env.$core.hub.events;
    this.$props = env.$props;
    this.$events.emit("shape-tool-created", this);
  }
  propagate(..._data: any) {
    // PLACEHOLDER ONLY
    return;
  }
  draw(...data: any) {
    this.propagate("draw", data);
  }
  drawBotbar(...data: any) {
    this.propagate("drawBotbar", data);
  }
  drawSidebar(...data: any) {
    this.propagate("drawSidebar", data);
  }
  keydown(...data: any) {
    this.propagate("keydown", data);
  }
  keypress(...data: any) {
    this.propagate("keypress", data);
  }
  keyup(...data: any) {
    this.propagate("keyup", data);
  }
  mouseup(...data: any) {
    this.propagate("mouseup", data);
  }
  mousedown(...data: any) {
    this.propagate("mousedown", data);
  }
  mousemove(...data: any) {
    this.propagate("mousemove", data);
    this.propagate("mouseover", data);
  }
  mouseout(...data: any) {
    this.propagate("mouseout", data);
  }
  mouseover(...data: any) {
    this.propagate("mouseover", data);
  }
  click(...data: any) {
    this.propagate("click", data);
  }
  legend(...data: any) {
    this.propagate("legend", data);
  }
  meta(...data: any) {
    this.propagate("meta", data);
  }
  dataFormat(...data: any) {
    this.propagate("dataFormat", data);
  }
  yRange(...data: any) {
    this.propagate("yRange", data);
  }
  preSampler(...data: any) {
    this.propagate("preSampler", data);
  }
  legendHtml(...data: any) {
    this.propagate("legendHtml", data);
  }
  valueTracker(...data: any) {
    this.propagate("valueTracker", data);
  }
  ohlc(...data: any) {
    this.propagate("ohlc", data);
  }
}
