<script setup lang="ts">
import { ref, watch } from "vue";

import { hexToRGBA, rgbaToHex } from "@/helpers/colorConversion";
import { COLORS } from "@/utilities/constants";

import InputText from "./InputText.vue";

const props = defineProps<{
  color: string;
}>();

const emit = defineEmits<{
  updateColor: [color: string];
}>();

const colorList = ref(COLORS);
const selectedColor = ref(props.color);
const selectedOpacity = ref(100);

watch(
  () => props.color,
  (newColor) => {
    selectedColor.value = newColor;

    if (newColor === "") {
      return;
    }

    convertShapeColor();
  },
  {
    immediate: true
  }
);

function convertShapeColor() {
  if (selectedColor.value.length === 7) {
    return;
  }

  const alphaHex = selectedColor.value.substring(7, 9);
  const alphaDecimal = parseInt(alphaHex, 16);
  const normalizedAlpha = (alphaDecimal / 255).toFixed(2);

  selectedColor.value = selectedColor.value.substring(0, 7);
  selectedOpacity.value = Math.floor(parseFloat(normalizedAlpha) * 100);
}

function handleColor(color: string) {
  selectedColor.value = color;

  if (selectedOpacity.value !== 100) {
    handleColorWithOpacity();
    return;
  }

  emit("updateColor", color);
}

function handleColorWithOpacity() {
  const rgba = hexToRGBA(selectedColor.value, selectedOpacity.value);
  const hex = rgbaToHex(rgba);

  emit("updateColor", hex);
}
</script>

<template>
  <div class="p-2">
    <div class="grid grid-cols-10">
      <div
        class="rounded border-2 border-transparent p-0.5"
        :class="{ '!border-selected': '#ffffff' === selectedColor }"
      >
        <div
          class="h-4 w-4 cursor-pointer border"
          @click="handleColor('#ffffff')"
        ></div>
      </div>

      <div
        class="rounded border-2 border-transparent p-0.5"
        :class="{ '!border-selected': color === selectedColor }"
        v-for="color in colorList"
        :key="color"
      >
        <div
          class="h-4 w-4 cursor-pointer"
          :style="{ background: color }"
          @click="handleColor(color)"
        ></div>
      </div>
    </div>

    <div class="mt-3">Opacity</div>

    <div class="flex items-center gap-x-3">
      <div class="grow">
        <input
          min="0"
          max="100"
          step="1"
          type="range"
          class="h-2 w-full cursor-pointer appearance-none rounded-lg bg-gray-200"
          v-model.number="selectedOpacity"
          @input="handleColorWithOpacity"
        />
      </div>

      <div
        class="flex w-[66px] items-center rounded-md border bg-gray-50 pr-2 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500"
      >
        <InputText
          type="text"
          class="border-0 focus:ring-0"
          v-model.number="selectedOpacity"
          @input="handleColorWithOpacity"
        />
        %
      </div>
    </div>
  </div>
</template>
