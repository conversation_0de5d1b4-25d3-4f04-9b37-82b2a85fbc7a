<script setup lang="ts">
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";

import { useUserStore } from "@/store/userStore";

import AccountSVG from "@/assets/svg/account.svg";
import LogOutSVG from "@/assets/svg/logout.svg";

import Dropdown from "@/components/Dropdown.vue";

const route = useRoute();
const router = useRouter();

const userStore = useUserStore();
console.log("user",userStore.user)
const userDropdown = ref(false);

async function logOut() {
  try {
    await userStore.logOut();

    if (route.name === "home") {
      return;
    }

    router.push({ name: "login" });
  } catch (e) {
    console.error(e);
  }
}
</script>

<template>
  <Dropdown
    class="h-9 w-9 rounded-full bg-gray-300 !p-0 hover:bg-gray-300 hover:shadow-xl"
    id="user-profile-dropdown"
    toggle-id="user-profile-toggle-dropdown"
    :icon="false"
    :offset-distance="4"
    :offset-skidding="-103"
    :class="{ 'border-2 !border-gray-400': userDropdown }"
    @show="userDropdown = true"
    @hide="userDropdown = false"
  >
    <template #text>
      <img 
                  v-if="userStore.user?.profile_picture" 
                  :src="userStore.user.profile_picture" 
                  class="w-full h-full object-cover rounded-full"
                  alt="Profile picture"
                />
                <template v-else>
                  {{ userStore.user?.name.split(" ")[0].slice(0,1) }}{{ userStore.user?.name.split(" ")[1].slice(0,1).toUpperCase() }}
                </template>
    </template>

    <template #content>
      <div class="mb-1 mt-4 w-60">
        <div class="flex justify-center">
          <div
            class="grid h-10 w-10 place-items-center rounded-full bg-gray-300 text-base"
          >
          <img 
                  v-if="userStore.user?.profile_picture" 
                  :src="userStore.user.profile_picture" 
                  class="w-full h-full object-cover rounded-full"
                  alt="Profile picture"
                />
                <template v-else>
                  {{ userStore.user?.name.split(" ")[0].slice(0,1) }}{{ userStore.user?.name.split(" ")[1].slice(0,1).toUpperCase() }}
                </template>
          </div>
        </div>

        <div class="mt-3 text-center font-medium">
          {{ userStore.user?.name }}
        </div>

        <div class="text-center text-xs text-gray-500">
          {{ userStore.user?.email }}
        </div>

        <div class="mt-2 divide-y">
          <router-link
            target="_blank"
            :to="{ name: 'dashboard' }"
            class="inline-flex w-full cursor-default items-center px-3 pb-1.5 pt-2 hover:bg-accent"
          >
            <div class="w-7">
              <AccountSVG />
            </div>

            <span>My Account</span>
          </router-link>

          <div
            role="button"
            class="flex w-full cursor-default items-center rounded-none px-3 pb-1.5 pt-2 hover:bg-accent"
            @click="logOut"
          >
            <div class="w-7">
              <LogOutSVG />
            </div>

            <span>Log Out</span>
          </div>
        </div>
      </div>
    </template>
  </Dropdown>
</template>
