<script setup lang="ts">
defineOptions({
  inheritAttrs: false
});

defineProps<{
  error?: string;
}>();

const modal = defineModel<string | number>();
</script>

<template>
  <input
    type="text"
    class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
    :class="{
      '!border-danger focus:ring-red-500': error
    }"
    v-bind="$attrs"
    v-model="modal"
  />

  <span class="mt-1.5 block px-1 text-xs text-danger" v-if="error">
    {{ error }}
  </span>
</template>
