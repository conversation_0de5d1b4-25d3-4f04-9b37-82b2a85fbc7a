<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref } from "vue";

import { toast } from "vue3-toastify";

import { ShapeTool } from "@/lib/night-vision/groups/ShapeTool";
import { NightVisionLibs } from "@/lib/night-vision/libs";
import { NightVisionScripts } from "@/lib/night-vision/scripts";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import ChartControlsBar from "@/pages/trading/components/ChartControlsBar.vue";
import Appbar from "@/pages/trading/components/appbar/Appbar.vue";
import BottomNavContentArea from "@/pages/trading/components/bottom-nav-drawer/BottomNavContentArea.vue";
import BottomNavDrawer from "@/pages/trading/components/bottom-nav-drawer/BottomNavDrawer.vue";
import ChartNavigationToolbar from "@/pages/trading/components/chart/ChartNavigationToolbar.vue";
import ChartSettingsModal from "@/pages/trading/components/chart/chart-modal/ChartSettingsModal.vue";
import ShapeToolbar from "@/pages/trading/components/chart/shape-toolbar/ShapeToolbar.vue";
import LeftNavDrawer from "@/pages/trading/components/left-nav-drawer/LeftNavDrawer.vue";
import RightNavContentArea from "@/pages/trading/components/right-nav-drawer/RightNavContentArea.vue";
import RightNavDrawer from "@/pages/trading/components/right-nav-drawer/RightNavDrawer.vue";

import { useAppbarStore } from "@/store/appbarStore";
import { useBottomNavDrawerStore } from "@/store/bottomNavDrawerStore";
import { useCandleStickStore } from "@/store/candleStickStore";
import { useChartStore } from "@/store/chartStore";
import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";

import { loadMoreOHLCData } from "@/chart-frontend/dataLoader";

import ChartLoader from "@/components/ChartLoader.vue";
import Loader from "@/components/Loader.vue";

import { getTimeZoneOffsetInHours } from "@/helpers/dateConversion";
import { getElementWidthAndHeight } from "@/helpers/elementWidthAndHeight";
import { getMarketClosingTime } from "@/helpers/symbols";

import { loadCandleStickData } from "@/utilities/candle-sticks";

import { NightVision } from "@pipclimber/night-vision";

const chartStore = useChartStore();
const appbarStore = useAppbarStore();
const candleStickStore = useCandleStickStore();
const rightNavDrawerStore = useRightNavDrawerStore();
const bottomNavDrawerStore = useBottomNavDrawerStore();

const loader = ref(true);

let chart: NightVision;
let chartWidth = 0;
let chartHeight = 0;

onMounted(async () => {
  try {
    const { candleStick } = await loadCandleStickData(
      candleStickStore.symbol,
      candleStickStore.interval,
      candleStickStore.symbolBroker
    );

    loader.value = false;

    await nextTick();

    const appbar = getElementWidthAndHeight("app-bar");
    const rightNavDrawer = getElementWidthAndHeight("right-nav-drawer");
    const bottomNavDrawer = getElementWidthAndHeight("bottom-nav-drawer");
    const chartControlsBar = getElementWidthAndHeight("chart-controls-bar");
    const leftNavDrawer = getElementWidthAndHeight("left-nav-drawer");

    chartWidth = window.innerWidth - leftNavDrawer.width - rightNavDrawer.width;

    chartHeight =
      window.innerHeight -
      appbar.height -
      chartControlsBar.height -
      bottomNavDrawer.height;

    // chartStore.chartWidth = chartWidth;
    // chartStore.chartHeight = chartHeight;

    rightNavDrawerStore.rightNavContentAreaHeight =
      chartHeight + chartControlsBar.height;

    document
      .getElementById("chart-container")
      ?.addEventListener("contextmenu", (e) => {
        e.preventDefault();
      });

    const offset = getTimeZoneOffsetInHours(candleStickStore.timezone);

    const { asia, london, newYork, closing } = getMarketClosingTime();

    chart = new NightVision("chart-container", {
      id: "chart",
      width: chartWidth,
      height: chartHeight,
      autoResize: true,
      timezone: offset,
      data: {
        panes: [
          {
            settings: {
              scaleTemplate: [[], ["A", "B"]]
            },
            overlays: [
              {
                name: candleStickStore.symbol,
                type: "Candles",
                main: true,
                data: candleStick,
                settings: {
                  scale: "A"
                },
                props: {
                  currencySymbol: "$",
                  showVolume: candleStickStore.chartSettings.volume
                }
              },
              {
                name: "Market Close Lines",
                type: "MarketCloseLines",
                data: candleStick,
                settings: {},
                props: {
                  lineColor: "#130163",
                  lineWidth: 1,
                  visible: candleStickStore.chartSettings.marketCloseLine,
                  closeHour: closing,
                  nyHour: newYork,
                  nyVisible: candleStickStore.chartSettings.newYorkMarket,
                  asiaHour: asia,
                  asiaVisible: candleStickStore.chartSettings.asiaMarket,
                  londonHour: london,
                  londonVisible: candleStickStore.chartSettings.londonMarket
                }
              }
            ]
          }
        ],
        indexBased: true
      },
      colors: {
        back: "#ffffff",
        grid: "#f1f5f9",
        textLG: "#dc2626",
        llBack: "#ffffff00",
        text: "black"
      },
      config: {
        // @ts-expect-error Night vision libs not properly typed
        lib: NightVisionLibs,
        FONT: "12px -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif",
        MAX_ZOOM: 3500,
        DEFAULT_LEN: 200
      },
      scripts: NightVisionScripts
    });

    chartStore.setInitialRange([...(chart.range as [number, number])]);

    chart.data.panes[0].overlays.push({
      type: "LineTool",
      name: "LineTool",
      data: [],
      props: {},
      settings: {
        zIndex: 1
      }
    });

    chartStore.chart = chart;

    chart.events.on(":shape-tool-ready", (shapeTool: ShapeTool) => {
      const currentDataName =
        candleStickStore.symbol + "_" + candleStickStore.interval;

      shapeTool.addShapesFromJSON(currentDataName);

      chartStore.shapeToolInstance = shapeTool;

      chartStore.shapeToolInstance.addListener(
        "home",
        "select-shape",
        async (line: IBaseShapeOptions<any> | undefined) => {
          chartStore.selectedShape = undefined;

          await nextTick();

          chartStore.selectedShape = line;
        }
      );

      chartStore.shapeToolInstance.addListener(
        "home",
        "double-click",
        async () => {
          await nextTick();

          if (!chartStore.selectedShape) {
            return;
          }

          appbarStore.toggleModal("chartSettingsModal", true);
        }
      );

      chartStore.shapeToolInstance.addListener(
        "home",
        "shape-draw-start",
        () => {
          appbarStore.toggleModal("chartSettingsModal", false);
        }
      );

      chartStore.shapeToolInstance.addListener(
        "home",
        "shape-draw-truncate",
        () => {
          chartStore.selectedShape = undefined;

          chartStore.isShapeToolSelected = false;
        }
      );

      chartStore.shapeToolInstance.addListener(
        "home",
        "shape-draw-complete",
        () => {
          chartStore.shapeToolInstance?.saveShapes(currentDataName);
          chartStore.isShapeToolSelected = false;
        }
      );

      chartStore.shapeToolInstance.addListener(
        "home",
        "point-update-coordinates",
        () => {
          chartStore.shapeToolInstance?.saveShapes(currentDataName);
        }
      );

      chartStore.shapeToolInstance.addListener(
        "home",
        "shape-property-changed",
        () => {
          chartStore.shapeToolInstance?.saveShapes(currentDataName);
        }
      );

      chartStore.shapeToolInstance.addListener(
        "home",
        "chart-display-bars-update",
        async (data: {
          hi: number;
          lo: number;
          count: number;
          indexBased: boolean;
        }) => {
          await loadMoreOHLCData({
            ...data,
            broker: candleStickStore.symbolBroker
          });
        }
      );

      document.body.style.overflow = "hidden";
    });
  } catch (_e) {
    toast.error("Cannot load chart");
  }
});

onUnmounted(() => {
  document.body.style.cursor = "default";

  document.body.style.overflow = "auto";

  rightNavDrawerStore.toggleRightNavContentArea(false);
  bottomNavDrawerStore.toggleBottomNavContentArea(false);
});
</script>

<template>
  <Teleport to="body">
    <ShapeToolbar
      v-if="chartStore.selectedShape && !appbarStore.modals.chartSettingsModal"
    />

    <ChartSettingsModal v-if="appbarStore.modals.chartSettingsModal" />
  </Teleport>

  <Loader v-if="loader" />

  <div v-else class="flex flex-col h-full">
    <Appbar />

    <div class="flex flex-grow">
      <LeftNavDrawer />

      <div class="flex-grow flex flex-col min-w-60">
        <div class="flex flex-grow w-full">
          <ChartLoader
            :width="chartStore.chartWidth"
            :height="chartStore.chartHeight"
            v-if="chartStore.chartLoader"
          />

          <div class="relative flex flex-col w-full shrink overflow-hidden">
            <div
              id="chart-container"
              class="outline-none grow h-28"
              tabindex="-1"
            ></div>

            <ChartNavigationToolbar />

            <ChartControlsBar />
          </div>

          <div
            class="hidden md:block flex-none"
            v-if="rightNavDrawerStore.rightNavContentArea"
          >
            <RightNavContentArea />
          </div>
        </div>

        <div class="flex-none">
          <BottomNavContentArea
            v-if="bottomNavDrawerStore.bottomNavContentArea"
          />

          <BottomNavDrawer v-else />
        </div>
      </div>

      <RightNavDrawer />
    </div>
  </div>
</template>
