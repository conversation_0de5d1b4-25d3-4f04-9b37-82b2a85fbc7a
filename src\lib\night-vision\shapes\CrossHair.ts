import { TCoreData } from "../types";
import { DistanceUtils } from "./utils/DistanceUtils";
import { DrawUtils2 } from "./utils/DrawUtils2";
import { ILineProperties, PreferenceUtils } from "./utils/PreferencesUtils";
import { BaseShape, IBaseShape, IBaseShapeOptions } from "./shapes/BaseShape";
import { Point } from "./shapes/Point";
import { formatCursorX } from "./utils/fns";
import { BaseShapeInterface } from "./base/shape.types";
import { HLine } from "./HLine";
import { VLine } from "./VLine";
type BasicPoint = { x: number; y: number };

export class CrossHair
  extends BaseShape<"crosshair">
  implements BaseShapeInterface<"crosshair">
{
  dragState: "tracking" | "settled" | "dragging" | "tracking-x" | "tracking-y" =
    "settled";
  lines: (HLine | VLine)[] = [];

  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (line: IBaseShape) => boolean
  ) {
    const linePoints = points.length
      ? points
      : [{ x: $core.cursor.x, y: $core.cursor.y }];
    super(
      $core,
      uuid,
      linePoints,
      points.length ? screenPoints : true,
      onSelect
    );
    this.type = "crosshair";
    this.properties = PreferenceUtils.crosshair;
    this.lines.push(
      new HLine(
        $core,
        "_" + uuid + "l1",
        [{ x: linePoints[0].x, y: linePoints[0].y }],
        points.length ? screenPoints : true,
        () => (this.selected = true)
      )
    );

    this.lines.push(
      new VLine(
        $core,
        uuid + "l2",
        [{ x: linePoints[0].x, y: linePoints[0].y }],
        points.length ? screenPoints : true,
        () => (this.selected = true)
      )
    );
    this.lines.forEach((line) => (line.properties = this.properties));

    if (!points.length) $core.hub.events.emit("shape-draw-complete", this);
    this.type = "crosshair";

    this.$core.hub.events.off(`base-shape:double-click`);

    this.$core.hub.events.on(`base-shape:double-click`, () => {
      setTimeout(() => {
        this.lines.forEach((line) => {
          line.dragging = false;
          line.draggingPoint = null;
        });
        this.dragging = false;
        this.draggingPoint = null;
      }, 0);
    });
  }

  get isValid(): boolean {
    return true;
  }

  get screenPoints() {
    return [
      { x: this.points[0].screenX, y: 0 },
      { x: this.points[0].screenX, y: this.$core.layout.height },
      { y: this.points[0].screenY, x: 0 },
      { y: this.points[0].screenY, x: this.$core.layout.width }
    ];
  }

  get endPoints() {
    return this.screenPoints;
  }

  toJSON: () => { [x: string]: any } = () => {
    return {
      uuid: this.uuid,
      type: this.type,
      points: this.points.map((p) => ({ x: p.x, y: p.y })),
      time: this.points.map((p) => p.time),
      properties: this.properties
    };
  };

  get midPoint(): { x: number; y: number } {
    return this.points[0].screen;
  }

  drawLine(ctx: CanvasRenderingContext2D) {
    const points = this.screenPoints;
    const lp = this.properties.lineProperties;

    ctx.save();
    ctx.strokeStyle = lp.line_color;
    ctx.setLineDash(DrawUtils2.getLineDash(lp.line_type));
    ctx.lineWidth = lp.line_width;
    DrawUtils2.drawLine(ctx, points[0], points[1]);
    DrawUtils2.drawLine(ctx, points[2], points[3]);
    ctx.restore();
  }

  draw(ctx: CanvasRenderingContext2D): void {
    // super.draw(ctx)
    this.lines.forEach((line) => {
      line.drawLine(ctx);
    });
    this.drawMidPoint(ctx);
  }

  drawSidebar(
    ctx: CanvasRenderingContext2D,
    _: any,
    scale: { prec: number }
  ): void {
    const lp = this.properties.lineProperties;
    const sd = this.properties.sidebarDisplay;

    const draw = () => {
      ctx.fillStyle = lp.line_color;
      this.points.forEach((point) => {
        const label = point.y.toFixed(scale.prec);
        ctx.save();
        DrawUtils2.drawSidebar(ctx, label, point.screenY);
        ctx.restore();
      });
    };

    if (sd.show_sidebar_default) draw();
    else if (sd.show_sidebar_hover && this.hovered) draw();
    else if (sd.show_sidebar_selected && this.selected) draw();
  }

  drawBotbar(ctx: CanvasRenderingContext2D): void {
    const lp = this.properties.lineProperties;
    const sd = this.properties.sidebarDisplay;

    const draw = () => {
      ctx.save();
      ctx.fillStyle = lp.line_color;
      this.points.forEach((point) => {
        const label = formatCursorX(
          point.time,
          this.$core.props.timeFrame,
          this.$core.props.timezone
        );
        DrawUtils2.drawBotbar(ctx, label, point.screenX);
      });
      ctx.restore();
    };

    if (sd.show_sidebar_default) draw();
    else if (sd.show_sidebar_hover && this.hovered) draw();
    else if (sd.show_sidebar_selected && this.selected) draw();
  }

  drawMidPoint(ctx: CanvasRenderingContext2D) {
    const point = this.points[0].screen;

    const lp = this.properties.lineProperties;
    const pd = this.properties.pointDisplay;
    const pp = this.properties.pointProperties;

    if (pp.point_position !== "midpoint") return;

    let width = 0;
    if (this.selected && pd.show_point_selected) {
      width = pp.point_width_selected;
    } else if (this.hovered && pd.show_point_hover) {
      width = pp.point_width_hover;
    } else if (pd.show_point_default) {
      width = pp.point_width_default;
    }

    if (width)
      Point.drawPoint(
        ctx,
        point,
        pp.point_shape,
        width,
        lp.line_color,
        lp.line_width
      );
  }

  private cursorOverLines(event: MouseEvent) {
    const cursor = { x: event.offsetX, y: event.offsetY };

    return this.lines.some((line) =>
      DistanceUtils.isCursorOnLine(
        line.screenPoints[0],
        line.screenPoints[1],
        cursor
      )
    );
  }

  mousedown(event: MouseEvent): void {
    if (this.cursorOverLines(event)) {
      this.onSelect(this);
      this.dragging = true;
      this.draggingPoint = { x: this.$core.cursor.x, y: this.$core.cursor.y };

      this.$core.hub.events.emit("scroll-lock", true);
    } else {
      this.dragging = false;
      this.draggingPoint = null;
    }
    this.$core.hub.events.emit("update-layout");
  }

  mouseover(event: MouseEvent): void {
    if (this.cursorOverLines(event)) {
      this.hovered = true;
    } else {
      this.hovered = false;
    }
  }

  moveShape(difference: BasicPoint): void {
    super.moveShape(difference);
    this.lines.forEach((l) => {
      l.moveShape(difference);
    });
  }

  mousemove(): void {
    if (this.dragging && this.draggingPoint && this.selected) {
      const difference = {
        x: this.$core.cursor.x - this.draggingPoint.x,
        y: this.$core.cursor.y - this.draggingPoint.y
      };
      this.draggingPoint = {
        x: this.$core.cursor.x,
        y: this.$core.cursor.y
      };
      this.moveShape(difference);
    }
  }

  mouseup(): void {
    this.dragging = false;
    this.$core.hub.events.emit("scroll-lock", false);
  }

  keydown?(): void {
    // throw new Error("Method not implemented.")
  }

  getCoordinates(): { [x: string]: any } {
    return {
      coords: {
        row: this.points[0].x,
        price: this.points[0].y
      }
    };
  }

  setCoordinates(name: string, value: any): boolean {
    if (value.row && value.price)
      switch (name) {
        case "coords": {
          const difference = {
            x: Number(value.row) - this.points[0].x,
            y: Number(value.price) - this.points[0].y
          };

          this.lines.forEach((l) => {
            l.points[0].x += difference.x;
            l.points[0].y += difference.y;
          });

          this.points[0].x = Number(value.row);
          this.points[0].y = Number(value.price);

          this.$core.hub.events.emit("point-update-coordinates");
          this.$core.hub.events.emit("update-layout");
          return true;
        }
      }
    return false;
  }

  setProperty<
    Y extends keyof ILineProperties,
    Z extends keyof ILineProperties[Y]
  >(baseKey: Y, subKey: Z, value: ILineProperties[Y][Z]) {
    super.setProperty(baseKey, subKey, value);
    this.lines.forEach((line) => line.setProperty(baseKey, subKey, value));
    return false;
  }

  setProperties(p: ILineProperties): void {
    super.setProperties(p);
    this.lines.forEach((line) => line.setProperties(p));
  }

  getOptions: () => IBaseShapeOptions<any> = () => ({
    uuid: this.uuid,
    deleteShape: () => console.error("Not implemented"),
    dragging: this.dragging,
    getCoordinates: this.getCoordinates.bind(this),
    getLabelProperties: this.getLabelProperties.bind(this),
    hovered: this.hovered,
    locked: this.locked,
    toggleDetails: this.toggleDetails.bind(this),
    properties: this.properties,
    setProperty: this.setProperty.bind(this) as any,
    setCoordinates: this.setCoordinates.bind(this),
    type: this.type,
    name: this.name,
    setName: this.setName.bind(this)
  });
}
