<script setup lang="ts">
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";

import { useUserStore } from "@/store/userStore";

import Dropdown from "@/components/Dropdown.vue";

import AccountSVG from "@/assets/svg/account.svg";
import LogOutSVG from "@/assets/svg/logout.svg";

const route = useRoute();
const router = useRouter();

const userStore = useUserStore();

const userDropdown = ref(false);

async function logOut() {
  try {
    await userStore.logOut();

    if (route.name === "home") {
      return;
    }

    router.push({ name: "login" });
  } catch (e) {
    console.error(e);
  }
}
</script>

<template>
  <Dropdown
    class="h-10 w-10 rounded-full border-2 border-gray-400 hover:bg-tertiary"
    id="user-profile-dropdown"
    toggle-id="user-profile-toggle-dropdown"
    :icon="false"
    :offset-distance="4"
    :offset-skidding="-100"
    :class="{ 'border-white': userDropdown }"
    @show="userDropdown = true"
    @hide="userDropdown = false"
  >
    <template #text>
      {{ userStore.user?.name.slice(0, 2) }}
    </template>

    <template #content>
      <div class="mb-1 mt-4 w-60 text-black">
        <div class="flex justify-center">
          <div
            class="grid h-10 w-10 place-items-center rounded-full bg-gray-300 text-base"
          >
            {{ userStore.user?.name.slice(0, 2) }}
          </div>
        </div>

        <div class="mt-3 text-center font-medium">
          {{ userStore.user?.name }}
        </div>

        <div class="text-center text-xs text-gray-500">
          {{ userStore.user?.email }}
        </div>

        <div class="mt-2 divide-y">
          <router-link
            target="_blank"
            :to="{ name: 'dashboard' }"
            class="inline-flex w-full cursor-default items-center px-3 pb-1.5 pt-2 hover:bg-accent"
          >
            <div class="w-7">
              <AccountSVG />
            </div>

            <span>My Account</span>
          </router-link>

          <div
            role="button"
            class="flex w-full cursor-default items-center rounded-none px-3 pb-1.5 pt-2 hover:bg-accent"
            @click="logOut"
          >
            <div class="w-7">
              <LogOutSVG />
            </div>

            <span>Log Out</span>
          </div>
        </div>
      </div>
    </template>
  </Dropdown>
</template>
