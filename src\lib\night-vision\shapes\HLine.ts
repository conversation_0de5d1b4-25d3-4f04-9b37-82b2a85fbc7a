import { TCoreData } from "../types";
import { Line } from "./LineTool";
import { DistanceUtils } from "./utils/DistanceUtils";
import { PreferenceUtils } from "./utils/PreferencesUtils";
import { IBaseShape } from "./shapes/BaseShape";
import { BaseShapeInterface } from "./base/shape.types";
type BasicPoint = { x: number; y: number };

export class HLine
  extends Line
  implements BaseShapeInterface<"horizontal-line">
{
  dragState: "tracking" | "settled" | "dragging" | "tracking-x" | "tracking-y" =
    "settled";
  draggingPoint: { x: number; y: number } | null = null;
  dragging: boolean = false;
  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (line: IBaseShape) => boolean
  ) {
    const linePoints = points.length
      ? points
      : [{ x: $core.cursor.x, y: $core.cursor.y }];
    super(
      $core,
      uuid,
      linePoints,
      points.length ? screenPoints : true,
      onSelect
    );
    this.type = "horizontal-line";
    if (!points.length) $core.hub.events.emit("shape-draw-complete", this);

    this.properties = PreferenceUtils["horizontal-line"];
  }

  get isValid(): boolean {
    return true;
  }

  get endPoints() {
    return [
      { x: 0, y: this.points[0].screenY },
      { x: this.$core.layout.width, y: this.points[0].screenY }
    ];
  }

  get midPoint() {
    return this.points[0].screen;
  }

  mousedown(event: MouseEvent): void {
    const cursor = { x: event.offsetX, y: event.offsetY };
    if (
      DistanceUtils.isCursorOnLine(
        this.screenPoints[0],
        this.screenPoints[1],
        cursor
      )
    ) {
      this.onSelect(this);
      // if (DistanceUtils.isCursorOnPoint(this.midPoint, cursor)) {
      this.dragging = true;
      this.draggingPoint = { x: this.$core.cursor.x, y: this.$core.cursor.y };
      this.$core.hub.events.emit("scroll-lock", true);
      // }
    } else {
      this.dragging = false;
      this.draggingPoint = null;
      // // this.selected = false
    }
    this.$core.hub.events.emit("update-layout");
  }

  keydown?(): void {
    // throw new Error("Method not implemented.")
  }
  getCoordinates(): { [x: string]: any } {
    return {
      price: this.points[0].y
    };
  }
  setCoordinatesVal(name: string, value: any): boolean {
    switch (name) {
      case "price": {
        this.points[0].y = Number(value);
        return true;
      }
    }
    return false;
  }
}
