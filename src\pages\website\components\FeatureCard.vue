<script setup lang="ts">
import { ArrowUpRight, Shield, BarChart2, Code2 } from "lucide-vue-next";

import YouTubeEmbed from "./YouTubeEmbed.vue";

defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    required: true
  },
  videoId: {
    type: String,
    default: ""
  }
});

const iconComponents = {
  ArrowUpRight,
  Shield,
  BarChart2,
  Code2
};
</script>

<template>
  <div
    class="transform rounded-2xl bg-white p-8 shadow-xl transition-transform duration-300 hover:scale-105"
  >
    <div class="mb-6">
      <YouTubeEmbed v-if="videoId" :videoId="videoId" :title="title" />
      <div
        v-else
        class="flex aspect-video items-center justify-center rounded-lg bg-indigo-100"
      >
        <component
          :is="iconComponents[icon]"
          class="h-16 w-16 text-indigo-600"
        />
      </div>
    </div>
    <h2 class="mb-4 text-2xl font-bold text-gray-900">{{ title }}</h2>
    <p class="text-gray-600">{{ description }}</p>
  </div>
</template>
