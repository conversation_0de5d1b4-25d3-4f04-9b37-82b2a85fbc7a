import { test, expect } from "vitest";
import { hexToRGBA, rgbaToHex } from "@/helpers/colorConversion";

test("Test for RGBA to HEX", () => {
  expect(rgbaToHex("rgba(0,0,0,0)")).toStrictEqual("#00000000");
  expect(rgbaToHex("rgba(0,0,0,1)")).toStrictEqual("#000000ff");

  expect(rgbaToHex("rgb(0,0,0)")).toStrictEqual("#000000");
  expect(rgbaToHex("rgba(255,255,255,1)")).toStrictEqual("#ffffffff");
  expect(rgbaToHex("rgb(255,255,255)")).toStrictEqual("#ffffff");
});

test("Test for HEX to RGBA", () => {
  expect(hexToRGBA("#000000", 0)).toSatisfy((v) => v === "rgba(0,0,0,0)");
  expect(hexToRGBA("#000", 0)).toSatisfy((v) => v === "rgba(0,0,0,0)");
  expect(hexToRGBA("#000", 100)).toSatisfy((v) => v === "rgba(0,0,0,1)");
  expect(hexToRGBA("#000000", 100)).toSatisfy((v) => v === "rgba(0,0,0,1)");
  expect(hexToRGBA("#fff", 0)).toSatisfy((v) => v === "rgba(255,255,255,0)");
});
