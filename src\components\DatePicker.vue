<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { DateTime } from "luxon";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import NavItem from "@/components/NavItem.vue";

const emit = defineEmits(["selected"]);

const selectedDate = defineModel<string>({ required: true });

const weekDays = ref(["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]);
const currentDate = ref(DateTime.now().toFormat("yyyy-MM-dd"));
const currentMonthDates = ref<number[]>([]);
const previousMonthDates = ref<number[]>([]);
const nextMonthDates = ref<number[]>([]);

onMounted(() => {
  calculateTotalDaysInAMonth();
});

const currentMonth = computed(() => {
  return DateTime.fromJSDate(new Date(currentDate.value)).toFormat("MMM");
});

const currentYear = computed(() => {
  return DateTime.fromJSDate(new Date(currentDate.value)).toFormat("yyyy");
});

function isDateSame(day: number) {
  const d1 = DateTime.fromJSDate(new Date(currentDate.value));

  const d2 = DateTime.fromObject({
    year: d1.year,
    month: d1.month,
    day
  }).toFormat("yyyy-MM-dd");

  return selectedDate.value === d2;
}

function calculateTotalDaysInAMonth() {
  const { year, month } = DateTime.fromJSDate(new Date(currentDate.value));

  const date = DateTime.fromObject({ year, month });

  const totalDaysInMonth = date.daysInMonth!;

  currentMonthDates.value = [];

  for (let i = 1; i <= totalDaysInMonth; i++) {
    currentMonthDates.value.push(i);
  }

  previousMonthDates.value = [];

  if (date.weekday !== 7) {
    const lastDayOfPreviousMonth = parseInt(
      DateTime.fromJSDate(new Date(currentDate.value))
        .minus({ months: 1 })
        .endOf("month")
        .toFormat("dd")
    );

    for (let i = date.weekday - 1; i >= 0; i--) {
      previousMonthDates.value.push(lastDayOfPreviousMonth - i);
    }
  }

  nextMonthDates.value = [];

  const totalDaysCount =
    previousMonthDates.value.length + currentMonthDates.value.length;

  const remainingSpaceCount = (42 - totalDaysCount) % 7;

  for (let i = 1; i <= remainingSpaceCount; i++) {
    nextMonthDates.value.push(i);
  }
}

function handlePreviousMonth() {
  const prevDate = DateTime.fromJSDate(new Date(currentDate.value)).minus({
    month: 1
  });

  currentDate.value = prevDate.toFormat("yyyy-MM-dd");

  calculateTotalDaysInAMonth();
}

function handleNextMonth() {
  const nextDate = DateTime.fromJSDate(new Date(currentDate.value)).plus({
    month: 1
  });

  currentDate.value = nextDate.toFormat("yyyy-MM-dd");

  calculateTotalDaysInAMonth();
}

function handleSelectedDate(day: number) {
  const { year, month } = DateTime.fromJSDate(new Date(currentDate.value));

  const date = DateTime.fromObject({
    year,
    month,
    day
  });

  selectedDate.value = date.toFormat("yyyy-MM-dd");

  emit("selected");
}
</script>

<template>
  <div class="w-[280px] cursor-default p-2">
    <div class="grid grid-cols-3 items-center">
      <div>
        <NavItem class="px-3" @click="handlePreviousMonth">
          <FontAwesomeIcon icon="fa-solid fa-chevron-left" />
        </NavItem>
      </div>

      <div class="text-center font-semibold">
        {{ currentMonth }} {{ currentYear }}
      </div>

      <div class="flex items-center justify-end">
        <NavItem class="px-3" @click="handleNextMonth">
          <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
        </NavItem>
      </div>
    </div>

    <div class="mt-1 grid grid-cols-7">
      <div v-for="day in weekDays" class="flex justify-center">
        <div class="w-10 text-center font-medium">
          {{ day }}
        </div>
      </div>
    </div>

    <div class="mt-1 grid grid-cols-7">
      <div
        class="grid h-9 w-9 place-items-center opacity-50"
        v-for="date in previousMonthDates"
      >
        {{ date }}
      </div>

      <div
        class="flex justify-center"
        v-for="date in currentMonthDates"
        :key="date"
        @click="handleSelectedDate(date)"
      >
        <div
          class="grid h-9 w-9 place-items-center rounded-full hover:bg-gray-300"
          :class="{
            'bg-primary text-white hover:bg-primary': isDateSame(date)
          }"
        >
          {{ date }}
        </div>
      </div>

      <div
        class="grid h-9 w-9 place-items-center opacity-50"
        v-for="date in nextMonthDates"
      >
        {{ date }}
      </div>
    </div>
  </div>
</template>
