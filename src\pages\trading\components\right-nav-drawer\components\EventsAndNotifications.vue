<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from "vue";

import { DateTime } from "luxon";

import { getElementWidthAndHeight } from "@/helpers/elementWidthAndHeight";

import { useChartStore } from "@/store/chartStore";
import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";

import Spinner from "@/components/Spinner.vue";

import { axios } from "@/api";

const chartStore = useChartStore();
const rightNavDrawerStore = useRightNavDrawerStore();

const isLoading = ref(true);
const contentAreaHeight = ref(0);
const eventType = ref("news");
const loadMore = ref(false);

let newsPage = 1;
let totalNewsPage = 1;
let announcementPage = 1;
let totalAnnouncementPage = 1;

onMounted(() => {
  calculateScrollHeight();

  getEvents();
});

onUnmounted(() => {
  rightNavDrawerStore.newsList = [];
  rightNavDrawerStore.announcementList = [];
});

const eventList = computed(() => {
  return eventType.value === "news"
    ? rightNavDrawerStore.newsList
    : rightNavDrawerStore.announcementList;
});

watch(
  () => chartStore.chartHeight,
  () => {
    calculateScrollHeight();
  }
);

function calculateScrollHeight() {
  const chartControlBar = getElementWidthAndHeight("chart-controls-bar");
  const eventsHeader = getElementWidthAndHeight("events-notifications-header");

  contentAreaHeight.value =
    chartStore.chartHeight - eventsHeader.height + chartControlBar.height;
}

async function getEvents() {
  try {
    if (!loadMore.value) {
      isLoading.value = true;
    }

    const resp = await axios.get(eventType.value, {
      params: {
        page: eventType.value === "news" ? newsPage : announcementPage,
        limit: 25
      }
    });

    if (eventType.value === "news") {
      rightNavDrawerStore.newsList = [
        ...rightNavDrawerStore.newsList,
        ...resp.data.data.news
      ];

      // This is done to remove duplicate events (at index 0 and 1)
      if (
        rightNavDrawerStore.newsList[0]._id ===
        rightNavDrawerStore.newsList[1]._id
      ) {
        rightNavDrawerStore.newsList.splice(1, 1);
      }

      totalNewsPage = resp.data.data.pagination.totalPages;
    } else {
      rightNavDrawerStore.announcementList = [
        ...rightNavDrawerStore.announcementList,
        ...resp.data.data.announcements
      ];

      if (
        rightNavDrawerStore.announcementList[0]._id ===
        rightNavDrawerStore.announcementList[1]._id
      ) {
        rightNavDrawerStore.announcementList.splice(1, 1);
      }

      totalAnnouncementPage = resp.data.data.pagination.totalPages;
    }
  } catch (e) {
    console.error(e);
  } finally {
    isLoading.value = false;
    loadMore.value = false;
  }
}

function handleEventType(type: string) {
  eventType.value = type;

  if (rightNavDrawerStore.announcementList.length === 0) {
    getEvents();
  }
}

function loadMoreEvents(e: Event) {
  const el = e.target as HTMLElement;

  const scrollTop = el.scrollTop;
  const clientHeight = el.clientHeight;
  const scrollHeight = el.scrollHeight;

  if (scrollTop + clientHeight >= scrollHeight) {
    if (eventType.value === "news") {
      if (newsPage > totalNewsPage) {
        return;
      }

      loadMore.value = true;
      newsPage++;

      getEvents();

      return;
    }

    if (announcementPage > totalAnnouncementPage) {
      return;
    }

    loadMore.value = true;

    announcementPage++;

    getEvents();
  }
}
</script>

<template>
  <div class="grow">
    <div id="events-notifications-header" class="px-3 py-2">
      <div class="text-lg font-bold">Events & Notifications</div>

      <div class="mt-1 flex gap-x-2">
        <div
          class="rounded-full bg-gray-200 px-3 pb-2 pt-2.5 hover:bg-gray-300"
          :class="{
            'bg-selected font-medium text-white hover:bg-selected':
              eventType === 'news'
          }"
          @click="handleEventType('news')"
        >
          News
        </div>

        <div
          class="rounded-full bg-gray-200 px-3 pb-2 pt-2.5 hover:bg-gray-300"
          :class="{
            'bg-selected font-medium text-white hover:bg-selected':
              eventType === 'announcements'
          }"
          @click="handleEventType('announcements')"
        >
          Announcements
        </div>
      </div>
    </div>

    <div class="mt-3 flex justify-center" v-if="isLoading">
      <Spinner />
    </div>

    <div
      class="scrollbar divide-y overflow-auto border-t"
      :style="{ height: contentAreaHeight + 'px' }"
      @scroll="loadMoreEvents"
      v-else
    >
      <div class="px-3 pb-2.5 pt-3" v-for="event in eventList" :key="event._id">
        <div class="flex justify-between gap-x-2">
          <div class="text-sm font-bold">
            {{ event.title }}
          </div>

          <div
            class="text-[11px] text-gray-600"
            v-if="eventType === 'announcements'"
          >
            {{ DateTime.fromISO(event.createdAt).toFormat("HH:mm, MMM dd") }}
          </div>
        </div>

        <div
          class="mt-1 flex items-center justify-between"
          v-if="eventType === 'news'"
        >
          <div
            class="rounded-xl px-1.5 pb-0.5 text-[10px] font-medium"
            :class="{
              'bg-green-200': event.category === 'low',
              'bg-orange-300': event.category === 'medium',
              'bg-red-300': event.category === 'high'
            }"
          >
            Category:
            {{ event.category[0].toUpperCase() + event.category.slice(1) }}
          </div>

          <div class="text-[11px] text-gray-600">
            {{ DateTime.fromISO(event.createdAt).toFormat("HH:mm, MMM dd") }}
          </div>
        </div>

        <div class="mt-1.5 text-gray-600" v-html="event.description"></div>
      </div>

      <div class="flex justify-center py-2" v-if="loadMore">
        <Spinner class="!h-7 !w-7" />
      </div>
    </div>
  </div>
</template>
