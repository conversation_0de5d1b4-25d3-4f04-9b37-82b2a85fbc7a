<script setup lang="ts">
import { ref } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

defineOptions({
  inheritAttrs: false
});

type TVariants = "info" | "success" | "warning" | "danger";

const props = withDefaults(
  defineProps<{
    variant: TVariants;
    icon?: boolean;
    closeIcon?: boolean;
  }>(),
  {
    icon: true,
    closeIcon: false
  }
);

defineEmits(["close"]);

const variant = ref(props.variant);

function setAlertVariant() {
  if (variant.value === "success") {
    return "bg-green-100 text-green-600";
  } else if (variant.value === "warning") {
    return "bg-orange-100 text-orange-500";
  } else if (variant.value === "danger") {
    return "bg-red-100 text-red-600";
  } else {
    return "bg-blue-100 text-blue-600";
  }
}

function setCloseHoverColor() {
  if (variant.value === "success") {
    return "hover:bg-green-200";
  } else if (variant.value === "warning") {
    return "hover:bg-orange-200";
  } else if (variant.value === "danger") {
    return "hover:bg-red-200";
  } else {
    return "hover:bg-blue-200";
  }
}
</script>

<template>
  <div
    role="alert"
    class="flex gap-x-3 rounded-md px-3 pb-2.5 pt-3 text-sm"
    v-bind="$attrs"
    :class="setAlertVariant()"
  >
    <span class="sr-only">Info</span>

    <div v-if="icon">
      <FontAwesomeIcon size="lg" icon="fa-solid fa-circle-info" />
    </div>

    <div class="grow">
      <slot />
    </div>

    <div v-if="closeIcon">
      <FontAwesomeIcon
        size="lg"
        icon="fa-solid fa-xmark"
        class="rounded-md px-1.5 pb-1 pt-1"
        :class="setCloseHoverColor()"
        @click="$emit('close')"
      />
    </div>
  </div>
</template>
