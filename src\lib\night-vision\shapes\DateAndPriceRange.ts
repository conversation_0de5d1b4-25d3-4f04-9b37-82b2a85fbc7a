import { TCoreData } from "../types";
import { Rectangle } from "./Rectangle";
import { BasicPoint } from "./base/Types";
import { BaseShapeInterface } from "./base/shape.types";
import { BaseLine } from "./shapes/BaseLine";
import { IBaseShape } from "./shapes/BaseShape";
import { DistanceUtils } from "./utils/DistanceUtils";
import { DrawUtils2 } from "./utils/DrawUtils2";
import { BoxProperties, PreferenceUtils } from "./utils/PreferencesUtils";

// @ts-expect-error
export class DateAndPriceRange
  extends Rectangle
  implements BaseShapeInterface<"date-and-price-range">
{
  lines: BaseLine[] = [];

  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (line: IBaseShape) => boolean
  ) {
    super($core, uuid, points, screenPoints, onSelect);
    this.type = "date-and-price-range";
    this.properties = PreferenceUtils["date-and-price-range"];
  }
  // get details() {
  //   // TODO
  //   return {}
  // }
  detailsInfo: { [x: string]: boolean } = {
    diff: true,
    percentChange: true,
    changeInPips: true,
    dateTimeRange: true,
    barsRange: true
  };
  get screenPoints(): BasicPoint[] {
    return [
      {
        x: (this.points[0].screenX + this.points[1].screenX) / 2,
        y: (this.points[0].screenY + this.points[1].screenY) / 2
      },
      {
        x: (this.points[1].screenX + this.points[2].screenX) / 2,
        y: (this.points[1].screenY + this.points[2].screenY) / 2
      },
      {
        x: (this.points[2].screenX + this.points[3].screenX) / 2,
        y: (this.points[2].screenY + this.points[3].screenY) / 2
      },
      {
        x: (this.points[3].screenX + this.points[0].screenX) / 2,
        y: (this.points[3].screenY + this.points[0].screenY) / 2
      }
    ];
  }
  mouseOverLines(event: MouseEvent): boolean {
    const cursor = { x: event.offsetX, y: event.offsetY };
    const pairs = [];
    pairs.push([this.screenPoints[0], this.screenPoints[2]]);
    pairs.push([this.screenPoints[1], this.screenPoints[3]]);

    if (pairs.some((p) => DistanceUtils.isCursorOnLine(p[0], p[1], cursor)))
      return true;
    return false;
  }
  draw(ctx: CanvasRenderingContext2D): void {
    if (this.selected || this.hovered) {
      this.points.forEach((p) => {
        DrawUtils2.drawPoint(ctx, p.screenX, p.screenY);
      });
    }

    const fp = (this.properties as BoxProperties).backgroundProperties;

    ctx.fillStyle = fp.fill_color;

    const points = this.points;
    ctx.beginPath();
    ctx.moveTo(points[0].screenX, points[0].screenY);
    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].screenX, points[i].screenY);
    }
    ctx.closePath();
    ctx.fill();

    const sP = this.screenPoints;

    ctx.save();
    ctx.lineWidth = this.properties.lineProperties.line_width;
    ctx.strokeStyle = this.properties.lineProperties.line_color;
    let width = this.properties.pointProperties.point_width_default;
    if (this.hovered) width = this.properties.pointProperties.point_width_hover;
    if (this.selected)
      width = this.properties.pointProperties.point_width_selected;

    DrawUtils2.drawArrow(ctx, sP[0], sP[2], width, undefined, true);
    DrawUtils2.drawLine(ctx, sP[0], sP[2]);
    DrawUtils2.drawArrow(ctx, sP[3], sP[1], width, undefined, true);
    DrawUtils2.drawLine(ctx, sP[3], sP[1]);

    ctx.restore();
    this.drawDetails(ctx);
  }
  mousedown(event: MouseEvent): void {
    const cursor = { x: event.offsetX, y: event.offsetY };
    if (this.selected) {
      let dragPoint = false;
      for (let i = 0; i < this.points.length; i++) {
        const p = this.points[i];
        if (DistanceUtils.isCursorOnPoint(p.screen, cursor)) {
          p.startDragging();
          this.points[(i + 1) % 4].startDragging(i % 2 === 0 ? "y" : "x");
          this.points[(i + 3) % 4].startDragging(i % 2 === 0 ? "x" : "y");
          dragPoint = true;
          break;
        }
      }
      if (dragPoint) {
        this.onSelect(this);
        return;
      }
    }
    if (this.mouseOverLines(event)) {
      this.onSelect(this);
      let dragPoint = false;

      for (let i = 0; i < this.screenPoints.length; i++) {
        const p = this.screenPoints[i];
        const p0 = this.points[i];
        const p1 = this.points[(i + 1) % 4];
        if (DistanceUtils.isCursorOnPoint(p, cursor)) {
          if (p0.x === p1.x) {
            p0.startDragging("x");
            p1.startDragging("x");
          } else if (p0.y === p1.y) {
            p0.startDragging("y");
            p1.startDragging("y");
          }
          dragPoint = true;
          break;
        }
      }
      if (!dragPoint) {
        this.dragging = true;
        this.draggingPoint = { x: this.$core.cursor.x, y: this.$core.cursor.y };

        this.$core.hub.events.emit("scroll-lock", true);
      }
    } else {
      this.dragging = false;
      this.draggingPoint = null;
      this.selected = false;
    }
    this.$core.hub.events.emit("update-layout");
  }
}
