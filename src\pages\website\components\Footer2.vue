<script setup lang="ts">
import { Facebook, Mail, MapPin, Youtube } from "lucide-vue-next";

const currentYear = new Date().getFullYear();
</script>

<template>
  <footer class="bg-blue-900 py-8 text-white">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
        <div class="space-y-4">
          <h3 class="mb-4 text-xl font-semibold text-blue-200">Tradeawaay</h3>
          <div class="flex items-start space-x-2">
            <MapPin class="mt-1 h-5 w-5 flex-shrink-0 text-blue-300" />
            <p class="text-sm text-gray-100">
              3080 Yonge St Ste 6060, Toronto, ON M4N 3N1
            </p>
          </div>
          <div class="flex items-center space-x-2">
            <Mail class="h-5 w-5 text-blue-300" />
            <a
              href="mailto:<EMAIL>"
              class="text-sm text-gray-100 transition-colors hover:text-blue-200"
              ><EMAIL></a
            >
          </div>
        </div>

        <div>
          <h3 class="mb-4 text-xl font-semibold text-blue-200">Quick Links</h3>
          <ul class="space-y-2">
            <li>
              <a
                href="#features"
                class="text-sm text-gray-100 transition-colors hover:text-blue-200"
                >Features</a
              >
            </li>
            <li>
              <a
                href="#pricing"
                class="text-sm text-gray-100 transition-colors hover:text-blue-200"
                >Pricing</a
              >
            </li>
            <li>
              <a
                href="#faq"
                class="text-sm text-gray-100 transition-colors hover:text-blue-200"
                >FAQ</a
              >
            </li>
            <li>
              <a
                href="/community/videos"
                class="text-sm text-gray-100 transition-colors hover:text-blue-200"
                >Education</a
              >
            </li>
          </ul>
        </div>

        <div>
          <h3 class="mb-4 text-xl font-semibold text-blue-200">Legal</h3>
          <ul class="space-y-2">
            <li>
              <a
                href="/legal/privacy_policy"
                class="text-sm text-gray-100 transition-colors hover:text-blue-200"
                >Privacy Policy</a
              >
            </li>
            <li>
              <a
                href="/legal/tos"
                class="text-sm text-gray-100 transition-colors hover:text-blue-200"
                >Terms of Service</a
              >
            </li>
          </ul>
        </div>

        <div>
          <h3 class="mb-4 text-xl font-semibold text-blue-200">
            Connect With Us
          </h3>
          <div class="mb-6 flex space-x-4">
            <a
              href="https://www.facebook.com/cittaalgolabs"
              class="text-blue-300 transition-colors hover:text-blue-200"
              target="_blank"
            >
              <Facebook class="h-6 w-6" />
            </a>
            <a
              href="https://www.youtube.com/@tradeawaay"
              class="text-blue-300 transition-colors hover:text-blue-200"
              target="_blank"
            >
              <Youtube class="h-6 w-6" />
            </a>
          </div>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="mt-8 border-t border-blue-800 pt-6">
        <div class="mb-4 rounded-lg bg-blue-950 p-4 text-xs text-gray-200">
          <p class="mb-2 font-medium text-blue-200">DISCLOSURE:</p>
          <p>
            All information provided on this site is exclusively intended for
            educational purpose only. We do not provide trading signal,
            brokerage service or proprietary trading service and does not serve
            in any way as an investment recommendation.
          </p>
          <p class="mt-2 font-medium text-blue-200">Forex Disclosure:</p>
          <p>
            Please note that foreign exchange and other leveraged trading carry
            a substantial risk of loss. It may not be suitable for all
            investors, and you should ensure you fully understand the risks
            involved, seeking independent advice if needed.
          </p>
        </div>

        <p class="text-center text-sm text-gray-100">
          © {{ currentYear }} Tradeawaay. All rights reserved.
        </p>
      </div>
    </div>
  </footer>
</template>
