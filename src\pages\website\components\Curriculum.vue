<script setup lang="ts">
import { Box, GraduationCap, Target } from "lucide-vue-next";

const features = [
  {
    title: "THE CLASSROOM",
    description: "A place for you to build a solid foundation to Forex Trading",
    icon: GraduationCap,
    gradient: "from-blue-500 to-cyan-400"
  },
  {
    title: "THE SANDBOX",
    description:
      "Practice and execute your trading plan while our algos provide feedbacks",
    icon: Box,
    gradient: "from-purple-500 to-pink-400"
  },
  {
    title: "THE ARENA",
    description:
      "Trade on your personal account or Funded account, special algos tracks your trading performance and provides feedback. ",
    icon: Target,
    gradient: "from-orange-500 to-red-400"
  }
];
</script>

<template>
  <div class="w-full bg-gray-50 py-16">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h2 class="mb-16 text-3xl font-bold text-gray-900 sm:text-4xl">
          Our 3 Part Curriculum
        </h2>
      </div>
      <div class="mt-8 grid grid-cols-1 gap-8 md:grid-cols-3">
        <div
          v-for="(feature, index) in features"
          :key="index"
          class="group relative"
        >
          <div
            class="absolute -inset-1 rounded-lg bg-gradient-to-r opacity-50 blur-lg transition-opacity duration-200 group-hover:opacity-100"
            :style="{
              backgroundImage: `linear-gradient(to right, var(--tw-gradient-stops))`
            }"
          ></div>
          <div
            class="relative h-full transform rounded-lg bg-white p-8 shadow-lg transition-all duration-200 group-hover:-translate-y-1"
          >
            <div
              :class="[
                'mb-6 inline-flex items-center justify-center rounded-lg bg-gradient-to-r p-3 shadow-lg',
                feature.gradient
              ]"
            >
              <component :is="feature.icon" class="h-6 w-6 text-white" />
            </div>
            <h3 class="mb-4 text-xl font-semibold text-gray-900">
              {{ feature.title }}
            </h3>
            <p class="leading-relaxed text-gray-600">
              {{ feature.description }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
