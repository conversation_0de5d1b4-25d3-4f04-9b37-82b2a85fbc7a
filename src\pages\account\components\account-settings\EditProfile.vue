<script setup lang="ts">
import { ref } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { useUserStore } from "@/store/userStore";
import { EUserRole } from "@/types/enums";

import ProfileHeader from "./ProfileHeader.vue";

const userStore = useUserStore();

const isUserCustomer = ref(userStore.user?.roles.includes(EUserRole.CUSTOMER));
</script>

<template>
  <ProfileHeader text="Your Account" route-name="account" />

  <div class="cursor-default px-5 pb-2.5 pt-3 text-sm hover:bg-gray-100">
    <div>Name</div>
    <div class="font-semibold">
      {{ userStore.user?.name }}
    </div>
  </div>

  <router-link :to="{ name: 'change-email' }">
    <div
      class="flex items-center justify-between px-5 pb-2.5 pt-3 text-sm hover:bg-gray-100"
    >
      <div>
        <div>Email</div>
        <div class="font-semibold">
          {{ userStore.user?.email }}
        </div>
      </div>

      <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
    </div>
  </router-link>

  <template v-if="isUserCustomer">
    <router-link :to="{ name: 'change-phone-number' }">
      <div
        class="flex items-center justify-between px-5 pb-2.5 pt-3 text-sm hover:bg-gray-100"
      >
        <div>
          <div>Phone Number</div>
          <div class="font-semibold">
            {{ userStore.user?.phone_number ?? "--" }}
          </div>
        </div>

        <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
      </div>
    </router-link>

    <router-link :to="{ name: 'change-location' }">
      <div
        class="flex items-center justify-between border-t px-5 pb-2.5 pt-3 text-sm hover:bg-gray-100"
      >
        <div>
          <div>Location</div>
          <div class="font-semibold">
            {{ userStore.user?.location?.country ?? "--" }}
          </div>
        </div>

        <FontAwesomeIcon icon="fa-solid fa-chevron-right" />
      </div>
    </router-link>
  </template>
</template>
