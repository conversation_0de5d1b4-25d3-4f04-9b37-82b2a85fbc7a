import { DateTime } from "luxon";

import { useCandleStickStore } from "@/store/candleStickStore";

import { axios } from "@/api";
import { ICandleStickInterval, IOHLC } from "@/types";

export const DEFAULT_FIRST_LOAD_COUNT = 1300;
export const DEFAULT_ADDITIONAL_LOAD_COUNT = 800;

export const candleStickIntervalList: ICandleStickInterval[] = [
  {
    id: "PERIOD_M1",
    type: "1m",
    name: "1 minute"
  },
  {
    id: "PERIOD_M5",
    type: "5m",
    name: "5 minutes"
  },
  {
    id: "PERIOD_M15",
    type: "15m",
    name: "15 minutes"
  },
  {
    id: "PERIOD_M30",
    type: "30m",
    name: "30 minutes"
  },
  {
    id: "PERIOD_H1",
    type: "1h",
    name: "1 hour"
  },
  {
    id: "PERIOD_H4",
    type: "4h",
    name: "4 hours"
  },
  {
    id: "PERIOD_D1",
    type: "D",
    name: "1 day"
  },
  {
    id: "PERIOD_W1",
    type: "W",
    name: "1 week"
  }
];

/**
 * @param symbol Instrument symbol (e.g. EURUSD).
 * @param interval Instrument interval (e.g. PERIOD_M15).
 * @param broker Market Broker (e.g. Blueberry Markets Pty Ltd).
 * @param timestamp Instrument timestamp (e.g. 1720792073).
 * @param count Number of ohlc data to get (e.g. 400).
 * @returns Candlestick data and last OHLC timestamp.
 */
export async function loadCandleStickData(
  symbol: string,
  interval: string,
  broker: string,
  timestamp?: number,
  count?: number
) {
  const candleStickStore = useCandleStickStore();

  let date;

  if (timestamp) {
    date = new Date(timestamp).toISOString();
  }

  let dataCount: number;

  if (count) {
    dataCount = count;
  } else {
    dataCount = timestamp
      ? DEFAULT_ADDITIONAL_LOAD_COUNT
      : DEFAULT_FIRST_LOAD_COUNT;
  }

  const candleStick: number[][] = [];
  let lastTimestamp = 0;

  const resp = await axios.get(`/ohlc/${interval}/${symbol}/${broker}`, {
    params: {
      timestamp: date,
      count: dataCount
    }
  });

  const message = resp.data.message;

  const ohlc: IOHLC[] = resp.data.data.ohlcData;

  if (ohlc.length === 0) {
    return {
      candleStick,
      last_timestamp: timestamp,
      count: dataCount,
      message
    };
  }

  if (timestamp) {
    // Removing last ohlc data since it will be avilable in new ohlc data.
    // This is done to remove duplicate data in candlestick.
    candleStickStore.ohlc.pop();

    candleStickStore.ohlc = [...candleStickStore.ohlc, ...ohlc];
  } else {
    candleStickStore.ohlc = ohlc;
  }

  for (let i = ohlc.length - 1; i >= 0; i--) {
    const utcDate = ohlc[i].timestamp;

    const timestamp = DateTime.fromISO(utcDate).toMillis();

    const temp = [
      timestamp,
      ohlc[i].open,
      ohlc[i].high,
      ohlc[i].low,
      ohlc[i].close,
      ohlc[i].tickVolume
    ];

    candleStick.push(temp);
  }

  const lastOHLCIdx = ohlc.length - 1;
  const lastUTCDate = ohlc[lastOHLCIdx].timestamp;

  lastTimestamp = DateTime.fromISO(lastUTCDate).toMillis();
  return {
    candleStick,
    last_timestamp: lastTimestamp,
    count: dataCount,
    message
  };
}
