import { TCoreData } from "../types";
import { BaseLine } from "./shapes/BaseLine";
import { BasicPoint } from "./base/Types";
import { DistanceUtils } from "./utils/DistanceUtils";
import { DrawUtils } from "./utils/DrawUtils";
import { IBaseShape } from "./shapes/BaseShape";
import { DrawUtils2 } from "./utils/DrawUtils2";
import { BaseShapeInterface } from "./base/shape.types";
import { BoxProperties, PreferenceUtils } from "./utils/PreferencesUtils";

// @ts-expect-error
export class Rectangle extends BaseLine implements BaseShapeInterface<"box"> {
  dragState: "tracking" | "settled" | "dragging" | "tracking-x" | "tracking-y" =
    "settled";
  dragging: boolean = false;
  draggingPoint: BasicPoint | null = null;
  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (line: IBaseShape) => boolean
  ) {
    const linePoints = points.length
      ? points
      : [
          { x: $core.cursor.x, y: $core.cursor.y },
          { x: $core.cursor.x, y: $core.cursor.y },
          { x: $core.cursor.x, y: $core.cursor.y },
          { x: $core.cursor.x, y: $core.cursor.y }
        ];
    super(
      $core,
      uuid,
      linePoints,
      points.length ? screenPoints : true,
      onSelect
    );
    this.type = "box";
    this.properties = PreferenceUtils["box"];
    if (screenPoints)
      $core.hub.events.emit("shape-draw-start", {
        shape: this,
        points: [
          { p: this.points[2], dir: null },
          { p: this.points[1], dir: "x" },
          { p: this.points[3], dir: "y" }
        ]
      });
  }
  get isValid(): boolean {
    return (
      this.points[0].x - this.points[2].x !== 0 &&
      this.points[0].y - this.points[2].y !== 0
    );
  }
  get screenPoints() {
    return this.points.map((point) => point.screen);
  }
  get endPoints() {
    return this.screenPoints;
  }
  get midPoint(): BasicPoint {
    const [p1, p2] = [this.points[0].screen, this.points[2].screen];
    return {
      x: (p1.x + p2.x) / 2,
      y: (p1.y + p2.y) / 2
    };
  }
  get details() {
    // Greater X index
    const prec = this.$core.meta.getAutoPrec(0, 0);
    const [lX, gX] =
      this.points[0].x > this.points[2].x
        ? [this.points[2].x, this.points[0].x]
        : [this.points[0].x, this.points[2].x];
    const [lY, gY] =
      this.points[0].y > this.points[2].y
        ? [this.points[2].y, this.points[0].y]
        : [this.points[0].y, this.points[2].y];
    const [s1, s2] = [this.points[0].screen, this.points[2].screen];
    const diff = (gY - lY).toFixed(prec);
    return {
      priceRange: `[${lY.toFixed(prec)}, ${gY.toFixed(prec)}]`,
      percentChange:
        (
          ((this.points[2].y - this.points[0].y) / this.points[2].y) *
          100
        ).toFixed(2) + "%",
      changeInPips: (((gY - lY) * 10 ** prec) / 10).toFixed(prec),
      diff: diff,
      barsRange: Math.floor(gX - lX),
      dateTimeRange: DistanceUtils.getTimeDifference(
        this.points[0].time,
        this.points[2].time
      ),
      distance: "ℓ : " + DistanceUtils.distance(s1, s2).toFixed(1) + "px",
      angle: "∠ : " + DistanceUtils.angleWithXAxis(s1, s2).toFixed(2) + "°"
    };
  }

  drawEndPoints(ctx: CanvasRenderingContext2D) {
    const points = this.endPoints;

    const lp = this.properties.lineProperties;
    const pd = this.properties.pointDisplay;
    const pp = this.properties.pointProperties;

    if (pp.point_position === "midpoint") return;

    const draw = (width: number) => {
      if (pp.point_shape === "circle") {
        points.forEach((p) =>
          DrawUtils2.drawCirclePoint(ctx, p, width, lp.line_color)
        );
      }
      if (pp.point_shape === "square") {
        points.forEach((p) => DrawUtils2.drawSquarePoint(ctx, p, width));
      }
      if (pp.point_shape === "arrow") {
        DrawUtils2.drawArrow(ctx, points[0], points[1], width);
      }
    };

    ctx.save();
    ctx.strokeStyle = lp.line_color;
    ctx.lineWidth = lp.line_width;
    if (this.selected && pd.show_point_selected) {
      const width = pp.point_width_selected;
      draw(width);
    } else if (this.hovered && pd.show_point_hover) {
      const width = pp.point_width_hover;
      draw(width);
    } else if (pd.show_point_default) {
      const width = pp.point_width_default;
      draw(width);
    }
    ctx.restore();
  }

  draw(ctx: CanvasRenderingContext2D): void {
    // const s1 = this.points[0].screen
    // const s3 = this.points[2].screen
    // DrawUtils.drawRect(ctx, s1, { w: s3.x - s1.x, h: s3.y - s1.y })
    // ctx.strokeRect(s1.x, s1.y, s3.x - s1.x, s3.y - s1.y)
    this.drawEndPoints(ctx);

    const fp = (this.properties as BoxProperties).backgroundProperties;

    ctx.fillStyle = fp.fill_color;

    const points = this.points;
    ctx.beginPath();
    ctx.moveTo(points[0].screenX, points[0].screenY);
    for (let i = 1; i < points.length; i++) {
      ctx.lineTo(points[i].screenX, points[i].screenY);
    }
    ctx.closePath();
    ctx.fill();

    const lp = this.properties.lineProperties;

    this.points.forEach((p, i) => {
      ctx.save();
      ctx.strokeStyle = lp.line_color;
      ctx.setLineDash(DrawUtils2.getLineDash(lp.line_type));
      ctx.lineWidth = lp.line_width;
      DrawUtils2.drawLine(ctx, p.screen, this.points[(i + 1) % 4].screen);
      ctx.restore();
    });
    // this.drawDetails(ctx)
  }

  detailsInfo: { [x: string]: boolean } = {};

  drawDetails(ctx: CanvasRenderingContext2D): void {
    if (!this.properties.labelProperties.show_label) return;

    const details = [];

    const {
      show_diff,
      show_percent_change,
      show_change_in_pips,
      show_bars_range,
      show_date_time_range,
      labelStyle
    } = this.properties.labelProperties;
    if (show_diff || show_percent_change || show_change_in_pips) {
      let txt = "";
      if (show_diff) txt += `${this.details.diff}  `;
      if (show_percent_change) txt += `${this.details.percentChange}  `;
      if (show_change_in_pips) txt += `${this.details.changeInPips}`;
      details.push(txt);
    }
    if (show_bars_range || show_date_time_range) {
      let txt = "";
      if (show_bars_range) txt += `${this.details.barsRange} bars `;
      if (show_date_time_range) txt += `${this.details.dateTimeRange}`;
      details.push(txt);
    }

    DrawUtils.drawDetails({
      ctx,
      details,
      styles: labelStyle,
      labelPosition: this.properties.labelProperties.label_position,
      p1: this.screenPoints[0],
      p2: this.screenPoints[2]
    });
  }
  /**
   * Check if mouse position is over the lines of rectangle
   */
  mouseOverLines(event: MouseEvent): boolean {
    const cursor = { x: event.offsetX, y: event.offsetY };
    const pairs = [];
    for (let i = 0; i < this.points.length; i++) {
      // Get the current point
      let p1 = this.points[i].screen;
      // Get the next point (or the first point if it's the last iteration)
      let p2 = this.points[(i + 1) % this.points.length].screen;
      pairs.push([p1, p2]);
    }

    if (pairs.some((p) => DistanceUtils.isCursorOnLine(p[0], p[1], cursor)))
      return true;
    return false;
  }
  mousedown(event: MouseEvent): void {
    const cursor = { x: event.offsetX, y: event.offsetY };

    if (this.mouseOverLines(event)) {
      this.onSelect(this);
      let dragPoint = false;
      for (let i = 0; i < this.points.length; i++) {
        const p = this.points[i];
        if (DistanceUtils.isCursorOnPoint(p.screen, cursor)) {
          p.startDragging();
          this.points[(i + 1) % 4].startDragging(i % 2 === 0 ? "y" : "x");
          this.points[(i + 3) % 4].startDragging(i % 2 === 0 ? "x" : "y");
          dragPoint = true;
          break;
        }
      }
      if (!dragPoint) {
        this.dragging = true;
        this.draggingPoint = { x: this.$core.cursor.x, y: this.$core.cursor.y };

        this.$core.hub.events.emit("scroll-lock", true);
      }
    } else {
      this.dragging = false;
      this.draggingPoint = null;
      // // this.selected = false
    }
    this.$core.hub.events.emit("update-layout");
  }
  mouseover(event: MouseEvent): void {
    if (this.mouseOverLines(event)) {
      this.hovered = true;
    } else {
      this.hovered = false;
    }
  }
  mousemove(event: MouseEvent): void {
    this.points.forEach((point) => point.mousemove(event));
    if (this.dragging && this.draggingPoint && this.selected) {
      const difference = {
        x: this.$core.cursor.x - this.draggingPoint.x,
        y: this.$core.cursor.y - this.draggingPoint.y
      };
      this.draggingPoint = {
        x: this.$core.cursor.x,
        y: this.$core.cursor.y
      };
      this.points.forEach((p) => {
        const screen = p.screen;
        // I don't know why the screen coordinates always come less than real values
        const x = screen.x + difference.x + 1;
        const y = screen.y + difference.y + 1;
        p.updatePosition({ x, y });
      });
    }
  }
  mouseup(event: MouseEvent): void {
    this.points.forEach((p) => {
      p.mouseup(event);
    });
    this.dragging = false;
    this.$core.hub.events.emit("scroll-lock", false);
  }
  keydown?(): void {
    // throw new Error("Method not implemented.")
  }
  getCoordinates(): { [x: string]: any } {
    return {
      diagonalEndpoint1: { row: this.points[0].x, price: this.points[0].y },
      diagonalEndpoint2: { row: this.points[2].x, price: this.points[2].y }
    };
  }
  setCoordinatesVal(name: string, value: any): boolean {
    if (value.row && value.price)
      switch (name) {
        case "diagonalEndpoint1": {
          this.points[0].x = Number(value.row);
          this.points[0].y = Number(value.price);
          this.points[3].x = Number(value.row);
          this.points[1].y = Number(value.price);
          return true;
        }
        case "diagonalEndpoint2": {
          this.points[2].x = Number(value.row);
          this.points[2].y = Number(value.price);
          this.points[1].x = Number(value.row);
          this.points[3].y = Number(value.price);
          return true;
        }
      }
    return false;
  }
}
