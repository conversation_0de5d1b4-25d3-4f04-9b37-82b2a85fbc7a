import path from "path";
import { defineConfig } from "vite";
import svgLoader from "vite-svg-loader";

// @ts-ignore
import { seo, viteRawPlugin } from "./src/utilities/plugins";
import vue from "@vitejs/plugin-vue";

export default defineConfig({
  plugins: [
    vue(),
    seo(),
    svgLoader(),
    viteRawPlugin({
      fileRegex: /\.navy$/
    })
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src/"),
      "@website": path.resolve(__dirname, "./src/pages/website/")
    }
  }
});
