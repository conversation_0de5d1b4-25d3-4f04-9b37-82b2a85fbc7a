<template>
  <div class="w-full max-w-6xl mx-auto px-4 py-12 bg-gray-50">
    <h2 class="text-3xl font-bold text-center text-gray-800 mb-8">
      SEE WHAT OUR MEMBERS ARE SAYING
    </h2>

    <div class="relative">
      <div class="overflow-hidden relative h-96">
        <div
          class="flex transition-transform duration-500 ease-in-out h-full"
          :style="{ transform: `translateX(-${activeIndex * 100}%)` }"
        >
          <div
            v-for="(testimonial, index) in testimonials"
            :key="index"
            class="w-full flex-shrink-0 px-4"
          >
            <div class="bg-white rounded-lg shadow-lg p-8 h-full flex flex-col">
              <div class="flex-grow">
                <p class="text-gray-700 italic mb-6">
                  "{{ testimonial.text }}"
                </p>
                <div class="flex items-center mb-2">
                  <svg
                    v-for="i in testimonial.rating"
                    :key="i"
                    class="w-5 h-5 text-yellow-400 fill-current"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"
                    />
                  </svg>
                </div>
              </div>
              <div>
                <p class="font-semibold text-gray-800">
                  {{ testimonial.name }}
                </p>
                <p class="text-gray-600">{{ testimonial.location }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation dots -->
      <div class="flex justify-center mt-6">
        <button
          v-for="(_, index) in testimonials"
          :key="index"
          @click="handleDotClick(index)"
          :class="[
            'w-3 h-3 mx-2 rounded-full',
            activeIndex === index ? 'bg-blue-600' : 'bg-gray-300'
          ]"
          :aria-label="`Go to slide ${index + 1}`"
        />
      </div>

      <!-- Navigation arrows -->
      <button
        class="absolute top-1/2 left-4 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-md text-gray-800 hover:bg-gray-100"
        @click="prevSlide"
      >
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M15 19l-7-7 7-7"
          />
        </svg>
      </button>

      <button
        class="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white rounded-full p-2 shadow-md text-gray-800 hover:bg-gray-100"
        @click="nextSlide"
      >
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M9 5l7 7-7 7"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from "vue";

interface Testimonial {
  text: string;
  name: string;
  location: string;
  rating: number;
}

const activeIndex = ref<number>(0);

const testimonials: Testimonial[] = [
  {
    text: "As a beginner, this has been a fantastic course. Andy is incredibly knowledgeable about the markets and is always willing to break down the fundamentals in an easy-to-understand way. What truly sets this course apart is the platform—it has all the necessary features to handle calculations for me, which makes it easy for a beginner.",
    name: "Christian R.",
    location: "United Kingdom",
    rating: 5
  },
  {
    text: "I'm so glad I joined this course! As a stay-at-home mom, I was looking to learn new skill and explore side income opportunities. The videos are accessible, and whenever I have questions, Andy is always available to help. My practice trades are sent to my virtual coach, who provides valuable feedback.",
    name: "Jasmine S.",
    location: "Canada",
    rating: 5
  },
  {
    text: "Great course and even better trading platform! After spending years trying to learn from influencers, I felt lost. When I joined the course, I finally understood forex. Andy took my knowledge to a new level with his deep understanding of macroeconomics. The trading platform is built around risk management—this is the real prize.",
    name: "Sebastian L.",
    location: "Canada",
    rating: 5
  }
];

let intervalId: number | null = null;

const handleDotClick = (index: number): void => {
  activeIndex.value = index;
};

const nextSlide = (): void => {
  activeIndex.value = (activeIndex.value + 1) % testimonials.length;
};

const prevSlide = (): void => {
  activeIndex.value =
    (activeIndex.value - 1 + testimonials.length) % testimonials.length;
};

onMounted(() => {
  // Auto-rotate
  intervalId = window.setInterval(() => {
    nextSlide();
  }, 5000);
});

onBeforeUnmount(() => {
  if (intervalId !== null) {
    window.clearInterval(intervalId);
  }
});
</script>
