/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{vue,js,ts}"],
  theme: {
    extend: {
      colors: {
        primary: "rgb(var(--color-primary) / <alpha-value>)",
        secondary: "rgb(var(--color-secondary) / <alpha-value>)",
        tertiary: "rgb(var(--color-tertiary) / <alpha-value>)",
        accent: "rgb(var(--color-accent) / <alpha-value>)",
        selected: "rgb(var(--color-selected) / <alpha-value>)",
        info: "rgb(var(--color-info) / <alpha-value>)",
        success: "rgb(var(--color-success) / <alpha-value>)",
        warning: "rgb(var(--color-warning) / <alpha-value>)",
        danger: "rgb(var(--color-danger) / <alpha-value>)",
        backdrop: "rgb(var(--color-backdrop) / <alpha-value>)"
      },
      fontFamily: {
        sans: ["Open Sans", "sans-serif"]
      }
    }
  },
  plugins: [require("flowbite/plugin"), require("@tailwindcss/forms")]
};
