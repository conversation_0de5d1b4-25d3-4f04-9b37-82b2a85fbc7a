import { useCandleStickStore } from "@/store/candleStickStore";
import { useChartStore } from "@/store/chartStore";
import { useTradeShapeStore } from "@/store/tradeShapeStore";

export function indexFromTime(
  range: { hi: number; lo: number; count: number },
  lastTimestamp: number
) {
  const { hi, lo, count } = range;

  // Calculate the time span and interval
  const timeSpan = hi - lo;
  const interval = timeSpan / (count - 1);

  // Calculate indices for lo, hi, and the given timestamp relative to lastTimestamp
  const loIndex = Math.round((lo - lastTimestamp) / interval);
  const hiIndex = Math.round((hi - lastTimestamp) / interval);

  return {
    loIndex,
    hiIndex
  };
}

interface NightVisionChart {
  range: [number, number];
  update(): void;
}

type ChartRange = [number, number];

export function animateChartRange(
  chart: NightVisionChart,
  targetRange: ChartRange,
  duration = 500
) {
  const startRange = [...chart.range];
  const startTime = performance.now();

  function animate(currentTime: number) {
    const elapsedTime = currentTime - startTime;
    const progress = Math.min(elapsedTime / duration, 1);

    const easeProgress = 1 - Math.pow(1 - progress, 3);

    const newRange: [number, number] = [
      startRange[0] + (targetRange[0] - startRange[0]) * easeProgress,
      startRange[1] + (targetRange[1] - startRange[1]) * easeProgress
    ];

    chart.range = newRange;
    chart.update();

    if (progress < 1) {
      requestAnimationFrame(animate);
    }
  }

  requestAnimationFrame(animate);
}

export function removeOrderShape(uuid: string) {
  const candleStickStore = useCandleStickStore();
  const chartStore = useChartStore();

  chartStore.shapeToolInstance?.removeOrder(uuid);

  const symbol = candleStickStore.symbol;
  const interval = candleStickStore.interval;

  const currentDataName = symbol + "_" + interval;
  chartStore.shapeToolInstance?.saveShapes(currentDataName);
  chartStore.shapeToolInstance?.saveShapes();
}

export function handleRemoveSelectedTradeShape() {
  const tradeShapeStore = useTradeShapeStore();

  if (tradeShapeStore.selectedTradeShape) {
    removeOrderShape(tradeShapeStore.selectedTradeShape.uuid);
    tradeShapeStore.selectedTradeShape = null;
  }
}
