<script setup lang="ts">
import { ref } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { useChartStore } from "@/store/chartStore";

import Dropdown from "@/components/Dropdown.vue";
import ColorPicker from "@/components/ColorPicker.vue";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"trend-line"> =
  chartStore.selectedShape!;

const lineColorDropdown = ref(false);
const selectedLineColor = ref(
  selectedShape.properties.lineProperties.line_color
);

function handleLineColor(lineColor: string) {
  selectedLineColor.value = lineColor;

  selectedShape.setProperty("lineProperties", "line_color", lineColor);

  chartStore.chart?.update();
}
</script>

<template>
  <Dropdown
    id="chart-modal-line-color-dropdown"
    toggle-id="chart-modal-line-color-toggle-dropdown"
    class="border"
    :class="{ 'border-info bg-accent': lineColorDropdown }"
    :icon="false"
    :offset-skidding="108"
    @show="lineColorDropdown = true"
    @hide="lineColorDropdown = false"
    v-if="selectedLineColor !== null"
  >
    <template #text>
      <FontAwesomeIcon icon="fa-solid fa-pen" />

      <div
        class="mt-0.5 h-1 w-6"
        :style="{ backgroundColor: selectedLineColor }"
      ></div>
    </template>

    <template #content>
      <ColorPicker :color="selectedLineColor" @update-color="handleLineColor" />
    </template>
  </Dropdown>
</template>
