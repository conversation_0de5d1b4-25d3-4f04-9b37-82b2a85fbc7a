import { DateTime } from "luxon";

/**
 * @param timezone To convert the date into specified timezone.
 * @returns UTC Offset in hours
 */
export function getTimeZoneOffsetInHours(timezone: string) {
  const desiredDate = DateTime.now().setZone(timezone);

  return desiredDate.offset / 60;
}

/**
 * Converts IANA Timezone to UTC offset
 * @param timezone IANA Timezone E.g. America/Toronto
 * @returns {string} Timezone offset in UTC E.g. UTC-4
 */
export function getUTCOffset(timezone: string) {
  const dt = DateTime.now().setZone(timezone);

  const offsetHours = dt.offset / 60;

  if (offsetHours === 0) {
    return "UTC";
  }

  const sign = offsetHours >= 0 ? "+" : "-";

  const formattedOffset = `UTC${sign}${Math.abs(offsetHours)}`;

  return formattedOffset;
}
