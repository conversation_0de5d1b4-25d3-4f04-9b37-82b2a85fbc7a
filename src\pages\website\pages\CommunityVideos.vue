<script setup lang="ts">
import { ref, useTemplateRef } from "vue";
import { onClickOutside } from "@vueuse/core";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import Select from "@/components/Select.vue";
import InputText from "@/components/InputText.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const videoModal = useTemplateRef("video-iframe");
const videoList = ref([
  {
    id: "aksosakasasasao",
    title: "Tradeawaay Expert Advisor Connection",
    description:
      "Connecting to the Tradeaway trading platform using the Tradeaway Expert Advisor.",
    duration: "1:32 mins",
    thumbnail: "https://img.youtube.com/vi/4pT8Np3at7Q/sddefault.jpg",
    category: "Introduction",
    link: "https://www.youtube.com/embed/4pT8Np3at7Q"
  },
  {
    id: "aksosakasabbsasao",
    title: "Tradeawaay: Trade Operations",
    description:
      "This is a how to video on how to open, modify and close market and pending orders in Tradewaay.",
    duration: "2:25 mins",
    thumbnail: "https://img.youtube.com/vi/lF1qMs7G0Cw/sddefault.jpg",
    category: "Forex Trading",
    link: "https://www.youtube.com/embed/lF1qMs7G0Cw"
  },
  {
    id: "aksosakao",
    title: "Risk Management Tools in Tradeawaay",
    description:
      "This video explores the different types of risks in day trading and demonstrates how Tradeaway assists its users in developing a strong risk management strategy.",
    duration: "3:25 mins",
    thumbnail: "https://img.youtube.com/vi/-ujC2f6deeY/sddefault.jpg",
    category: "Risk Trading",
    link: "https://www.youtube.com/embed/-ujC2f6deeY"
  },
  {
    id: "aksosakasasasao",
    title:
      "Master the math of passing a Prop Funding Challenge (Forex) by creating and executing a winning plan",
    description:
      "In this video, we’ll show you how to craft a winning trading plan, manage risk, and execute with confidence. Learn to ditch the bad habits—emotions, averaging trades, and overtrading—by building a solid game plan that keeps you on track.",
    duration: "13:00 mins",
    thumbnail: "https://img.youtube.com/vi/N18iemyD7Q0/sddefault.jpg",
    category: "Forex Trading",
    link: "https://www.youtube.com/embed/N18iemyD7Q0"
  },
  {
    id: "aksosakasasasao",
    title: "Simple Forex intraday strategy using daily price range theory",
    description:
      "Simple Forex intra day strategy using the daily price range theory. Check out how a range box plot on a chart can identify entry strategy and profit targets. ",
    duration: "5:05 mins",
    thumbnail: "https://img.youtube.com/vi/KyspyPPnHlo/sddefault.jpg",
    category: "Forex Trading",
    link: "https://www.youtube.com/embed/KyspyPPnHlo"
  }
]);
const searchVideo = ref("");
const selectedCategory = ref("trading");
const videoLink = ref("");

onClickOutside(videoModal, () => {
  videoLink.value = "";
});

function openVideo(link: string) {
  videoLink.value = link;
}
</script>

<template>
  <Teleport to="body">
    <template v-if="videoLink !== ''">
      <div
        class="fixed left-0 top-0 z-10 min-h-screen w-full bg-backdrop opacity-50"
      ></div>

      <div class="fixed right-4 top-4 z-20 text-white">
        <FontAwesomeIcon
          size="2xl"
          icon="fa-solid fa-xmark"
          class="cursor-pointer"
        />
      </div>

      <div class="fixed top-1/2 z-20 w-full -translate-y-1/2">
        <iframe
          ref="video-iframe"
          class="mx-auto h-[500px] w-3/4 2xl:h-[700px]"
          :src="videoLink"
        >
        </iframe>
      </div>
    </template>
  </Teleport>

  <div class="grow bg-accent pb-24 pt-10">
    <div class="container mx-auto">
      <div class="grid grid-cols-12">
        <h1 class="col-span-7 text-2xl font-bold">Community Videos</h1>

        <div class="col-span-5 grid grid-cols-12 gap-x-3">
          <InputText
            class="col-span-7 bg-white"
            placeholder="Search video..."
            v-model="searchVideo"
          />

          <Select
            class="col-span-5 bg-white"
            :default-option="false"
            v-model="selectedCategory"
          >
            <option value="trading">Trading</option>
            <option value="ea">EA</option>
            <option value="analytics">Analytics</option>
          </Select>
        </div>
      </div>

      <div class="mt-5 grid grid-cols-3 gap-8 2xl:grid-cols-4">
        <div
          class="cursor-pointer rounded-md border bg-white shadow-lg"
          v-for="video in videoList"
          :key="video.id"
          @click="openVideo(video.link)"
        >
          <div class="overflow-hidden">
            <img class="h-64 w-full" :src="video.thumbnail" />
          </div>

          <h3 class="mt-4 px-4 text-lg font-semibold"></h3>

          <div class="mt-2 flex items-center justify-between px-4 text-sm">
            <div class="rounded-lg bg-tertiary px-2 py-0.5 text-white">
              {{ video.category }}
            </div>

            <div class="flex items-center gap-x-1.5 text-gray-800">
              <FontAwesomeIcon icon="fa-regular fa-clock" />

              <span class="">{{ video.duration }}</span>
            </div>
          </div>

          <div class="mt-3 px-4 pb-3">
            <p class="line-clamp-3 text-sm text-gray-600">
              {{ video.description }}
            </p>
          </div>
        </div>
      </div>

      <div class="mt-16 flex justify-center text-sm">
        <PrimaryButton
          class="flex items-center gap-x-1.5 rounded-lg bg-tertiary px-3"
        >
          Load more
          <FontAwesomeIcon icon="fa-solid fa-arrow-right" />
        </PrimaryButton>
      </div>
    </div>
  </div>
</template>

<style>
main {
  display: flex;
  flex-direction: column;
}
</style>

<style scoped>
img:hover {
  transform: scale(1.1);
  transition: ease-in 0.2s;
}

img {
  transform: scale(1);
  transition: ease-out 0.2s;
}
</style>
