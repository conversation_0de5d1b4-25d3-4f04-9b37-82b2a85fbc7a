import { BasicPoint } from "../base/Types";
import { DistanceUtils } from "./DistanceUtils";
import { LabelPosition } from "./PreferencesUtils";

interface GetLabelPositionParams {
  p1: BasicPoint;
  p2: BasicPoint;
  bgHeight: number;
  bgWidth: number;
  position: LabelPosition;
}

export function getLabelPosition({
  p1,
  p2,
  bgHeight,
  bgWidth,
  position
}: GetLabelPositionParams): BasicPoint {
  const midX = (p1.x + p2.x) / 2;
  const midY = (p1.y + p2.y) / 2;
  const gapFromLine = 10;

  switch (position) {
    case "center":
      return getCenterLabelPosition(p1, p2);
    case "bottom":
      return { x: midX, y: Math.max(p1.y, p2.y) + 10 };
    case "left":
      return getLeftLabelPosition(p1, p2);
    case "right":
      return getRightLabelPosition(p1, p2);
    case "vertical-arrow":
      return getVerticalArrowPosition(p1, p2);
    case "horizontal-arrow":
      return getHorizontalArrowPosition(p1, p2);
    default:
      return { x: midX, y: midY };
  }

  function getLeftLabelPosition(
    p1: { x: number; y: number },
    p2: { x: number; y: number }
  ): { x: number; y: number } {
    let leftPoint;

    if (p1.x < p2.x) {
      leftPoint = p1;
    } else {
      leftPoint = p2;
    }

    let coord: { x: number; y: number } = {
      x: 0,
      y: 0
    };

    coord.x = leftPoint.x - bgWidth - gapFromLine;
    coord.y = leftPoint.y + bgHeight / 2;

    return coord;
  }

  function getRightLabelPosition(
    p1: { x: number; y: number },
    p2: { x: number; y: number }
  ): { x: number; y: number } {
    let rightPoint;

    if (p1.x > p2.x) {
      rightPoint = p1;
    } else {
      rightPoint = p2;
    }

    let coord: { x: number; y: number } = {
      x: 0,
      y: 0
    };

    coord.x = rightPoint.x + gapFromLine;
    coord.y = rightPoint.y + bgHeight / 2;

    return coord;
  }

  function getCenterLabelPosition(
    p1: { x: number; y: number },
    p2: { x: number; y: number }
  ): { x: number; y: number } {
    const midX = (p1.x + p2.x) / 2;
    const midY = (p1.y + p2.y) / 2;

    let angle;

    if (p2.x >= p1.x) {
      angle = DistanceUtils.angleWithXAxis(p1, p2);
    } else {
      angle = DistanceUtils.angleWithXAxis(p2, p1);
    }

    let coord: { x: number; y: number } = {
      x: 0,
      y: 0
    };

    const gap = 20;

    if (angle > 0 && angle < 45) {
      coord.x = midX - bgWidth;
      coord.y = midY - gap;
    } else if (Math.abs(angle) === 90) {
      coord.x = midX + gap;
      coord.y = midY - gap;
    } else if (angle > 45 && angle < 90) {
      coord.x = midX + gap;
      coord.y = midY + bgHeight;
    } else if (angle < -90 && angle > -135) {
      coord.x = midX + gap;
      coord.y = midY + bgHeight;
    } else {
      coord = { x: midX, y: midY - gap };
    }

    return coord;
  }

  function getVerticalArrowPosition(
    p1: { x: number; y: number },
    p2: { x: number; y: number }
  ): { x: number; y: number } {
    let coord: { x: number; y: number } = {
      x: p2.x - bgWidth / 2,
      y: 0
    };

    if (p1.y < p2.y) {
      coord.y = p2.y + gapFromLine + bgHeight;
    } else {
      coord.y = p2.y - gapFromLine;
    }

    return coord;
  }

  function getHorizontalArrowPosition(
    p1: { x: number; y: number },
    p2: { x: number; y: number }
  ): { x: number; y: number } {
    let coord: { x: number; y: number } = {
      x: 0,
      y: p2.y + bgHeight / 2
    };

    if (p1.x < p2.x) {
      coord.x = p2.x + gapFromLine;
      return coord;
    } else {
      coord.x = p2.x - gapFromLine - bgWidth;
      return coord;
    }
  }
}
