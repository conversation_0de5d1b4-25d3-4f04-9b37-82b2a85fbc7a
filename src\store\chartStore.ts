import { ref, shallowRef, watch } from "vue";

import { defineStore } from "pinia";
import { toast } from "vue3-toastify";

import { ShapeTool } from "@/lib/night-vision/groups/ShapeTool";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { animateChartRange } from "@/chart-frontend/utils";

import { ChartInstanceError } from "@/helpers/errors";
import { getMarketClosingTime } from "@/helpers/symbols";

import { loadCandleStickData } from "@/utilities/candle-sticks";

import {
  CandleOverlayProps,
  MarketCloseLinesOverlayProps
} from "@/types/overlays";

import { useCandleStickStore } from "./candleStickStore";
import { BoxPlotData } from "@/types";
import { NightVision } from "@pipclimber/night-vision";

type CandleStickInterval =
  | "PERIOD_M1"
  | "PERIOD_M5"
  | "PERIOD_M15"
  | "PERIOD_M30"
  | "PERIOD_H1"
  | "PERIOD_H4"
  | "PERIOD_D1"
  | "PERIOD_W1";

export const useChartStore = defineStore("chart", () => {
  const candleStickStore = useCandleStickStore();

  const intervalGapMap: { [key in CandleStickInterval]: number } = {
    // Leave 5 units of bars to the right
    PERIOD_M1: 5 * 60000,
    PERIOD_M5: 5 * 5 * 60000,
    PERIOD_M15: 5 * 15 * 60000,
    PERIOD_M30: 5 * 30 * 60000,
    PERIOD_H1: 5 * 60 * 60000,
    PERIOD_H4: 5 * 4 * 60 * 60000,
    PERIOD_D1: 5 * 24 * 60 * 60000,
    PERIOD_W1: 5 * 7 * 24 * 60 * 60000
  };

  const chartLoader = ref(false);
  const endOfData = ref(false);
  const chartWidth = ref(0);
  const chartHeight = ref(0);

  const chart = shallowRef<NightVision | null>(null);
  const selectedShape = shallowRef<IBaseShapeOptions<any> | undefined>(
    undefined
  );
  const shapeToolInstance = shallowRef<ShapeTool | null>(null);
  const isShapeToolSelected = ref(false);
  const isShapeToolLocked = ref(false);
  const chartModal = ref(false);
  const chartModalPosition = ref({
    top: 48,
    left: 51
  });
  const initialRange = ref<[number, number] | null>(null);
  const shapeToolbarPosition = ref({
    top: 69,
    left: window.innerWidth / 2
  });

  // watch(chartWidth, (newWidth) => {
  //   if (chart.value) {
  //     chart.value.width = newWidth;
  //   }
  // });

  // watch(chartHeight, (newHeight) => {
  //   if (chart.value) {
  //     chart.value!.height = newHeight;
  //   }
  // });

  async function changeCurrentSymbol(symbol: string) {
    if (!chart.value) {
      return;
    }

    if (candleStickStore.symbol === symbol) {
      return;
    }

    try {
      chartLoader.value = true;

      let newSymbol = symbol;
      let newBroker = "";

      if (
        candleStickStore.marketWatchOnlineStatus &&
        candleStickStore.brokerInfo
      ) {
        if (symbol in candleStickStore.brokerInfo.mapping_symbols) {
          newSymbol =
            candleStickStore.brokerInfo.mapping_symbols[symbol].symbol;
          newBroker =
            candleStickStore.brokerInfo.mapping_symbols[symbol].broker_chart;
        } else {
          newSymbol = symbol.match(/[A-Z0-9]/g)?.join("") || "";
          newBroker = candleStickStore.brokerInfo.forex_chart_broker;
        }
      } else {
        candleStickStore.symbolList.forEach((w) => {
          w.instruments.forEach((x) => {
            if (x === newSymbol) {
              newBroker = w.broker;
            }
          });
        });
      }

      let candleStick: number[][];

      try {
        const result = await loadCandleStickData(
          newSymbol,
          candleStickStore.interval,
          newBroker
        );
        candleStick = result.candleStick;
      } catch (_e) {
        toast.error("Cannot change symbol");
        throw new Error(`Cannot load candlestick data`);
      }

      // Box Plot
      closeBoxPlot();

      candleStickStore.symbol = newSymbol;
      candleStickStore.symbolBroker = newBroker;

      candleStickStore.storeChartSettings({
        symbol: newSymbol,
        symbol_broker: newBroker
      });

      chart.value.hub.mainOv.name = symbol;
      chart.value.hub.mainOv.data = candleStick;

      updateMarketClosingLines(candleStick);

      chart.value.fullReset();

      resetXZoom();
      resetYZoom();

      endOfData.value = false;

      updateMarketClosingTime();
    } catch (e) {
      console.error(e);
    } finally {
      chartLoader.value = false;
    }
  }

  function toggleModal(modal: boolean) {
    chartModal.value = modal;
  }

  function setChartModalPosition(top: number, left: number) {
    chartModalPosition.value = {
      top,
      left
    };
  }

  function setShapeToolbarPosition(top: number, left: number) {
    shapeToolbarPosition.value = {
      top,
      left
    };
  }

  function setInitialRange(range: [number, number]) {
    initialRange.value = range;
  }

  const resetYZoom = () => {
    const paneId = 0;
    const scaleId = "A";

    if (chart.value) {
      if (chart.value.meta.yTransforms[paneId]) {
        chart.value.meta.yTransforms[paneId][scaleId] = null;
      }

      chart.value.update();
    }
  };

  const resetXZoom = () => {
    if (chart.value) {
      const interval = candleStickStore.interval as CandleStickInterval;

      const mainPane = chart.value.data.panes[0];
      const mainOverlay = mainPane.overlays.find((o: any) => o.main);

      if (!mainOverlay || !mainOverlay.data || mainOverlay.data.length === 0) {
        console.error("Main overlay data is not available");
        return;
      }

      const dataLength = mainOverlay.data.length;

      if (chart.value && initialRange.value) {
        let endPoint, startPoint;
        if (chart.value.hub.indexBased) {
          const initialDiff = initialRange.value[1] - initialRange.value[0];
          endPoint = dataLength + 3;
          startPoint = endPoint - initialDiff;
        } else {
          endPoint =
            mainOverlay.data[dataLength - 1][0] + intervalGapMap[interval];
          startPoint = endPoint - intervalGapMap[interval] * 10;
        }
        animateChartRange(chart.value, [startPoint, endPoint], 500);
      } else {
        console.error("Chart or initial range is not available");
      }
    }
  };

  const handleZoomReset = () => {
    resetYZoom();
    resetXZoom();
  };

  /**
   * Creates a temporary canvas for manipulation.
   * @returns A temporary canvas element.
   */
  function createTemporaryCanvas() {
    if (!chart.value) {
      throw new Error("Error: Chart not intialized.");
    }

    const canvases = document.querySelectorAll("canvas");

    if (!canvases.length) {
      throw new Error("No canvas elements found");
    }

    const mainCanvas = canvases[0];
    const rightBar = canvases[1];
    const bottomBar = canvases[2];

    // Create a temporary canvas to handle background
    const tempCanvas = document.createElement("canvas");
    const padding = 40;
    tempCanvas.width = mainCanvas.width + (rightBar?.width || 0) + padding * 2;
    tempCanvas.height =
      mainCanvas.height + (bottomBar?.height || 0) + padding * 2;

    const ctx = tempCanvas.getContext("2d");

    if (!ctx) {
      throw new Error("Error in getting 2D context.");
    }

    ctx.fillStyle = "#ffffff";
    ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

    ctx.drawImage(mainCanvas, padding, padding);

    if (bottomBar) {
      ctx.drawImage(bottomBar, padding, mainCanvas.height + padding);
    }

    if (rightBar) {
      ctx.drawImage(rightBar, mainCanvas.width + padding, padding);
    }

    ctx.strokeStyle = "gray";
    ctx.strokeRect(
      padding,
      padding,
      mainCanvas.width + rightBar.width,
      mainCanvas.height + padding
    );

    const symbol = candleStickStore.symbol;
    const dateString = new Date().toUTCString();
    ctx.fillStyle = "black";
    ctx.font = "16px Arial";
    ctx.fillText(`${symbol} ${dateString}`, padding, 30);
    ctx.fillText(
      "Powered by Tradeaway",
      padding,
      mainCanvas.height + 2 * padding + 20
    );

    return tempCanvas;
  }

  /**
   * @returns Base64 encoded string representing the image of the chart.
   */
  function getChartImage() {
    if (!chart.value) {
      throw new Error("Error: Chart not intialized.");
    }

    const { id } = chart.value;

    const canvas = document.querySelector(`#${id} canvas`) as HTMLCanvasElement;

    if (!canvas) {
      throw new Error("Error: Canvas element not found.");
    }

    const tempCanvas = createTemporaryCanvas();

    if (!tempCanvas) {
      throw new Error("Error: Failed to create temporary canvas.");
    }

    const dataUrl = tempCanvas.toDataURL("image/png", 1);

    return dataUrl;
  }

  /**
   *
   * @returns A Promise that resolves with a Blob representing the image of the chart.
   */
  async function getChartImageBlob() {
    const tempCanvas = createTemporaryCanvas();

    if (!tempCanvas) {
      throw new Error("Error: Failed to create temporary canvas.");
    }

    const blob = await new Promise<Blob>((resolve, reject) => {
      tempCanvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error("Error: Canvas to Blob conversion failed."));
          }
        },
        "image/png",
        1
      );
    });

    return blob;
  }

  /**
   * Updates OHLC candlestick data for market closing line script.
   * @param candlestick New candlestick data.
   */
  function updateMarketClosingLines(candleStick: number[][]) {
    if (!chart.value) {
      return;
    }

    const marketCloseOverlay = chart.value.hub.chart.overlays.find(
      (o: any) => o.name === "Market Close Lines"
    );

    if (!marketCloseOverlay) {
      throw new Error("Error updating market close line.");
    }

    marketCloseOverlay.data = candleStick;
  }

  function setBoxPlotValues(
    show: boolean,
    boxPlotType: "box_plot_high" | "box_plot_low",
    boxPlotData?: BoxPlotData
  ) {
    if (!chart.value) {
      throw new ChartInstanceError();
    }

    const script = chart.value.hub.chart.overlays.find(
      (o: any) => o.type === "MarketCloseLines"
    );

    if (!script) {
      throw new Error("Market closing script not found.");
    }

    const props = script.props as MarketCloseLinesOverlayProps;

    if (!show) {
      if (boxPlotType === "box_plot_high") {
        props["boxPlotHigh"] = false;
      } else {
        props["boxPlotLow"] = false;
      }
      return;
    }

    if (!boxPlotData) {
      throw new Error("No box plot data found");
    }

    const { highValues, lowValues } = boxPlotData;

    props["highMin"] = highValues.high_min;
    props["highMax"] = highValues.high_max;
    props["highMedian"] = highValues.high_median;
    props["highQ1"] = highValues.high_q1;
    props["highQ3"] = highValues.high_q3;
    props["lowMin"] = lowValues.low_min;
    props["lowMax"] = lowValues.low_max;
    props["lowMedian"] = lowValues.low_median;
    props["lowQ1"] = lowValues.low_q1;
    props["lowQ3"] = lowValues.low_q3;

    if (boxPlotType === "box_plot_high") {
      props["boxPlotHigh"] = true;
    } else {
      props["boxPlotLow"] = true;
    }

    chart.value.update();
  }

  function closeBoxPlot() {
    candleStickStore.boxPlot.boxPlotHigh = false;
    setBoxPlotValues(false, "box_plot_high");

    candleStickStore.boxPlot.boxPlotLow = false;
    setBoxPlotValues(false, "box_plot_low");
  }

  /**
   * Toggles market close lines in the graph.
   * @param show State to toggle market close lines.
   */
  function toggleMarketClosingLines(
    show: boolean,
    market: "ny" | "as" | "ln" | "close"
  ) {
    const script = chart.value?.hub.chart.overlays.find(
      (o: any) => o.type === "MarketCloseLines"
    );

    if (!script || !script.props) {
      throw new Error("Market closing script not found.");
    }

    const props = script.props as MarketCloseLinesOverlayProps;

    if (market === "ny") {
      props["nyVisible"] = show;
    } else if (market === "as") {
      props["asiaVisible"] = show;
    } else if (market === "ln") {
      props["londonVisible"] = show;
    } else if (market === "close") {
      props["visible"] = show;
    }

    if (!chart.value) {
      throw new Error("Chart instance not found");
    }

    chart.value.update();
  }

  /**
   * Updates the market closing time
   */
  function updateMarketClosingTime() {
    const script = chart.value?.hub.chart.overlays.find(
      (o: any) => o.type === "MarketCloseLines"
    );

    if (!script || !script.props) {
      throw new Error("Market closing script not found.");
    }

    const props = script.props as MarketCloseLinesOverlayProps;

    const time = getMarketClosingTime();

    props["closeHour"] = time.closing;
    props["nyHour"] = time.newYork;
    props["asiaHour"] = time.asia;
    props["londonHour"] = time.london;
  }

  /**
   * Toggles volume bars drawing in the chart
   */
  function toggleVolume(value?: boolean) {
    if (!chart.value) {
      throw new Error("Chart instance not found");
    }

    const dataOverlay = chart.value.data.panes[0].overlays.find(
      (o: any) => o.type === "Candles"
    );

    if (!dataOverlay) {
      throw new Error("Candles overlay not found");
    }

    const props = dataOverlay.props as CandleOverlayProps;

    if (value) {
      props.showVolume = value;
    } else {
      props.showVolume = !props.showVolume;
    }

    chart.value.update();
  }

  return {
    chartLoader,
    chartWidth,
    chartHeight,
    chart,
    selectedShape,
    shapeToolInstance,
    isShapeToolSelected,
    isShapeToolLocked,
    shapeToolbarPosition,
    chartModal,
    chartModalPosition,
    initialRange,
    endOfData,
    changeCurrentSymbol,
    toggleModal,
    setShapeToolbarPosition,
    setChartModalPosition,
    setInitialRange,
    resetYZoom,
    handleZoomReset,
    getChartImage,
    getChartImageBlob,
    updateMarketClosingLines,
    toggleMarketClosingLines,
    toggleVolume,
    setBoxPlotValues,
    closeBoxPlot
  };
});
