import { TCoreData } from "../types";
import {
  ILabelInfo,
  NewOrder,
  OrderData,
  OrderExecution,
  OrderType
} from "./NewOrder";

interface SelectedOrderOptions {
  orderData: OrderData;
  orderType: OrderType;
  orderExecution: OrderExecution;
  slLabel: ILabelInfo;
  tpLabel: ILabelInfo;
  tp1Label?: ILabelInfo;
}

export class SelectedOrder extends NewOrder {
  constructor(
    $core: TCoreData,
    uuid: string,
    {
      orderData,
      orderExecution,
      orderType,
      slLabel,
      tpLabel,
      tp1Label
    }: SelectedOrderOptions
  ) {
    super($core, uuid, { orderData, orderType, orderExecution, fixedX: true });

    this.isNewOrder = false;

    this.setName("current-order");

    this.slLabelInfo = slLabel;
    this.tpLabelInfo = tpLabel;

    this.updatePrice(orderData.price);
    if (orderExecution !== "order") {
      this.priceHLine.draggable = false;
    }

    this.updateStopLoss(orderData.stopLoss);
    this.stopLossHLine.draggable = true;
    this.stopLossHLine.labelInfo = slLabel;

    this.updateTakeProfit(orderData.takeProfit);
    this.takeProfitHLine.draggable = true;
    this.takeProfitHLine.labelInfo = tpLabel;

    if (orderData.takeProfit1 && tp1Label) {
      this.updateTakeProfit1(orderData.takeProfit1);
      this.takeProfit1HLine.labelInfo = tp1Label;
      this.takeProfit1HLine.draggable = true;
    }

    this.computeRatioLabel();

    this.$core.hub.events.emit("update-layout");
  }

  mouseup(event: MouseEvent): void {
    super.mouseup(event);

    const orderData: OrderData = {
      price: this.priceHLine.points[0].y,
      stopLoss: this.stopLossHLine.points[0].y,
      takeProfit: this.takeProfitHLine.points[0].y
    };

    this.$core.hub.events.emitSpec(
      "shapetool",
      "order-data-updated",
      orderData
    );
  }
}
