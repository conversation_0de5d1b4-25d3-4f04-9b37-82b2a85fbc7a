div<script setup lang="ts">
import { nextTick, ref, watch, PropType } from 'vue';

import { Camera, SendHorizontal, Smile, AtSign, X } from "lucide-vue-next";
import { axios } from "@/api";
// import { axios, chatAxios } from "@/api";

interface User {
  _id: string;
  name: string;
  user_name: string;
}

import { useChartStore } from "@/store/chartStore";
const chartStore = useChartStore();
import { Message } from '@/types/chat';
const emit = defineEmits(["send-message", "user-selected", "parentUpdated","image-copied","type-of-media-updated"]);
const messageText = ref("");
const textarea = ref<HTMLTextAreaElement>();
const debounceDelay = 300;
let debounceTimeout: ReturnType<typeof setTimeout> | undefined = undefined;
const mentionedUsers = ref<{id: string, username: string}[]>([]);
const file  = ref<File | null>(null);
const previewURL  = ref<string | null>(null);
const showMentionSuggestions = ref(false);
const mentionSearchResults = ref<User[]>([]);
const mentionQuery = ref("");
const mentionStartPosition = ref(0);


const selectedMentionIndex = ref(0);

const props = defineProps({
  atTheRateActive: {
    type: Boolean,
    required: true
  },
  userTag: {
    type: String,
    required: true,
  },
  setUserTag: {
    type: Function,
    required: true,
  },
  ActiveUserForMessageSearch: {
    type: String,
    required: true
  },
  setActiveUserForMessageSearch: {
    type: Function,
    required: true
  },
  mode: {
    type: String,
    required: true
  },
  setMode: {
    type: Function,
    required: true
  },
  userSearchResults: {
    type: Array,
    default: () => [],
    required: false
  },
 parentMessage: {
    type: Object as PropType<Message | null>,
    required: false,
  },
  fileInChat: {
    type: Object as PropType<File | null>,
    required: false,
  },
  typeOfMedia: {
    type: String,
    required: false,
  },
toggleTypeOfMedia: {
    type: Function,
    required: false,
  }
});

// Toggle between modes
// const toggleMode = () => {
//   if (props.mode === "self") {
//     props.setMode("regular");
//   } else if (props.mode === "other") {
//     props.setMode("self");
//   } else {
//     props.setMode("self");
//   }
// };
// watch( () => props.userTag, async (newUserTag:string) => {
//   console.log("userTag value in MessageInput", newUserTag);
//   if(props.userTag.startsWith("@")){
//     // props.setUserTag({ target: { value: '' } } as Event);
//     const {data} =await axios.get(`/user/search?user_name=${newUserTag.slice(1)}`);
//     console.log(data);
//     handleUserMentions(data.data);
//   }
// });
// const handleUserMentions = (data: any) => {

//   console.log(data);
// };
const sendMessage = () => {
  if (messageText.value.trim()) {
    emit("send-message", messageText.value);
    messageText.value = "";
    mentionedUsers.value = [];
    resetTextareaHeight();
  }
};

const autoResize = async () => {
  await nextTick();
  if (textarea.value) {
    textarea.value.style.height = "auto";
    textarea.value.style.height =
      Math.min(textarea.value.scrollHeight, 120) + "px";
  }
};

const resetTextareaHeight = async () => {
  await nextTick();
  if (textarea.value) {
    textarea.value.style.height = "auto";
  }
};

// Handle input for the textarea with @ detection
const handleInput = (e: Event) => {
  const inputValue = (e.target as HTMLTextAreaElement).value;
  messageText.value = inputValue;
  autoResize();

  // Check for @ mentions in the text
  checkForMentions(inputValue);
};

// Function to check for @ mentions in text with debouncing
const checkForMentions = (text: string) => {
  if (!textarea.value) return;

  const cursorPos = textarea.value.selectionStart;
  const textBeforeCursor = text.substring(0, cursorPos);

  // Find the last @ symbol before cursor
  const lastAtIndex = textBeforeCursor.lastIndexOf('@');

  // If there's an @ and it's either at the start or has a space before it
  if (lastAtIndex >= 0 && (lastAtIndex === 0 || textBeforeCursor[lastAtIndex - 1] === ' ')) {
    const query = textBeforeCursor.substring(lastAtIndex + 1);

    if (query && !query.includes(' ')) {
      mentionQuery.value = query;
      mentionStartPosition.value = lastAtIndex;

      clearTimeout(debounceTimeout);

      debounceTimeout = setTimeout(async () => {
        try {
          const { data } = await axios.get(`/user/search?user_name=${query}`);
          mentionSearchResults.value = data.data || [];
          showMentionSuggestions.value = true; // Always show dropdown, even if empty
        } catch (error) {
          console.error('Error searching users:', error);
          mentionSearchResults.value = [];
          showMentionSuggestions.value = false;
        }
      }, debounceDelay);
    } else {
      showMentionSuggestions.value = false;
    }
  } else {
    showMentionSuggestions.value = false;
  }
};
// for @ active and nischal
const insertMention = (user: User) => {
  if (!textarea.value) return;


  mentionedUsers.value.push({
    id: user._id,
    username: user.user_name
  });

  // Get cursor position
  const cursorPos = textarea.value.selectionStart;
  const textBefore = messageText.value.substring(0, cursorPos);
  const textAfter = messageText.value.substring(cursorPos);

  // Insert the mention
  const mentionText = `@${user.user_name} `;
  messageText.value = textBefore + mentionText + textAfter;

  // Close the dropdown
  props.setUserTag({ target: { value: '' } } as Event);

  // Focus back on textarea and set cursor position after the inserted mention
  nextTick(() => {
    if (textarea.value) {
      textarea.value.focus();
      const newCursorPos = textBefore.length + mentionText.length;
      textarea.value.setSelectionRange(newCursorPos, newCursorPos);
      autoResize();
    }
  });
};

// for @nischal without @ active
const insertInlineMention = (user: User) => {
  if (!textarea.value) return;

  mentionedUsers.value.push({
    id: user._id,
    username: user.user_name
  });

  const cursorPos = textarea.value.selectionStart;
  const textBeforeCursor = messageText.value.substring(0, cursorPos);
  const textAfterCursor = messageText.value.substring(cursorPos);

  const lastAtIndex = textBeforeCursor.lastIndexOf('@');

  // Only proceed if we found an @ symbol
  if (lastAtIndex >= 0) {
    // Get text before the @ symbol
    const textBeforeMention = messageText.value.substring(0, lastAtIndex);

    // Insert the mention
    const mentionText = `@${user.user_name} `;
    messageText.value = textBeforeMention + mentionText + textAfterCursor;

    showMentionSuggestions.value = false;

    // Focus back on textarea and set cursor position after the inserted mention
    nextTick(() => {
      if (textarea.value) {
        textarea.value.focus();
        const newCursorPos = lastAtIndex + mentionText.length;
        textarea.value.setSelectionRange(newCursorPos, newCursorPos);
        autoResize();
      }
    });
  }
};

// Select a user from search results to view their messages

// Handle keyboard events for dropdown navigation
const handleKeyDown = (e: KeyboardEvent) => {
  if (showMentionSuggestions.value && mentionSearchResults.value.length > 0) {
    if (e.key === 'ArrowDown') {
      e.preventDefault(); // Prevent default scrolling
      // Move selection down (with wrap-around)
      selectedMentionIndex.value = (selectedMentionIndex.value + 1) % mentionSearchResults.value.length;
      scrollToSelectedMention();
    } else if (e.key === 'ArrowUp') {
      e.preventDefault(); // Prevent default scrolling
      // Move selection up (with wrap-around)
      selectedMentionIndex.value = selectedMentionIndex.value <= 0 
        ? mentionSearchResults.value.length - 1 
        : selectedMentionIndex.value - 1;
      scrollToSelectedMention();
    } else if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      // Insert the currently selected mention
      insertInlineMention(mentionSearchResults.value[selectedMentionIndex.value]);
    } else if (e.key === 'Escape') {
      showMentionSuggestions.value = false;
    } else if (e.key === 'Tab') {
      e.preventDefault();
      insertInlineMention(mentionSearchResults.value[selectedMentionIndex.value]);
    }
  } else if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault();
    sendMessage();
  }
};
//some javascript code to always keep the selected item in view
const scrollToSelectedMention = () => {
  nextTick(() => {
    const mentionsList = document.querySelector('.mention-suggestions-list');
    const selectedItem = mentionsList?.querySelector(`[data-index="${selectedMentionIndex.value}"]`);
    
    if (selectedItem && mentionsList) {
      // Calculate if the element is outside the visible area
      const containerRect = mentionsList.getBoundingClientRect();
      const elementRect = selectedItem.getBoundingClientRect();
      
      if (elementRect.bottom > containerRect.bottom) {
        // If element is below visible area
        selectedItem.scrollIntoView({ block: 'end', behavior: 'smooth' });
      } else if (elementRect.top < containerRect.top) {
        // If element is above visible area
        selectedItem.scrollIntoView({ block: 'start', behavior: 'smooth' });  
      }
    }
  });
};

///////////////// for copy trading

async function copyChartImage() {
  try {
    const blob = await chartStore.getChartImageBlob();

    // const clipboardItem = new ClipboardItem({
    //   [blob.type]: blob
    // });

    // await navigator.clipboard.write([clipboardItem]);
    console.log("Image copied to clipboard.");
    //set file 
    file.value = new File([blob], `chart_${Date.now()}.png`, { type: blob.type });
    emit("image-copied", file.value);
   
  } catch (_e) {
    console.log("error copying chart image to clipboard:", _e);
  }
}
watch(file, (newFile) => {
  console.log("File changed:", newFile);
  if (newFile) {
    // Create a preview URL for the file
    previewURL.value = URL.createObjectURL(newFile);
  } else {
    // Clear the preview URL when file is removed
    previewURL.value = null;
  }
});
// Reset selected index when mention results change
watch(mentionSearchResults, () => {
  selectedMentionIndex.value = 0;
});
</script>

<template>
  <div class="bg-white p-1">
    <div v-if="fileInChat!=null">
      <span @click="emit('type-of-media-updated', typeOfMedia=='normal'?'copyTrade':'normal')" class="hover:bg-gray-200 p-1 rounded mb-4">{{ typeOfMedia }}</span>
      <div class="flex items-center justify-between mb-2 px-4">
        <div class="flex items-center gap-2">

          <img v-if="fileInChat?.type.startsWith('image/')" :src="previewURL || URL.createObjectURL(fileInChat)" alt="Preview" class="w-12 h-12 object-cover rounded" />
          <span class="font-medium">{{ fileInChat?.name }}</span>
        </div>
        <button @click="emit('image-copied',null)" class=" hover:bg-gray-200 p-1 rounded">
          <X class="h-6 w-6" />
        </button>
      </div>
    </div>
  <div v-if="parentMessage!=null" class="mb-2 py-2 px-4 bg-gray-100 rounded-lg flex justify-between">
    <div>

      <div >Replying to <span class="font-medium">{{ parentMessage.sender.user_name }}</span></div>
      <p>{{ parentMessage.content.slice(0, 100) }}{{ parentMessage.content.length > 100 ? '...' : '' }}</p>
    </div>
    <X class="h-6 w-6 cursor-pointer " @click="emit('parentUpdated',null)"/>
  </div>
    <div class="flex items-center gap-3">
      <div class="flex-1 relative">
        <!-- Main textarea for message input -->
        <textarea
          v-model="messageText"
          @input="handleInput"
          placeholder="Say Something"
          rows="1"
          class="w-full px-4 py-3 bg-gray-50 border-1 border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-blue-500 focus:bg-white transition-all duration-200 placeholder-gray-500 resize-none overflow-y-auto"
          @keydown="handleKeyDown"
          ref="textarea"
        ></textarea>

        <!-- User search results when @ is active -->
        <!-- <div v-if="atTheRateActive && mentionSearchResults.length > 0"
             class="absolute z-50 bottom-full mb-1 w-full max-h-40 overflow-y-auto bg-white rounded-lg shadow-lg border border-gray-200 mention-suggestions-list">
          <div v-for="(user, index) in mentionSearchResults"
               :key="user._id"
               @click="insertMention(user)"
               :class="['px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center', { 'bg-gray-100': index === selectedMentionIndex }]"
               :data-index="index">
            <div class="w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center mr-2">
              {{ user.name.charAt(0).toUpperCase() }}
            </div>
            <span class="font-medium">{{ user.name }}</span>
            <span class="text-gray-500 text-sm ml-2">@{{ user.user_name }}</span>
          </div>
          <div v-if="mentionSearchResults.length === 0" class="px-4 py-2 text-gray-500 italic">
            No users found
          </div>
        </div> -->

        <!-- Inline mention suggestions (for @ typed in text) -->
        <div v-if="!atTheRateActive && showMentionSuggestions"
             class="absolute z-50 bottom-full mb-1 w-full max-h-40 overflow-y-auto bg-white rounded-lg shadow-lg border border-gray-200 mention-suggestions-list">
          <div v-for="(user, index) in mentionSearchResults"
               :key="user._id"
               @click="insertInlineMention(user)"
               :class="['px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center', { 'bg-gray-100': index === selectedMentionIndex }]"
               :data-index="index">
            <div class="w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center mr-2">
              {{ user.name.charAt(0).toUpperCase() }}
            </div>
            <span class="font-medium">{{ user.name }}</span>
            <span class="text-gray-500 text-sm ml-2">@{{ user.user_name }}</span>
          </div>
          <div v-if="mentionSearchResults.length === 0" class="px-4 py-2 text-gray-500 italic">
            No users found
          </div>
        </div>
      </div>
      <!-- Send Button -->
      <button
        @click="sendMessage"
        :disabled="!messageText.trim()"
        class="p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200"
        aria-label="Send message"
      >
        <SendHorizontal  />
      </button>
    </div>

    <!-- Bottom Row - Action Buttons and @ Toggle -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <!-- @ Button with active state -->
        <!--  -->
        
        <!-- Emoji Button -->
        <button
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
          aria-label="Add emoji"
        >
          <Smile class="size-5" />
        </button>

        <!-- Camera Button -->
        <button @click="() => {  copyChartImage();}"
          class="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
          aria-label="Take photo"
        >
          <Camera class="size-5" />
        </button>
      </div>

      <!-- User search input when @ is active -->
    </div>
  </div>
</template>
