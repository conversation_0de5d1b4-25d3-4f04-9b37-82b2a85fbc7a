<script setup lang="ts">
import { PropType, computed, onMounted, onUnmounted, ref, watch } from "vue";
import { MoreVertical, Trash2,Reply, ReplyIcon } from "lucide-vue-next";
import { useUserStore } from "@/store/userStore";
import getInitials from "@/helpers/getInitials";
import { Message } from "@/types/chat";

const userStore = useUserStore();

const { messages,parentMessage } = defineProps({
  messages: {
    type: Array as PropType<Message[]>,
    required: true
  },
  parentMessage: {
    type: Object as PropType<Message | null>,
    required: false,
  },
  search: {
    type: String,
    required: false,
  }
});

console.log("parent message in message area", parentMessage);
const emit = defineEmits(['update:search', 'message-deleted', 'parent-updated', 'delete-request']);
const chatContainer = ref<HTMLElement | null>(null);
const userHasScrolled = ref(false);
const activeMessageMenu = ref<string | null>(null);

// Function to toggle message menu
const toggleMessageMenu = (messageId: string) => {
  if (activeMessageMenu.value === messageId) {
    activeMessageMenu.value = null;
  } else {
    activeMessageMenu.value = messageId;
  }
};

// Function to close menu when clicking outside
const closeMenuOnOutsideClick = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (!target.closest('.message-menu') && !target.closest('.message-menu-button')) {
    activeMessageMenu.value = null;
  }
};

// Add event listener for outside clicks
onMounted(() => {
  document.addEventListener('click', closeMenuOnOutsideClick);
});

onUnmounted(() => {
  document.removeEventListener('click', closeMenuOnOutsideClick);
});

// Update the delete function to emit a request
const deleteMessage = async (messageId: string) => {
  emit('delete-request', messageId);
};

// Check if the current user is the message sender
const isCurrentUserMessage = (senderId: string) => {
  return userStore.user && userStore.user._id === senderId;
};

const formatTime = (timestamp:any) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false
  });
};

const formatDate = (date:any) => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (isSameDay(date, today)) {
    return "Today";
  } else if (isSameDay(date, yesterday)) {
    return "Yesterday";
  } else {
    // Format like "Monday, May 5th"
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric"
    });
  }
};

const isSameDay = (date1:any, date2:any) => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

const isDifferentDay = (date1:any, date2:any) => {
  return !isSameDay(date1, date2);
};

const messagesWithDaySeparators = computed(() => {
  if (!messages.length) return [];

  const result:any = [];
  let lastDate: Date | null = null;

  const sortedMessages = [...messages].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  sortedMessages.forEach((message, index) => {
    const messageDate = new Date(message.timestamp);
    const currentDate = new Date(
      messageDate.getFullYear(),
      messageDate.getMonth(),
      messageDate.getDate()
    );

    // First message or new day
    if (index === 0 || !lastDate || isDifferentDay(lastDate, currentDate)) {
      result.push({
        separator: true,
        date: formatDate(currentDate)
      });
    }

    result.push(message);
    lastDate = currentDate;
  });

  return result;
});

// Only auto-scroll when new messages arrive or on initial load
watch(
  () => messages.length,
  (newLength, oldLength) => {
    if (newLength > oldLength && chatContainer.value) {
      // Only auto-scroll if user was already at the bottom
      const container = chatContainer.value;
      const isNearBottom =
        container.scrollHeight - container.clientHeight <=
        container.scrollTop + 50;

      if (isNearBottom || !userHasScrolled.value) {
        setTimeout(() => {
          if (chatContainer.value) {
            chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
          }
        }, 0);
      }
    }
  }
);

// Track when user manually scrolls
const handleScroll = () => {
  if (!chatContainer.value) return;

  const container = chatContainer.value;
  const isAtBottom =
    container.scrollHeight - container.clientHeight <= container.scrollTop + 50;

  // If user scrolls away from bottom, mark as manually scrolled
  if (!isAtBottom) {
    userHasScrolled.value = true;
  }

  // If user scrolls back to bottom, reset the flag
  if (isAtBottom) {
    userHasScrolled.value = false;
  }
};

// Scroll to bottom on initial mount
onMounted(() => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
    chatContainer.value.addEventListener("scroll", handleScroll);
  }
});

// Clean up event listener
onUnmounted(() => {
  if (chatContainer.value) {
    chatContainer.value.removeEventListener("scroll", handleScroll);
  }
});
const handleTagClick = (event: Event) => {
  const tag = (event.target as HTMLSpanElement).textContent;
  if (tag) {
    emit('update:search', tag);
  }
};  
const highlightTags = (content: string) => {
  // Regular expression to match hashtags
  const tagRegex = /#(\w+)/g;
  
  // Replace hashtags with highlighted spans
  return content.replace(tagRegex, '<span class="text-blue-500 font-medium cursor-pointer">#$1</span>');
};

// Add a function to handle mention clicks
const handleMentionClick = (event: Event) => {
  const mention = (event.target as HTMLSpanElement).textContent;
  if (mention && mention.startsWith('@')) {
    const username = mention.substring(1); // Remove the @ symbol
    emit('update:search', '@' + username);
  }
};

// Combined click handler for both tags and mentions
const handleContentClick = (e: Event) => {
  const target = e.target as HTMLElement;
  if (target.classList.contains('text-blue-500')) {
    const content = target.textContent;
    if (content?.startsWith('#')) {
      handleTagClick(e);
    } else if (content?.startsWith('@')) {
      handleMentionClick(e);
    }
  }
};

// Highlight mentions in blue
const highlightMentions = (content: string) => {
  // Regular expression to match @username mentions
  const mentionRegex = /@(\w+)/g;
  
  // Replace mentions with highlighted spans
  return content.replace(mentionRegex, '<span class="text-blue-500 font-medium cursor-pointer">@$1</span>');
};

// Add this helper function
const nl2br = (str: string) => {
  return str.replace(/\n/g, "<br>");
};

// Update highlightContent to include nl2br
const highlightContent = (content: string) => {
  // First highlight hashtags
  let highlighted = highlightTags(content);
  // Then highlight mentions
  highlighted = highlightMentions(highlighted);
  // Finally, convert newlines to <br>
  highlighted =  nl2br(highlighted);
  return highlighted;
};

// Scroll to a message by ID
function scrollToMessage(id?: string) {
  if (!id) return;
  const el = document.getElementById('msg-' + id);
  if (el && chatContainer.value) {
    el.scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
}

///copy trading part
const fullImageActive = ref("");
const toggleFullImage = (imageUrl: string) => {
  if (fullImageActive.value === imageUrl) {
    fullImageActive.value = "";
  } else {
    fullImageActive.value = imageUrl;
  }
};
</script>

<template>
  <div
    ref="chatContainer"
    class="h-full overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-sm"
    @click="handleContentClick"
  >
    <div
      v-for="(message, index) in messagesWithDaySeparators"
      :key="message._id || index"
    >
    <div v-if="!message.deleted">
      <!-- Day separator -->
      <div
        v-if="message.separator"
        class="flex items-center justify-center py-2"
      >
        <div
          class="bg-gray-100 rounded-full px-3 py-1 text-xs text-gray-600 font-medium"
        >
          {{ message.date }}
        </div>
      </div>

      <!-- Message -->
      <div
        v-else
        :id="message._id ? 'msg-' + message._id : undefined"
        class="py-3 px-4 flex items-start gap-3 border-b border-gray-100 last:border-0 hover:bg-gray-50 transition-colors relative group"
      >
      
        <div class="flex-shrink-0">
          
          <div
            v-if="message.sender?.profile_picture || (userStore.user?.user_name==message.sender?.user_name && userStore.user?.profile_picture)"
            class="w-8 h-8 rounded-full overflow-hidden"
          >
            <img
              :src="message.sender.profile_picture || (userStore.user?.user_name==message.sender?.user_name && userStore.user?.profile_picture)"
              alt="User avatar"
              class="w-full h-full object-cover"
            />
          </div>
          <div
            v-else
            class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium text-sm"
          >
            {{ getInitials(message.sender?.user_name || 'Unknown User') }}
          </div>
        </div>

        <div class="flex-1 min-w-0">
          <div class="flex items-center justify-between mb-1">
            <div class="flex items-center gap-2">
              <span class="font-medium text-gray-900">
                {{ message.sender?.user_name || 'Unknown User' }}
              </span>
              <span class="text-xs text-gray-500">
                {{ formatTime(message.timestamp) }}
              </span>
            </div>
            
            <!-- Message menu button - only show for user's own messages -->
             
            <button 
              
              class="message-menu-button p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 opacity-0 group-hover:opacity-100 transition-opacity"
              @click.stop="toggleMessageMenu(message._id)"
            >

              <MoreVertical class="w-4 h-4" />
            </button>
            
            <!-- Message menu dropdown -->
            <div 
              v-if="activeMessageMenu === message._id"
              class="message-menu absolute right-4 -top-10 bg-white shadow-lg rounded-md border border-gray-200 z-10"
            >
              
           
              <button 
                class="flex items-center gap-2 w-full px-4 py-2 text-left text-gray-600 hover:bg-gray-50 cursor-pointer"
                @click="()=> {
                  
                  emit('parent-updated', message);
                }"
              >
                <ReplyIcon class="w-4 h-4" />
                <span>Reply</span>
              </button>
              <button  v-if="isCurrentUserMessage(message.sender?._id || message.sender)"
                class="flex items-center gap-2 w-full px-4 py-2 text-left text-red-600 hover:bg-gray-50 cursor-pointer"
                @click="deleteMessage(message._id)"  
              >
                <Trash2 class="w-4 h-4" />
                <span>Delete</span>
              </button>
            </div>
          </div>
          
          <!-- Parent message preview -->
          <div
            v-if="message.parentId != null"
            class="flex items-center gap-2 mb-2 px-3 py-2 bg-gray-50 border-l-4 border-blue-400 rounded shadow-sm cursor-pointer hover:bg-blue-50"
            style="max-width: 90%;"
            @click="scrollToMessage(message.parentId._id)"
          >
            <div v-if="message.parentId.media && message.parentId.media.length > 0" class="">
              <img
                :src="message.parentId.media[0]"
                alt="Attached image"
                class="max-w-full w-12 h-12 rounded-lg shadow-sm mr-2"
              />
            </div>
            <!-- Reply icon -->
            <ReplyIcon class="w-4 h-4 text-blue-400 mr-1 flex-shrink-0" />

            <!-- Parent profile picture or initials -->
            <div class="w-6 h-6 rounded-full overflow-hidden flex-shrink-0">
              <img
                v-if="message.parentId.sender?.profile_picture"
                :src="message.parentId.sender.profile_picture"
                alt="User avatar"
                class="w-full h-full object-cover"
              />
              <div
                v-else
                class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium text-xs"
              >
                {{ getInitials(message.parentId.sender?.user_name || 'Unknown User') }}
              </div>
            </div>
            <!-- Parent username and content -->
            <div class="flex flex-col min-w-0">
              <span class="text-gray-700 font-medium text-xs truncate">
                {{ message.parentId.sender?.user_name || 'Unknown User' }}
              </span>
              <span class="text-gray-500 text-xs italic truncate" style="max-width: 200px;">
                {{ message.parentId.content.slice(0, 80) }}{{ message.parentId.content.length > 80 ? '...' : '' }}
              </span>
            </div>
          </div>

          <!-- Main message content -->
           <!-- image section  if parentid nulll not replied wala -->
            <div @click="toggleFullImage(message.media[0])" v-if="message.parentId==null && message?.media && message?.media.length>0" class="mb-2">
              <img
                :src="message.media[0]"
                alt="Attached image"
                class="max-w-full  w-2/4 h-auto rounded-lg shadow-sm"
              />
              </div>
          <div 
            class="text-gray-700 break-words"
            v-html="highlightContent(message.content)"
          ></div>
        </div>
      </div>
    </div>
    </div>
    <!-- Full image modal -->
    <div v-if="fullImageActive" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" @click="toggleFullImage('')">
  <div class="relative max-w-7xl w-full p-4" @click.stop>
    <button 
      class="absolute top-2 right-2 text-white bg-gray-800 rounded-full p-1 hover:bg-gray-700 z-10"
      @click="toggleFullImage('')"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>
    <img :src="fullImageActive" alt="Full size image" class="max-w-full h-auto rounded-lg shadow-lg" @click.stop />
  </div>
</div>
  </div>
</template>

<style scoped>
/* Add any additional styles here */
.message-menu {
  min-width: 120px;
}
</style>
