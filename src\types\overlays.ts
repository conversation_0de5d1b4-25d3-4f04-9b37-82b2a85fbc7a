export interface MarketCloseLinesOverlayProps {
  visible: boolean;
  nyVisible: boolean;
  asiaVisible: boolean;
  londonVisible: boolean;
  closeHour: number;
  nyHour: number;
  asiaHour: number;
  londonHour: number;
  boxPlotHigh: boolean;
  boxPlotLow: boolean;
  highMin: number;
  highMax: number;
  highMedian: number;
  highQ1: number;
  highQ3: number;
  lowMin: number;
  lowMax: number;
  lowMedian: number;
  lowQ1: number;
  lowQ3: number;
}

export interface CandleOverlayProps {
  showVolume: boolean;
}
