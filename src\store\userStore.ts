import { ref } from "vue";

import { defineStore } from "pinia";

import { useCandleStickStore } from "./candleStickStore";
import { axios } from "@/api";
import { IUser, SubscriptionPlan } from "@/types";

export const useUserStore = defineStore("user", () => {
  const candleStickStore = useCandleStickStore();
  const isUserLoggedIn = ref(false);
  const user = ref<IUser | null>(null);
  const userAccessToken = ref<string | null>(null);
  const eaAccessToken = ref<string | null>(null);
  const onlineStatus = ref(true);
  const subscriptionPlans = ref<SubscriptionPlan[]>([]);
  const isEnvProd = ref(import.meta.env.VITE_APP_ENV === "production");

  async function logOut(executeAPI = true) {
    try {
      if (executeAPI) {
        await axios.post("/auth/logout");
      }

      user.value = null;
      userAccessToken.value = null;
      eaAccessToken.value = null;
      isUserLoggedIn.value = false;

      candleStickStore.reset();

      localStorage.removeItem("loggedOut");
    } catch (e) {
      console.error(e);
    }
  }
  function updateProfilePicture(url: string) {
    if (user.value) {
      user.value = {
        ...user.value,
        profile_picture: url
      };
    }
  }

  return {
    isUserLoggedIn,
    user,
    userAccessToken,
    eaAccessToken,
    onlineStatus,
    subscriptionPlans,
    isEnvProd,
    logOut,
    updateProfilePicture
  };
});
