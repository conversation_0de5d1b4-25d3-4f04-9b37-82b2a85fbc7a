<script setup lang="ts">
import { ref } from "vue";
import { useWindowSize } from "@vueuse/core";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { useUserStore } from "@/store/userStore";
import { EUserRole } from "@/types/enums";

const userStore = useUserStore();

const { height: cardHeight } = useWindowSize();

const isUserCustomer = ref(userStore.user?.roles.includes(EUserRole.CUSTOMER));
</script>

<template>
  <div
    class="mx-auto grid w-10/12 grid-cols-12 rounded-md border bg-white shadow-lg"
    :style="{ height: cardHeight - 55 - 40 + 'px' }"
  >
    <div class="col-span-4 border-r py-5">
      <div class="mb-2 px-4 text-lg font-bold">Settings</div>

      <router-link
        class="flex items-center justify-between px-4 pb-2.5 pt-3 hover:bg-gray-100"
        :to="{ name: 'account' }"
      >
        Your Account

        <FontAwesomeIcon size="sm" icon="fa-solid fa-chevron-right" />
      </router-link>

      <router-link
        class="flex items-center justify-between px-4 pb-2.5 pt-3 hover:bg-gray-100"
        :to="{ name: 'edit-payment-information' }"
        v-if="isUserCustomer"
      >
        Payment Information

        <FontAwesomeIcon size="sm" icon="fa-solid fa-chevron-right" />
      </router-link>
    </div>

    <div class="col-span-8 overflow-auto py-5">
      <router-view></router-view>
    </div>
  </div>
</template>

<style scoped>
.router-link-active {
  font-weight: 600;
  background-color: #f3f4f6;
}
</style>
