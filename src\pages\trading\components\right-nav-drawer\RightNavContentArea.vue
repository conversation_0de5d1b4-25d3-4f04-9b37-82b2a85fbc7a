<script setup lang="ts">
import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";

import EventsAndNotifications from "./components/EventsAndNotifications.vue";
import MarketWatch from "./components/MarketWatch.vue";
import { useRightNavResize } from "./composables/use-right-nav-resize";
import Chat from "@/features/chat/Chat.vue";

const rightNavDrawerStore = useRightNavDrawerStore();
const { handleMouseDown } = useRightNavResize();
</script>

<template>
  <div
    id="right-nav-container"
    class="relative flex h-full"
    :style="{
      width: `${rightNavDrawerStore.rightNavContentAreaWidth}px`,
      height: rightNavDrawerStore.rightNavContentAreaHeight + 'px'
    }"
  >
    <!-- Resize handle on the left side -->
    <div
      class="absolute left-0 top-0 w-1 h-full bg-gray-300 hover:bg-blue-400 cursor-ew-resize z-2"
      @mousedown="handleMouseDown"
    ></div>

    <div
      id="right-nav-content-area"
      class="overflow-hidden border-l-4 text-sm w-full h-full pl-1"
    >
      <MarketWatch
        v-if="rightNavDrawerStore.rightNavContentAreaTab === 'market-watch-tab'"
      />
      <EventsAndNotifications
        v-if="
          rightNavDrawerStore.rightNavContentAreaTab ===
          'events-and-notifications-tab'
        "
      />
      <Chat v-if="rightNavDrawerStore.rightNavContentAreaTab === 'chat-tab'" />
    </div>
  </div>
</template>
