import { BasicPoint } from "../base/Types";

const HOUR = 3600000; // milliseconds in an hour
const DAY = HOUR * 24; // milliseconds in a day
const WEEK = 7 * DAY; // milliseconds in a week
const YEAR = DAY * 365;
const MONTHMAP = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sept",
  "Oct",
  "Nov",
  "Dec"
];

const addZero = (x: number) => (x < 10 ? `0${x}` : `${x}`);

export function formatCursorX(
  $time: number,
  timeFrame: number,
  timezone: number
) {
  let t = $time;
  if (t === void 0) return `Out of range`;
  let tf = timeFrame;
  let k = tf < DAY ? 1 : 0;
  let d = new Date(t + k * timezone * HOUR);
  if (tf === YEAR) {
    return d.getUTCFullYear().toString();
  }
  let dd = "01";
  let mo = "";
  let yr = "";
  if (tf < YEAR) {
    yr = "`" + `${d.getUTCFullYear()}`.slice(-2);
    mo = MONTHMAP[d.getUTCMonth()];
  }
  if (tf <= WEEK) dd = d.getUTCDate().toString();
  let date = `${dd} ${mo} ${yr}`;
  let time = "";
  if (tf < DAY) {
    let h = addZero(d.getUTCHours());
    let m = addZero(d.getUTCMinutes());
    time = h + ":" + m;
  }
  return `${date}  ${time}`;
}

export function extendLine(
  p1: BasicPoint,
  p2: BasicPoint,
  layoutWidth: number,
  layoutHeight: number
) {
  const dx = p2.x - p1.x,
    dy = p2.y - p1.y;
  const tx = dx ? (dx > 0 ? layoutWidth - p1.x : -p1.x) / dx : Infinity;
  const ty = dy ? (dy > 0 ? layoutHeight - p1.y : -p1.y) / dy : Infinity;
  const t = Math.min(tx, ty);
  return { x: p1.x + t * dx, y: p1.y + t * dy };
}
