import { TCoreData } from "../types";
import { BasicPoint } from "./base/Types";
import { DistanceUtils } from "./utils/DistanceUtils";
import { BaseShape, IBaseShape, IBaseShapeOptions } from "./shapes/BaseShape";
import { PreferenceUtils } from "./utils/PreferencesUtils";
import { formatCursorX } from "./utils/fns";
import { DrawUtils2 } from "./utils/DrawUtils2";
import { Point } from "./shapes/Point";
import { BaseShapeInterface } from "./base/shape.types";

function throttle<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timerId: ReturnType<typeof setTimeout> | undefined;
  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    if (timerId) return;
    func.apply(this, args);
    timerId = setTimeout(() => {
      clearTimeout(timerId);
      timerId = undefined;
    }, delay);
  };
}

export class TextShape
  extends BaseShape<"text">
  implements BaseShapeInterface<"text">
{
  dragState: "tracking" | "settled" | "dragging" | "tracking-x" | "tracking-y" =
    "settled";
  draggingPoint: { x: number; y: number } | null = null;
  dragging: boolean = false;
  textData: string = "";
  isEditing: boolean = true;
  cursorVisible: boolean = false;
  setText = throttle((t: string) => {
    this.textData = t;
    this.$core.hub.events.emit("shape-property-changed");
    this.$core.hub.events.emit("update-layout");
  }, 5);

  textBoundingBox: { x: number; y: number }[] = [];
  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (line: IBaseShape) => boolean
  ) {
    const linePoints = points.length
      ? points
      : [{ x: $core.cursor.x, y: $core.cursor.y }];
    super(
      $core,
      uuid,
      linePoints,
      points.length ? screenPoints : true,
      onSelect
    );
    this.type = "text";
    this.properties = PreferenceUtils.text;

    if (!screenPoints) this.isEditing = false;
    // Start the blinking cursor timer
    setInterval(() => {
      this.cursorVisible = !this.cursorVisible;
      if (this.isEditing) {
        this.$core.hub.events.emit("update-layout");
      }
    }, 500); // Toggle cursor visibility every 500ms
  }
  get isValid(): boolean {
    return true;
  }
  get screenPoints() {
    return [this.points[0].screen, this.points[0].screen];
  }

  draw(ctx: CanvasRenderingContext2D): void {
    this.drawMidPoint(ctx);
    const tp = this.properties.textProperties;
    // const tp = this.properties.;
    // const tp = this.properties.textProperties;
    // const tp = this.properties.textProperties;

    // Measure the width of the text
    ctx.font = `${tp.font_size}px ${tp.font}`; // Font size and family
    const fontColor = tp.font_color;
    const fontColorWithAlpha =
      tp.font_color + (tp.font_color.length > 4 ? "88" : "8");
    const textMeasure = ctx.measureText(this.textData);
    const emptyTextMeasure = ctx.measureText("Type Here");

    // Set the position of the text
    const midPoint = this.points[0].screen;
    const textX = midPoint.x - textMeasure.width / 2; // Adjust to center text
    const emptyTextX = midPoint.x - emptyTextMeasure.width / 2; // Adjust to center text
    const textY = midPoint.y - 10; // Baseline position for 25px font size
    const isEmpty = !this.textData.length;
    this.textBoundingBox = [
      { x: isEmpty ? emptyTextX : textX, y: textY },
      {
        x: isEmpty
          ? emptyTextX + emptyTextMeasure.width
          : textX + textMeasure.width,
        y: isEmpty
          ? textY - emptyTextMeasure.actualBoundingBoxAscent
          : textY - textMeasure.actualBoundingBoxAscent
      }
    ];

    // Draw background rectangle if background color is not transparent
    ctx.save();
    ctx.fillStyle = tp.bg_color;
    const padding = 4;
    const textWidth = isEmpty ? emptyTextMeasure.width : textMeasure.width;
    const textHeight = isEmpty
      ? emptyTextMeasure.actualBoundingBoxAscent
      : textMeasure.actualBoundingBoxAscent;

    ctx.fillRect(
      (isEmpty ? emptyTextX : textX) - padding,
      textY - textHeight - padding,
      textWidth + padding * 2,
      textHeight + padding * 2
    );
    ctx.restore();

    // Draw the text
    ctx.beginPath();
    ctx.font = `${tp.font_size}px ${tp.font}`; // Font size and family
    ctx.fillStyle = this.textData.length === 0 ? fontColorWithAlpha : fontColor;
    ctx.fillText(
      this.textData.length ? this.textData : "Type Here",
      this.textData.length ? textX : emptyTextX,
      textY
    );

    if (this.isEditing && this.cursorVisible && !this.locked) {
      // Draw cursor at the end of the text
      const cursorX = textX + textMeasure.width;
      ctx.beginPath();
      ctx.strokeStyle = fontColor;
      ctx.lineWidth = 2;
      ctx.moveTo(cursorX, textY + 5);
      ctx.lineTo(cursorX, textY - 15);
      ctx.stroke();
    }
  }

  drawMidPoint(ctx: CanvasRenderingContext2D) {
    const point = this.points[0].screen;

    const tp = this.properties.textProperties;
    const pd = this.properties.pointDisplay;
    const pp = this.properties.pointProperties;

    if (pp.point_position !== "midpoint") return;

    let width = 0;
    if (this.selected && pd.show_point_selected) {
      width = pp.point_width_selected;
    } else if (this.hovered && pd.show_point_hover) {
      width = pp.point_width_hover;
    } else if (pd.show_point_default) {
      width = pp.point_width_default;
    }

    if (width)
      Point.drawPoint(ctx, point, pp.point_shape, width, tp.font_color, 1);
  }

  drawSidebar(
    ctx: CanvasRenderingContext2D,
    _: any,
    scale: { prec: number }
  ): void {
    const tp = this.properties.textProperties;
    const sd = this.properties.sidebarDisplay;

    const draw = () => {
      ctx.fillStyle = tp.font_color;
      this.points.forEach((point) => {
        const label = point.y.toFixed(scale.prec);
        ctx.save();
        DrawUtils2.drawSidebar(ctx, label, point.screenY);
        ctx.restore();
      });
    };

    if (sd.show_sidebar_default) draw();
    else if (sd.show_sidebar_hover && this.hovered) draw();
    else if (sd.show_sidebar_selected && this.selected) draw();
  }

  drawBotbar(ctx: CanvasRenderingContext2D): void {
    const tp = this.properties.textProperties;
    const sd = this.properties.sidebarDisplay;

    const draw = () => {
      ctx.save();
      ctx.fillStyle = tp.font_color;
      this.points.forEach((point) => {
        const label = formatCursorX(
          point.time,
          this.$core.props.timeFrame,
          this.$core.props.timezone
        );
        DrawUtils2.drawBotbar(ctx, label, point.screenX);
      });
      ctx.restore();
    };

    if (sd.show_sidebar_default) draw();
    else if (sd.show_sidebar_hover && this.hovered) draw();
    else if (sd.show_sidebar_selected && this.selected) draw();
  }

  hoverOnText(cursor: BasicPoint) {
    if (
      cursor.x <
        Math.max(this.textBoundingBox[0].x, this.textBoundingBox[1].x) &&
      cursor.x > Math.min(this.textBoundingBox[0].x, this.textBoundingBox[1].x)
    ) {
      if (
        cursor.y <
          Math.max(this.textBoundingBox[0].y, this.textBoundingBox[1].y) &&
        cursor.y >
          Math.min(this.textBoundingBox[0].y, this.textBoundingBox[1].y)
      ) {
        return true;
      }
    }
    return false;
  }

  mousedown(event: MouseEvent): void {
    const cursor = { x: event.offsetX, y: event.offsetY };

    if (
      DistanceUtils.isCursorOnLine(
        this.screenPoints[0],
        this.screenPoints[1],
        cursor
      ) ||
      this.hoverOnText(cursor)
    ) {
      if (this.selected) {
        this.isEditing = true;
      }

      this.onSelect(this);
      // if (DistanceUtils.isCursorOnPoint(this.midPoint, cursor)) {
      this.dragging = true;
      this.draggingPoint = { x: this.$core.cursor.x, y: this.$core.cursor.y };
      this.$core.hub.events.emit("scroll-lock", true);
      // }
    } else {
      this.isEditing = false;
      this.dragging = false;
      this.draggingPoint = null;
      // // this.selected = false
    }
    this.$core.hub.events.emit("update-layout");
  }

  mouseover(event: MouseEvent): void {
    const cursor = { x: event.offsetX, y: event.offsetY };
    if (
      DistanceUtils.isCursorOnLine(
        this.screenPoints[0],
        this.screenPoints[1],
        cursor
      ) ||
      this.hoverOnText(cursor)
    ) {
      this.hovered = true;
    } else {
      this.hovered = false;
    }
  }

  mousemove(): void {
    if (this.dragging && this.draggingPoint && this.selected) {
      this.points.forEach((p) => {
        // I don't know why the screen coordinates always come less than real values
        const x = this.$core.cursor.x;
        const y = this.$core.cursor.y;
        p.updatePosition({ x, y });
      });
    }
  }

  mouseup(): void {
    if (this.isValid) {
      this.dragging = false;
    }
    this.$core.hub.events.emit("scroll-lock", false);
    // throw new Error("Method not implemented.")
  }

  keydown?(event: KeyboardEvent): void {
    if (this.isEditing) {
      if (event.code === "Enter" || event.code === "Escape")
        this.isEditing = false;

      if (event.code === "Backspace") {
        this.setText(this.textData.substring(0, this.textData.length - 1));
      }

      // Handle special keys that shouldn't add characters
      if (
        event.ctrlKey ||
        event.altKey ||
        event.metaKey || // Modifier keys
        event.key.length > 1 || // Special keys like 'ArrowRight'
        // Function keys F1-F12
        event.code.startsWith("F")
      ) {
        return;
      }

      // Add the pressed key to text
      this.setText(this.textData + event.key);
    }
  }
  getCoordinates(): { [x: string]: any } {
    return {
      row: this.points[0].x,
      price: this.points[0].y
    };
  }
  setCoordinatesVal(name: string, value: any): boolean {
    switch (name) {
      case "row": {
        this.points[0].x = Number(value);
        return true;
      }
      case "price": {
        this.points[0].y = Number(value);
        return true;
      }
    }
    return false;
  }

  toJSON: () => { [x: string]: any } = () => {
    if (!this.textData) return {};
    return {
      uuid: this.uuid,
      type: this.type,
      time: this.points.map((p) => p.time),
      points: [{ x: this.points[0].x, y: this.points[0].y }],
      properties: this.properties,
      textData: this.textData
    };
  };

  getOptions: () => IBaseShapeOptions<any> = () => {
    return {
      uuid: this.uuid,
      getCoordinates: this.getCoordinates.bind(this),
      name: this.name,
      coords: this.getCoordinates(),
      locked: this.locked,
      properties: this.properties,
      setCoordinates: this.setCoordinatesVal.bind(this),
      textData: this.textData,
      setText: this.setText,
      setProperty: this.setProperty.bind(this),
      type: this.type,
      getLabelProperties: this.getLabelProperties.bind(this)
    } as unknown as IBaseShapeOptions<any>;
  };
}
