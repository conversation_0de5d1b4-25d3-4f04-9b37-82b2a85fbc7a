// vite.config.ts
import path from "path";
import { defineConfig } from "file:///D:/Coding/tradeawaay/tradeawaay-frontend/node_modules/vite/dist/node/index.js";
import svgLoader from "file:///D:/Coding/tradeawaay/tradeawaay-frontend/node_modules/vite-svg-loader/index.js";

// src/utilities/plugins.ts
import { loadEnv } from "file:///D:/Coding/tradeawaay/tradeawaay-frontend/node_modules/vite/dist/node/index.js";
function viteRawPlugin({ fileRegex }) {
  return {
    name: "vite-raw-plugin",
    transform(code, id) {
      if (fileRegex.test(id)) {
        const json = JSON.stringify(code).replace(/\u2028/g, "\\u2028").replace(/\u2029/g, "\\u2029");
        return {
          code: `export default ${json}`
        };
      }
    }
  };
}
function seo() {
  const seoTags = [
    '<link rel="canonical" href="https://www.tradeawaay.com" />\n',
    '<meta name="robots" content="index, follow"/>\n',
    '<meta name="description" content="Tradeawaay is a web-based trading application powered by data analytics for traders to make calculated decisions in trading. Tradeawaay provides full trading experience by connecting to your MetaTrader 5 through Expert Advisor."/>\n',
    '<meta name="twitter:url" content="https://www.tradeawaay.com"/>\n',
    '<meta name="twitter:title" content="Tradeawaay \u2013 Maximize Your Decision Making In Trading"/>\n',
    '<meta name="twitter:description" content="Tradeawaay is a web-based trading application powered by data analytics for traders to make calculated decisions in day trading. Tradeawaay provides full trading experience by connecting to your MetaTrader 5 through Expert Advisor."/>\n',
    '<meta name="twitter:card" content="summary"/>\n',
    '<meta property="og:url" content="https://www.tradeawaay.com"/>',
    '<meta property="og:title" content="Tradeawaay \u2013 Maximize Your Decision Making In Trading"/>\n',
    ' <meta property="og:description" content="Tradeawaay is a web-based trading application powered by data analytics for traders to make calculated decisions in day trading. Tradeawaay provides full trading experience by connecting to your MetaTrader 5 through Expert Advisor."/>\n',
    '<meta property="og:type" content="website"/>'
  ];
  const env = loadEnv("production", process.cwd(), "");
  return {
    name: "html-transform",
    transformIndexHtml(html) {
      if (env.VITE_APP_ENV === "production") {
        let tags = "";
        seoTags.forEach((t) => {
          tags += t;
        });
        html = html.replace(
          '<meta name="robots" content="noindex, nofollow" />',
          tags
        );
      }
      return html;
    }
  };
}

// vite.config.ts
import vue from "file:///D:/Coding/tradeawaay/tradeawaay-frontend/node_modules/@vitejs/plugin-vue/dist/index.mjs";
var __vite_injected_original_dirname = "D:\\Coding\\tradeawaay\\tradeawaay-frontend";
var vite_config_default = defineConfig({
  plugins: [
    vue(),
    seo(),
    svgLoader(),
    viteRawPlugin({
      fileRegex: /\.navy$/
    })
  ],
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src/"),
      "@website": path.resolve(__vite_injected_original_dirname, "./src/pages/website/")
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiLCAic3JjL3V0aWxpdGllcy9wbHVnaW5zLnRzIl0sCiAgInNvdXJjZXNDb250ZW50IjogWyJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRDpcXFxcQ29kaW5nXFxcXHRyYWRlYXdhYXlcXFxcdHJhZGVhd2FheS1mcm9udGVuZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRDpcXFxcQ29kaW5nXFxcXHRyYWRlYXdhYXlcXFxcdHJhZGVhd2FheS1mcm9udGVuZFxcXFx2aXRlLmNvbmZpZy50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRDovQ29kaW5nL3RyYWRlYXdhYXkvdHJhZGVhd2FheS1mcm9udGVuZC92aXRlLmNvbmZpZy50c1wiO2ltcG9ydCBwYXRoIGZyb20gXCJwYXRoXCI7XHJcbmltcG9ydCB7IGRlZmluZUNvbmZpZyB9IGZyb20gXCJ2aXRlXCI7XHJcbmltcG9ydCBzdmdMb2FkZXIgZnJvbSBcInZpdGUtc3ZnLWxvYWRlclwiO1xyXG5cclxuLy8gQHRzLWlnbm9yZVxyXG5pbXBvcnQgeyBzZW8sIHZpdGVSYXdQbHVnaW4gfSBmcm9tIFwiLi9zcmMvdXRpbGl0aWVzL3BsdWdpbnNcIjtcclxuaW1wb3J0IHZ1ZSBmcm9tIFwiQHZpdGVqcy9wbHVnaW4tdnVlXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoe1xyXG4gIHBsdWdpbnM6IFtcclxuICAgIHZ1ZSgpLFxyXG4gICAgc2VvKCksXHJcbiAgICBzdmdMb2FkZXIoKSxcclxuICAgIHZpdGVSYXdQbHVnaW4oe1xyXG4gICAgICBmaWxlUmVnZXg6IC9cXC5uYXZ5JC9cclxuICAgIH0pXHJcbiAgXSxcclxuICByZXNvbHZlOiB7XHJcbiAgICBhbGlhczoge1xyXG4gICAgICBcIkBcIjogcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgXCIuL3NyYy9cIiksXHJcbiAgICAgIFwiQHdlYnNpdGVcIjogcGF0aC5yZXNvbHZlKF9fZGlybmFtZSwgXCIuL3NyYy9wYWdlcy93ZWJzaXRlL1wiKVxyXG4gICAgfVxyXG4gIH1cclxufSk7XHJcbiIsICJjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfZGlybmFtZSA9IFwiRDpcXFxcQ29kaW5nXFxcXHRyYWRlYXdhYXlcXFxcdHJhZGVhd2FheS1mcm9udGVuZFxcXFxzcmNcXFxcdXRpbGl0aWVzXCI7Y29uc3QgX192aXRlX2luamVjdGVkX29yaWdpbmFsX2ZpbGVuYW1lID0gXCJEOlxcXFxDb2RpbmdcXFxcdHJhZGVhd2FheVxcXFx0cmFkZWF3YWF5LWZyb250ZW5kXFxcXHNyY1xcXFx1dGlsaXRpZXNcXFxccGx1Z2lucy50c1wiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9pbXBvcnRfbWV0YV91cmwgPSBcImZpbGU6Ly8vRDovQ29kaW5nL3RyYWRlYXdhYXkvdHJhZGVhd2FheS1mcm9udGVuZC9zcmMvdXRpbGl0aWVzL3BsdWdpbnMudHNcIjtpbXBvcnQgeyBsb2FkRW52IH0gZnJvbSBcInZpdGVcIjtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiB2aXRlUmF3UGx1Z2luKHsgZmlsZVJlZ2V4IH06IHsgZmlsZVJlZ2V4OiBSZWdFeHAgfSkge1xyXG4gIHJldHVybiB7XHJcbiAgICBuYW1lOiBcInZpdGUtcmF3LXBsdWdpblwiLFxyXG4gICAgdHJhbnNmb3JtKGNvZGU6IGFueSwgaWQ6IHN0cmluZykge1xyXG4gICAgICBpZiAoZmlsZVJlZ2V4LnRlc3QoaWQpKSB7XHJcbiAgICAgICAgY29uc3QganNvbiA9IEpTT04uc3RyaW5naWZ5KGNvZGUpXHJcbiAgICAgICAgICAucmVwbGFjZSgvXFx1MjAyOC9nLCBcIlxcXFx1MjAyOFwiKVxyXG4gICAgICAgICAgLnJlcGxhY2UoL1xcdTIwMjkvZywgXCJcXFxcdTIwMjlcIik7XHJcblxyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICBjb2RlOiBgZXhwb3J0IGRlZmF1bHQgJHtqc29ufWBcclxuICAgICAgICB9O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfTtcclxufVxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHNlbygpIHtcclxuICBjb25zdCBzZW9UYWdzID0gW1xyXG4gICAgJzxsaW5rIHJlbD1cImNhbm9uaWNhbFwiIGhyZWY9XCJodHRwczovL3d3dy50cmFkZWF3YWF5LmNvbVwiIC8+XFxuJyxcclxuICAgICc8bWV0YSBuYW1lPVwicm9ib3RzXCIgY29udGVudD1cImluZGV4LCBmb2xsb3dcIi8+XFxuJyxcclxuICAgICc8bWV0YSBuYW1lPVwiZGVzY3JpcHRpb25cIiBjb250ZW50PVwiVHJhZGVhd2FheSBpcyBhIHdlYi1iYXNlZCB0cmFkaW5nIGFwcGxpY2F0aW9uIHBvd2VyZWQgYnkgZGF0YSBhbmFseXRpY3MgZm9yIHRyYWRlcnMgdG8gbWFrZSBjYWxjdWxhdGVkIGRlY2lzaW9ucyBpbiB0cmFkaW5nLiBUcmFkZWF3YWF5IHByb3ZpZGVzIGZ1bGwgdHJhZGluZyBleHBlcmllbmNlIGJ5IGNvbm5lY3RpbmcgdG8geW91ciBNZXRhVHJhZGVyIDUgdGhyb3VnaCBFeHBlcnQgQWR2aXNvci5cIi8+XFxuJyxcclxuICAgICc8bWV0YSBuYW1lPVwidHdpdHRlcjp1cmxcIiBjb250ZW50PVwiaHR0cHM6Ly93d3cudHJhZGVhd2FheS5jb21cIi8+XFxuJyxcclxuICAgICc8bWV0YSBuYW1lPVwidHdpdHRlcjp0aXRsZVwiIGNvbnRlbnQ9XCJUcmFkZWF3YWF5IFx1MjAxMyBNYXhpbWl6ZSBZb3VyIERlY2lzaW9uIE1ha2luZyBJbiBUcmFkaW5nXCIvPlxcbicsXHJcbiAgICAnPG1ldGEgbmFtZT1cInR3aXR0ZXI6ZGVzY3JpcHRpb25cIiBjb250ZW50PVwiVHJhZGVhd2FheSBpcyBhIHdlYi1iYXNlZCB0cmFkaW5nIGFwcGxpY2F0aW9uIHBvd2VyZWQgYnkgZGF0YSBhbmFseXRpY3MgZm9yIHRyYWRlcnMgdG8gbWFrZSBjYWxjdWxhdGVkIGRlY2lzaW9ucyBpbiBkYXkgdHJhZGluZy4gVHJhZGVhd2FheSBwcm92aWRlcyBmdWxsIHRyYWRpbmcgZXhwZXJpZW5jZSBieSBjb25uZWN0aW5nIHRvIHlvdXIgTWV0YVRyYWRlciA1IHRocm91Z2ggRXhwZXJ0IEFkdmlzb3IuXCIvPlxcbicsXHJcbiAgICAnPG1ldGEgbmFtZT1cInR3aXR0ZXI6Y2FyZFwiIGNvbnRlbnQ9XCJzdW1tYXJ5XCIvPlxcbicsXHJcbiAgICAnPG1ldGEgcHJvcGVydHk9XCJvZzp1cmxcIiBjb250ZW50PVwiaHR0cHM6Ly93d3cudHJhZGVhd2FheS5jb21cIi8+JyxcclxuICAgICc8bWV0YSBwcm9wZXJ0eT1cIm9nOnRpdGxlXCIgY29udGVudD1cIlRyYWRlYXdhYXkgXHUyMDEzIE1heGltaXplIFlvdXIgRGVjaXNpb24gTWFraW5nIEluIFRyYWRpbmdcIi8+XFxuJyxcclxuICAgICcgPG1ldGEgcHJvcGVydHk9XCJvZzpkZXNjcmlwdGlvblwiIGNvbnRlbnQ9XCJUcmFkZWF3YWF5IGlzIGEgd2ViLWJhc2VkIHRyYWRpbmcgYXBwbGljYXRpb24gcG93ZXJlZCBieSBkYXRhIGFuYWx5dGljcyBmb3IgdHJhZGVycyB0byBtYWtlIGNhbGN1bGF0ZWQgZGVjaXNpb25zIGluIGRheSB0cmFkaW5nLiBUcmFkZWF3YWF5IHByb3ZpZGVzIGZ1bGwgdHJhZGluZyBleHBlcmllbmNlIGJ5IGNvbm5lY3RpbmcgdG8geW91ciBNZXRhVHJhZGVyIDUgdGhyb3VnaCBFeHBlcnQgQWR2aXNvci5cIi8+XFxuJyxcclxuICAgICc8bWV0YSBwcm9wZXJ0eT1cIm9nOnR5cGVcIiBjb250ZW50PVwid2Vic2l0ZVwiLz4nXHJcbiAgXTtcclxuXHJcbiAgY29uc3QgZW52ID0gbG9hZEVudihcInByb2R1Y3Rpb25cIiwgcHJvY2Vzcy5jd2QoKSwgXCJcIik7XHJcblxyXG4gIHJldHVybiB7XHJcbiAgICBuYW1lOiBcImh0bWwtdHJhbnNmb3JtXCIsXHJcbiAgICB0cmFuc2Zvcm1JbmRleEh0bWwoaHRtbDogc3RyaW5nKSB7XHJcbiAgICAgIGlmIChlbnYuVklURV9BUFBfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xyXG4gICAgICAgIGxldCB0YWdzID0gXCJcIjtcclxuXHJcbiAgICAgICAgc2VvVGFncy5mb3JFYWNoKCh0KSA9PiB7XHJcbiAgICAgICAgICB0YWdzICs9IHQ7XHJcbiAgICAgICAgfSk7XHJcblxyXG4gICAgICAgIGh0bWwgPSBodG1sLnJlcGxhY2UoXHJcbiAgICAgICAgICAnPG1ldGEgbmFtZT1cInJvYm90c1wiIGNvbnRlbnQ9XCJub2luZGV4LCBub2ZvbGxvd1wiIC8+JyxcclxuICAgICAgICAgIHRhZ3NcclxuICAgICAgICApO1xyXG4gICAgICB9XHJcblxyXG4gICAgICByZXR1cm4gaHRtbDtcclxuICAgIH1cclxuICB9O1xyXG59XHJcbiJdLAogICJtYXBwaW5ncyI6ICI7QUFBa1QsT0FBTyxVQUFVO0FBQ25VLFNBQVMsb0JBQW9CO0FBQzdCLE9BQU8sZUFBZTs7O0FDRmtVLFNBQVMsZUFBZTtBQUV6VyxTQUFTLGNBQWMsRUFBRSxVQUFVLEdBQTBCO0FBQ2xFLFNBQU87QUFBQSxJQUNMLE1BQU07QUFBQSxJQUNOLFVBQVUsTUFBVyxJQUFZO0FBQy9CLFVBQUksVUFBVSxLQUFLLEVBQUUsR0FBRztBQUN0QixjQUFNLE9BQU8sS0FBSyxVQUFVLElBQUksRUFDN0IsUUFBUSxXQUFXLFNBQVMsRUFDNUIsUUFBUSxXQUFXLFNBQVM7QUFFL0IsZUFBTztBQUFBLFVBQ0wsTUFBTSxrQkFBa0IsSUFBSTtBQUFBLFFBQzlCO0FBQUEsTUFDRjtBQUFBLElBQ0Y7QUFBQSxFQUNGO0FBQ0Y7QUFFTyxTQUFTLE1BQU07QUFDcEIsUUFBTSxVQUFVO0FBQUEsSUFDZDtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxJQUNBO0FBQUEsSUFDQTtBQUFBLElBQ0E7QUFBQSxFQUNGO0FBRUEsUUFBTSxNQUFNLFFBQVEsY0FBYyxRQUFRLElBQUksR0FBRyxFQUFFO0FBRW5ELFNBQU87QUFBQSxJQUNMLE1BQU07QUFBQSxJQUNOLG1CQUFtQixNQUFjO0FBQy9CLFVBQUksSUFBSSxpQkFBaUIsY0FBYztBQUNyQyxZQUFJLE9BQU87QUFFWCxnQkFBUSxRQUFRLENBQUMsTUFBTTtBQUNyQixrQkFBUTtBQUFBLFFBQ1YsQ0FBQztBQUVELGVBQU8sS0FBSztBQUFBLFVBQ1Y7QUFBQSxVQUNBO0FBQUEsUUFDRjtBQUFBLE1BQ0Y7QUFFQSxhQUFPO0FBQUEsSUFDVDtBQUFBLEVBQ0Y7QUFDRjs7O0FEakRBLE9BQU8sU0FBUztBQU5oQixJQUFNLG1DQUFtQztBQVF6QyxJQUFPLHNCQUFRLGFBQWE7QUFBQSxFQUMxQixTQUFTO0FBQUEsSUFDUCxJQUFJO0FBQUEsSUFDSixJQUFJO0FBQUEsSUFDSixVQUFVO0FBQUEsSUFDVixjQUFjO0FBQUEsTUFDWixXQUFXO0FBQUEsSUFDYixDQUFDO0FBQUEsRUFDSDtBQUFBLEVBQ0EsU0FBUztBQUFBLElBQ1AsT0FBTztBQUFBLE1BQ0wsS0FBSyxLQUFLLFFBQVEsa0NBQVcsUUFBUTtBQUFBLE1BQ3JDLFlBQVksS0FBSyxRQUFRLGtDQUFXLHNCQUFzQjtBQUFBLElBQzVEO0FBQUEsRUFDRjtBQUNGLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
