<script setup lang="ts">
import { onMounted, ref } from "vue";
import { cloneDeep } from "lodash-es";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";
import { IText } from "@/lib/night-vision/shapes/base/shape-properties.types";

import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";
import { useChartStore } from "@/store/chartStore";
import { useAppbarStore } from "@/store/appbarStore";

import TextFontColor from "./TextFontColor.vue";
import TextFontSize from "./TextFontSize.vue";

import Textarea from "@/components/Textarea.vue";
import Dropdown from "@/components/Dropdown.vue";
import ColorPicker from "@/components/ColorPicker.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const chartStore = useChartStore();
const appbarStore = useAppbarStore();

const selectedShape: IBaseShapeOptions<"text"> | undefined =
  chartStore.selectedShape;

const text = ref("");
const backgroundColorDropdown = ref(false);
const selectedBackgroundColor = ref("");

let previousShapeSettings = {} as {
  properties: IText;
  backgroundProperties: {
    fill_color: string;
  };
  text: string;
};

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set text shape properties");
  }

  selectedBackgroundColor.value =
    selectedShape.properties.textProperties.bg_color;

  text.value = selectedShape.textData;

  previousShapeSettings = {
    properties: cloneDeep(selectedShape.properties.textProperties),
    backgroundProperties: {
      fill_color: selectedBackgroundColor.value
    },
    text: text.value
  };
});

function closeModal() {
  appbarStore.toggleModal("chartSettingsModal", false);
}

function updateText() {
  if (!selectedShape) {
    throw new ShapeInstanceError("Cannot update text for text shape");
  }

  selectedShape.setText(text.value);
}

function handleBackgroundColor(color: string) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Cannot change text background color");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Cannot change text background color");
  }

  selectedShape.setProperty("textProperties", "bg_color", color);

  selectedBackgroundColor.value = color;

  selectedShape.setProperty(
    // @ts-expect-error
    "backgroundProperties",
    "fill_color",
    previousShapeSettings.backgroundProperties.fill_color
  );

  chartStore.chart.update();
}

function restorePreviousSettings() {
  if (!selectedShape) {
    throw new ShapeInstanceError(
      "Unable to restore previous settings for text shape"
    );
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError(
      "Unable to restore previous settings for text shape"
    );
  }

  for (const [k, v] of Object.entries(previousShapeSettings.properties)) {
    selectedShape.setProperty("textProperties", k as keyof IText, v);
  }

  // No Typescript auto completion for textData and setText, need to fix this.
  selectedShape.setText(previousShapeSettings.text);

  chartStore.chart.update();

  closeModal();
}
</script>

<template>
  <div class="scrollbar h-[300px] overflow-auto border-t px-4 py-3">
    <div class="grid grid-cols-12 items-center">
      <div class="col-span-4">Format</div>

      <div class="col-span-8 flex gap-x-3">
        <TextFontColor />

        <TextFontSize />
      </div>
    </div>

    <div class="mt-3">
      <Textarea v-model="text" @input="updateText" />
    </div>

    <div class="mt-3 grid grid-cols-12 items-center">
      <div class="col-span-4">Background</div>

      <div class="col-span-8">
        <Dropdown
          id="chart-modal-fib-one-color-dropdown"
          toggle-id="chart-modal-fib-one-color-toggle-dropdown"
          class="flex h-9 w-9 items-center justify-center border !p-0"
          :class="{
            'border-info': backgroundColorDropdown
          }"
          :icon="false"
          :offset-skidding="110"
          @show="backgroundColorDropdown = true"
          @hide="backgroundColorDropdown = false"
        >
          <template #text>
            <img
              width="20"
              height="20"
              src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQbYhroPyAlU14fdkwhbx3ullUY4SPp3lOjpq2HA-K3An2FbdJaxhvCjSiHbmZNUvRPzPE&usqp=CAU"
              alt="Transparent Image"
              v-if="selectedBackgroundColor.slice(0, 7) === '#ffffff'"
            />

            <div
              class="h-[20px] w-[20px]"
              :style="{
                backgroundColor: selectedBackgroundColor.slice(0, 7)
              }"
              v-else
            ></div>
          </template>

          <template #content>
            <div>
              <ColorPicker
                :color="selectedBackgroundColor"
                :show-opacity="true"
                @update-color="handleBackgroundColor"
              />
            </div>
          </template>
        </Dropdown>
      </div>
    </div>
  </div>

  <div class="flex justify-end gap-x-3 border-t px-3 pb-1.5 pt-2">
    <PrimaryButton class="px-3" @click="closeModal">OK</PrimaryButton>

    <PrimaryButton
      class="border border-info bg-white !text-info hover:border-secondary hover:!text-white"
      @click="restorePreviousSettings"
    >
      Discard
    </PrimaryButton>
  </div>
</template>
