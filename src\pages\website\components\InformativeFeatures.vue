<script setup lang="ts">
import FeatureCard from "./FeatureCard.vue";

const videoIds = {
  setup: "4pT8Np3at7Q",
  trading: "lF1qMs7G0Cw",
  risk: "-ujC2f6deeY"
};

const features = [
  {
    title: "Easy Set Up",
    description:
      "Quickly connect to your MetaTrader with Expert Advisor and sync your data with Tradeawaay for smooth, integrated trading. With a user-friendly setup and reliable connectivity, you'll enjoy a more seamless and powerful trading experience.",
    icon: "Code2",
    videoId: videoIds.setup
  },
  {
    title: "Intuitive Trading Experience",
    description:
      "Utilize advanced trading features directly on charts, including click-to-trade, auto position sizing, view pre-execution trade parameters, partial position close and trailing stop loss, modify open trade, custom position closure.",
    icon: "BarChart2",
    videoId: videoIds.trading
  },
  {
    title: "Risk Management Tools",
    description:
      "Leverage risk tools to assess overall risk in alignment with your trading objectives. Gain access to comprehensive account-level analytics to evaluate your performance, and obtain a clear snapshot of the total risk from your open positions.",
    icon: "Shield",
    videoId: videoIds.risk
  },
  {
    title: "Support",
    description:
      "Our platform is a learning hub designed to help you discover new trading strategies and techniques. Our mission is to support users in reaching their trading goals by offering algorithms that keep you on track for success.",
    icon: "ArrowUpRight"
  }
];
</script>

<template>
  <div class="min-h-screen bg-gradient-to-b from-indigo-50 to-white">
    <div class="mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8">
      <header class="mb-16 text-center">
        <h1 class="mb-6 text-2xl font-bold text-gray-900 md:text-5xl">
          Our Trading Platform Connected to MT5
        </h1>
        <p class="mx-auto max-w-3xl text-xl text-gray-600">
          Experience seamless integration with MetaTrader for professional
          trading
        </p>
      </header>

      <div class="mb-16 grid gap-12 md:grid-cols-2">
        <FeatureCard
          v-for="(feature, index) in features"
          :key="index"
          :title="feature.title"
          :description="feature.description"
          :icon="feature.icon"
          :videoId="feature.videoId"
        />
      </div>

      <div
        class="rounded-2xl bg-gradient-to-r from-blue-100 to-purple-200 p-8 text-center text-black"
      >
        <h2 class="mb-4 text-3xl font-bold">More Features — coming soon</h2>
        <p class="mx-auto max-w-3xl text-lg opacity-90">
          We have an exciting list of features in the works that we can't wait
          to share with our community, including leaderboard, copy trading,
          advanced price action indicators, pattern recognition ML models, and
          integration with multiple trading systems like MT4, cTrader, and
          DXTrade. Stay tuned!
        </p>
      </div>
    </div>
  </div>
</template>
