<script setup lang="ts">
import { ref } from "vue";

import { DateTime } from "luxon";

import { useUserStore } from "@/store/userStore";

import NavItem from "@/components/NavItem.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

import { axios } from "@/api";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

defineProps<{
  subscriptionEndDate: string;
}>();

const emit = defineEmits<{
  back: [];
  cancel: [message: string[]];
}>();

const userStore = useUserStore();

const btnLoading = ref(false);

async function handleCancelSubscription() {
  try {
    btnLoading.value = true;

    await axios.post("payments/stripe/subscription/cancel");

    const userResp = await axios.get("/user");

    userStore.user = userResp.data.data;

    const cancelEffectiveDate = userStore.user?.payment?.cancel_effective_date;

    const endDate = DateTime.fromSeconds(
      parseInt(cancelEffectiveDate!)
    ).toFormat("dd MMM, yyyy");

    const messages = [
      "Your subscription plan has been cancelled.",
      `However, your plan is active till ${endDate}. You may continue to use app until then.`
    ];

    emit("cancel", messages);
  } catch (e) {
    console.error(e);
  } finally {
    btnLoading.value = false;
  }
}
</script>

<template>
  <div class="px-5 text-sm">
    <div class="-ml-5 flex items-center gap-x-3 pl-3">
      <NavItem class="!rounded-full px-3 pb-2 pt-2.5" @click="$emit('back')">
        <FontAwesomeIcon size="xl" icon="fa-solid fa-arrow-left" />
      </NavItem>

      <div class="text-lg font-bold">Cancel Your Plan</div>
    </div>

    <div class="mt-2 text-lg font-semibold">
      You're about to cancel your subscription
    </div>

    <div class="mt-3 text-gray-600">
      Once your subscription expires on your next invoice date, following things
      will happen:

      <ul class="mt-3 list-disc px-5">
        <li>You'll lose access to Tradeway.</li>
        <li>You can no longer use our services for trading.</li>
      </ul>
    </div>

    <div class="mt-5 text-gray-600">
      However, your plan is active till

      <span class="font-bold">{{ subscriptionEndDate }}.</span>

      You may continue to use app until then.
    </div>

    <div class="mt-6 flex justify-center gap-x-3 border-t pt-7">
      <PrimaryButton class="border border-primary px-5" @click="$emit('back')">
        No, Go Back
      </PrimaryButton>

      <PrimaryButton
        class="flex w-36 items-center justify-center gap-x-2 !bg-red-600 hover:!bg-red-700"
        :loading="btnLoading"
        @click="handleCancelSubscription"
      >
        Yes, Cancel It
        <FontAwesomeIcon size="sm" icon="fa-solid fa-chevron-right" />
      </PrimaryButton>
    </div>
  </div>
</template>
