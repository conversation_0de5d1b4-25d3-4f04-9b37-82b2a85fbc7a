<script setup lang="ts">
import { ref } from "vue";

import { useUserStore } from "@/store/userStore";

import ProfileHeader from "./ProfileHeader.vue";

import Alert from "@/components/Alert.vue";
import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const userStore = useUserStore();

const currentEmail = ref(userStore.user?.email);
const email = ref("");
</script>

<template>
  <ProfileHeader text="Change Email" route-name="edit" />

  <div class="px-5">
    <Alert variant="info">
      Your current email address is
      <span class="font-semibold"> {{ currentEmail }} </span>. If you change
      your email address, all account related activities and notifications will
      be sent to your new email address.
    </Alert>

    <div class="mt-5">
      <InputLabel for="email">New Email Address</InputLabel>

      <InputText id="email" type="email" v-model="email" />
    </div>

    <div class="mt-5">
      <PrimaryButton class="w-full">Save Changes</PrimaryButton>
    </div>
  </div>
</template>
