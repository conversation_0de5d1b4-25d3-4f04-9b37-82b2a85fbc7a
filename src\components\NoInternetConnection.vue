<script setup lang="ts">
import PrimaryButton from "./PrimaryButton.vue";

import WifiOffSVG from "@/assets/svg/wifi-off.svg";

function reloadPage() {
  location.reload();
}
</script>

<template>
  <div
    class="absolute left-0 top-0 z-20 grid h-full w-full place-items-center bg-accent"
  >
    <div class="flex flex-col items-center">
      <div>
        <WifiOffSVG width="60" height="60" />
      </div>

      <div class="mt-5 flex flex-col items-center">
        <div>Connect to the internet</div>

        <div>You're offline. Check your connection.</div>

        <div class="mt-4">
          <PrimaryButton class="px-3" @click="reloadPage">Retry</PrimaryButton>
        </div>
      </div>
    </div>

    <div class="absolute bottom-0 z-20 w-full text-center text-sm font-medium">
      No internet connection
    </div>
  </div>
</template>
