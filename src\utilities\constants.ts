export const Indicators = {
  "ACCUMULATION/DISTRIBUTION": "Accumulation/Distribution",
  "ACCUMULATIVE SWING INDEX": "Accumulative Swing Index",
  "ADVANCE/DECLINE": "Advance/Decline",
  "ARNAUD LEGOUX MOVING AVERAGE": "Arnaud <PERSON>ux Moving Average",
  AROON: "Aroon",
  "AVERAGE DIRECTIONAL INDEX": "Average Directional Index",
  "AVERAGE PRICE": "Average Price",
  "AVERAGE TRUE RANGE": "Average True Range",
  "AWESOME OSCILLATOR": "Awesome Oscillator",
  "BALANCE OF POWER": "Balance of Power",
  "BOLLINGER BANDS": "Bollinger Bands",
  "BOLLINGER BANDS %B": "Bollinger Bands %B",
  "BOLLINGER BANDS WIDTH": "Bollinger Bands Width",
  "CHAIKIN MONEY FLOW": "Chaikin Money Flow",
  "CHAIKIN OSCILLATOR": "Chaikin Oscillator",
  "CHAIKIN VOLATILITY": "Chaikin Volatility",
  "CHANDE KROLL STOP": "Chande Kroll Stop",
  "CHANDE MOMENTUM OSCILLATOR": "Chande Momentum Oscillator",
  "CHOP ZONE": "Chop Zone",
  "CHOPPINESS INDEX": "Choppiness Index",
  "COMMODITY CHANNEL INDEX": "Commodity Channel Index",
  "CONNORS RSI": "Connors RSI",
  "COPPOCK CURVE": "Coppock Curve",
  "CORRELATION - LOG": "Correlation - Log",
  "CORRELATION COEFFICIENT": "Correlation Coefficient",
  "DETRENDED PRICE OSCILLATOR": "Detrended Price Oscillator",
  "DIRECTIONAL MOVEMENT": "Directional Movement",
  "DONCHIAN CHANNELS": "Donchian Channels",
  "DOUBLE EMA": "Double EMA",
  "EASE OF MOVEMENT": "Ease Of Movement",
  "ELDER'S FORCE INDEX": "Elder's Force Index",
  "EMA CROSS": "EMA Cross",
  ENVELOPES: "Envelopes",
  "FISHER TRANSFORM": "Fisher Transform",
  "GUPPY MULTIPLE MOVING AVERAGE": "Guppy Multiple Moving Average",
  "HISTORICAL VOLATILITY": "Historical Volatility",
  "HULL MOVING AVERAGE": "Hull Moving Average",
  "ICHIMOKU CLOUD": "Ichimoku Cloud",
  "KELTNER CHANNELS": "Keltner Channels",
  "KLINGER OSCILLATOR": "Klinger Oscillator",
  "KNOW SURE THING": "Know Sure Thing",
  "LEAST SQUARES MOVING AVERAGE": "Least Squares Moving Average",
  "LINEAR REGRESSION CURVE": "Linear Regression Curve",
  "LINEAR REGRESSION SLOPE": "Linear Regression Slope",
  "MA CROSS": "MA Cross",
  "MA WITH EMA CROSS": "MA with EMA Cross",
  MACD: "MACD",
  "MAJORITY RULE": "Majority Rule",
  "MASS INDEX": "Mass Index",
  "MCGINLEY DYNAMIC": "McGinley Dynamic",
  "MEDIAN PRICE": "Median Price",
  MOMENTUM: "Momentum",
  "MONEY FLOW INDEX": "Money Flow Index",
  "MOVING AVERAGE": "Moving Average",
  "MOVING AVERAGE ADAPTIVE": "Moving Average Adaptive",
  "MOVING AVERAGE CHANNEL": "Moving Average Channel",
  "MOVING AVERAGE DOUBLE": "Moving Average Double",
  "MOVING AVERAGE EXPONENTIAL": "Moving Average Exponential",
  "MOVING AVERAGE HAMMING": "Moving Average Hamming",
  "MOVING AVERAGE MULTIPLE": "Moving Average Multiple",
  "MOVING AVERAGE TRIPLE": "Moving Average Triple",
  "MOVING AVERAGE WEIGHTED": "Moving Average Weighted",
  "NET VOLUME": "Net Volume",
  "ON BALANCE VOLUME": "On Balance Volume",
  "PARABOLIC SAR": "Parabolic SAR",
  "PIVOT POINTS STANDARD": "Pivot Points Standard",
  "PRICE CHANNEL": "Price Channel",
  "PRICE OSCILLATOR": "Price Oscillator",
  "PRICE VOLUME TREND": "Price Volume Trend",
  "RATE OF CHANGE": "Rate Of Change",
  RATIO: "Ratio",
  "RELATIVE STRENGTH INDEX": "Relative Strength Index",
  "RELATIVE VIGOR INDEX": "Relative Vigor Index",
  "RELATIVE VOLATILITY INDEX": "Relative Volatility Index",
  "SMI ERGODIC INDICATOR/OSCILLATOR": "SMI Ergodic Indicator/Oscillator",
  "SMOOTHED MOVING AVERAGE": "Smoothed Moving Average",
  SPREAD: "Spread",
  "STANDARD DEVIATION": "Standard Deviation",
  "STANDARD ERROR": "Standard Error",
  "STANDARD ERROR BANDS": "Standard Error Bands",
  STOCHASTIC: "Stochastic",
  "STOCHASTIC RSI": "Stochastic RSI",
  SUPERTREND: "SuperTrend",
  "TREND STRENGTH INDEX": "Trend Strength Index",
  "TRIPLE EMA": "Triple EMA",
  TRIX: "TRIX",
  "TRUE STRENGTH INDEX": "True Strength Index",
  "TYPICAL PRICE": "Typical Price",
  "ULTIMATE OSCILLATOR": "Ultimate Oscillator",
  "VOLATILITY CLOSE-TO-CLOSE": "Volatility Close-to-Close",
  "VOLATILITY INDEX": "Volatility Index",
  "VOLATILITY O-H-L-C": "Volatility O-H-L-C",
  "VOLATILITY ZERO TREND CLOSE-TO-CLOSE":
    "Volatility Zero Trend Close-to-Close",
  VOLUME: "Volume",
  "VOLUME OSCILLATOR": "Volume Oscillator",
  "VOLUME PROFILE FIXED RANGE": "Volume Profile Fixed Range",
  "VOLUME PROFILE VISIBLE RANGE": "Volume Profile Visible Range",
  "VORTEX INDICATOR": "Vortex Indicator",
  VWAP: "VWAP",
  VWMA: "VWMA",
  "WILLIAMS %R": "Williams %R",
  "WILLIAMS ALLIGATOR": "Williams Alligator",
  "WILLIAMS FRACTAL": "Williams Fractal",
  "ZIG ZAG": "Zig Zag"
};

export const COLORS = [
  "#ff6633",
  "#ffb399",
  "#ff33ff",
  "#ffff99",
  "#00b3e6",
  "#e6b333",
  "#3366e6",
  "#999966",
  "#99ff99",
  "#b34d4d",
  "#80b300",
  "#809900",
  "#e6b3b3",
  "#6680b3",
  "#66991a",
  "#ff99e6",
  "#ccff1a",
  "#ff1a66",
  "#e6331a",
  "#33ffcc",
  "#66994d",
  "#b366cc",
  "#4d8000",
  "#b33300",
  "#cc80cc",
  "#66664d",
  "#991aff",
  "#e666ff",
  "#4db3ff",
  "#1ab399",
  "#e666b3",
  "#33991a",
  "#cc9999",
  "#b3b31a",
  "#00e680",
  "#4d8066",
  "#809980",
  "#e6ff80",
  "#1aff33",
  "#999933",
  "#ff3380",
  "#cccc00",
  "#66e64d",
  "#4d80cc",
  "#9900b3",
  "#e64d66",
  "#4db380",
  "#ff4d4d",
  "#6666ff"
];

export const VUE_PRODUCTION_ERROR_CODES = {
  0: "setup function",
  1: "render function",
  2: "watcher getter",
  3: "watcher callback",
  4: "watcher cleanup function",
  5: "native event handler",
  6: "component event handler",
  7: "vnode hook",
  8: "directive hook",
  9: "transition hook",
  10: "app errorHandler",
  11: "app warnHandler",
  12: "ref function",
  13: "async component loader",
  14: "scheduler flush",
  15: "component update",
  16: "app unmount cleanup function",
  sp: "serverPrefetch hook",
  bc: "beforeCreate hook",
  c: "created hook",
  bm: "beforeMount hook",
  m: "mounted hook",
  bu: "beforeUpdate hook",
  u: "updated",
  bum: "beforeUnmount hook",
  um: "unmounted hook",
  a: "activated hook",
  da: "deactivated hook",
  ec: "errorCaptured hook",
  rtc: "renderTracked hook",
  rtg: "renderTriggered hook"
};

export const SECOND = 1000;
export const MINUTE = SECOND * 60;
export const HOUR = MINUTE * 60;
