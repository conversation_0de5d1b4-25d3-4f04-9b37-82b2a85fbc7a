<script setup lang="ts">
import { onMounted, ref } from "vue";

import { Check, Star } from "lucide-vue-next";

import { axios } from "@/api";
import { SubscriptionPlan } from "@/types";

const hoveredPlan = ref<number | null>(null);

const subscriptionPlans = ref<SubscriptionPlan[]>([]);

onMounted(async () => {
  try {
    const resp = await axios.get("/payments/subscription-plan", {
      params: {
        trial: false
      }
    });

    subscriptionPlans.value = resp.data.data;
  } catch (e) {
    console.error(e);
  }
});
</script>

<template>
  <section id="pricing" class="bg-gradient-to-b from-blue-50 to-white py-16">
    <div class="mx-auto max-w-screen-xl px-4">
      <div class="text-center">
        <h2 class="text-4xl font-bold text-gray-900">
          <span class="relative"> Pricing Plans </span>
        </h2>
        <p class="mt-6 text-lg text-gray-600">
          Choose the perfect plan for your needs
        </p>
      </div>

      <div class="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-2">
        <div
          v-for="(plan, idx) in subscriptionPlans"
          :key="plan.name"
          class="relative rounded-2xl transition-transform duration-200 ease-out"
          :class="{ '-translate-y-2 transform': hoveredPlan === idx }"
          @mouseenter="hoveredPlan = idx"
          @mouseleave="hoveredPlan = null"
        >
          <div
            v-if="idx === 1"
            class="absolute -top-4 left-0 right-0 mx-auto w-32 rounded-full bg-blue-700 py-1 text-center text-sm font-semibold text-white"
          >
            <div class="flex items-center justify-center gap-1">
              <Star class="h-4 w-4" />
              Most Popular
            </div>
          </div>

          <div
            class="h-full rounded-2xl border-2 p-8 shadow-xl"
            :class="[
              idx === 1
                ? 'border-blue-500 bg-white shadow-blue-200'
                : 'border-gray-200 bg-white'
            ]"
          >
            <h3 class="text-xl font-semibold text-gray-900">
              {{ plan.service_name }}
            </h3>
            <div class="mt-4 flex items-baseline">
              <span class="text-4xl font-bold text-gray-900"
                >${{ plan.charge }}</span
              >
              <span class="ml-1 text-gray-500">/{{ plan.duration }}</span>
            </div>

            <ul class="mt-8 space-y-4">
              <li
                v-for="(feature, featureIdx) in plan.features"
                :key="featureIdx"
                class="flex items-center gap-3"
              >
                <Check class="h-5 w-5 text-blue-600" />
                <span class="text-gray-600">{{ feature }}</span>
              </li>
            </ul>

            <router-link
              :to="{
                name: 'register',
                query: { subscription_plan: plan.name }
              }"
            >
              <button
                class="mt-8 w-full cursor-pointer rounded-lg px-6 py-3 text-center font-semibold transition-all duration-200 ease-out"
                :class="[
                  idx === 1
                    ? 'bg-blue-700 text-white hover:bg-blue-800'
                    : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                ]"
              >
                Register
              </button>
            </router-link>
          </div>
        </div>
      </div>

      <!-- <p class="mt-10 text-center text-sm text-gray-500">
        *Free trial available for new users only. Terms and conditions apply.
      </p> -->
    </div>
  </section>
</template>
