<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from "vue";

import {
  ILabelInfo,
  OrderData,
  OrderExecution,
  OrderType
} from "@/lib/night-vision/shapes/NewOrder";

import { useAppbarStore } from "@/store/appbarStore";
import { useBottomNavDrawerStore } from "@/store/bottomNavDrawerStore";
import { useCandleStickStore } from "@/store/candleStickStore";
import { useChartStore } from "@/store/chartStore";
import { useLeftNavDrawerStore } from "@/store/leftNavDrawerStore";
import { useTradeShapeStore } from "@/store/tradeShapeStore";
import { useUserStore } from "@/store/userStore";

import { useLabelFunctions } from "@/chart-frontend/labelUtils";
import { handleRemoveSelectedTradeShape } from "@/chart-frontend/utils";

import Checkbox from "@/components/Checkbox.vue";
import Dropdown from "@/components/Dropdown.vue";
import InputLabel from "@/components/InputLabel.vue";
import NavItem from "@/components/NavItem.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";
import Tooltip from "@/components/Tooltip.vue";

import {
  EActiveOrders,
  EPlacedOrders,
  ETradeOperationType
} from "@/types/enums";

import DeleteTradeModal from "./DeleteActiveTradeModal.vue";
import EditTradeModal from "./EditTradeModal.vue";
import { eaSocket } from "@/socketio";
import { IActiveOrders, IPlacedOrders } from "@/types";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

interface ITrade {
  action: string;
  symbol: string;
  sl: number;
  tp: number;
  ticket: number;
  price: number;
  type_time?: string;
  expiration?: string;

  type: string;
  volume: number;
  tp1?: number;
  tp1_status?: string;
  vol1?: number;
  vol1_status?: string;
  sl_trail?: number;
}

const userStore = useUserStore();
const chartStore = useChartStore();
const appbarStore = useAppbarStore();
const tradeShapeStore = useTradeShapeStore();
const candleStickStore = useCandleStickStore();
const leftNavDrawerStore = useLeftNavDrawerStore();
const bottomNavDrawerStore = useBottomNavDrawerStore();

const tableHeaders = ref([
  {
    id: "open_time",
    text: "Open Time",
    toggle: true
  },
  {
    id: "symbol",
    text: "Symbol",
    toggle: true
  },
  {
    id: "ticket",
    text: "Ticket",
    toggle: true
  },
  {
    id: "type",
    text: "Type",
    toggle: true
  },
  {
    id: "volume",
    text: "Volume",
    toggle: true
  },
  {
    id: "sl",
    text: "SL",
    toggle: true
  },
  {
    id: "tp",
    text: "TP",
    toggle: true
  },
  {
    id: "tp1",
    text: "TP1",
    toggle: true
  },
  {
    id: "vol1",
    text: "TP1 Vol",
    toggle: true
  },
  {
    id: "open_price",
    text: "Open Price",
    toggle: true
  },
  {
    id: "current_price",
    text: "Price",
    toggle: true
  },
  {
    id: "comment",
    text: "Comment",
    toggle: false
  },
  {
    id: "sl_trail",
    text: "SL Trail",
    toggle: false
  },
  {
    id: "margin",
    text: "Margin (%)",
    toggle: true
  },
  {
    id: "exp_profit",
    text: "Exp Profit (%)",
    toggle: true
  },
  {
    id: "exp_loss",
    text: "Exp Loss (%)",
    toggle: true
  },
  {
    id: "swap",
    text: "Swap",
    toggle: false
  },
  {
    id: "profit",
    text: "Profit (%)",
    toggle: true
  }
]);
const toggleTableHeaders = ref([
  {
    id: "comment",
    text: "Comment"
  },
  {
    id: "sl_trail",
    text: "SL Trail"
  },
  {
    id: "swap",
    text: "Swap"
  }
]);
const selectedToggleHeaders = ref<string[]>([]);
const sortColumn = ref("");
const sortDirection = ref("");
const editOrderValues = ref<ITrade>({
  action: "",
  type: "",
  symbol: "",
  sl: 0,
  tp: 0,
  ticket: 0,
  volume: 0,
  price: 0,
  tp1: 0,
  tp1_status: "",
  vol1: 0,
  vol1_status: "",
  sl_trail: 0,
  type_time: "",
  expiration: ""
});
const closeAllTradeModal = ref(false);
const deleteTrade = ref({
  symbol: "",
  tp: 0,
  ticket: 0,
  volume: 0,
  open_price: 0
});

const selectedTrade = ref<IActiveOrders | IPlacedOrders | null>(null);
const selectedTradeType = ref<string | null>(null);
const selectedTradeBuyOrSell = ref<"buy" | "sell" | null>(null);

const { calculateSlLabels, calculateTpLabels } = useLabelFunctions();

onMounted(() => {
  initializeTableHeaders();

  chartStore.shapeToolInstance?.addListener(
    "placeneworder",
    "update-sl-value",
    (value: number) => {
      if (
        tradeShapeStore.selectedTradeShape &&
        selectedTrade.value &&
        selectedTradeBuyOrSell.value
      ) {
        const labelInfo: ILabelInfo = calculateSlLabels(
          {
            volume: selectedTrade.value.volume,
            price: selectedTrade.value.open_price,
            sl: value,
            orderType: selectedTradeBuyOrSell.value
          },
          selectedTrade.value.symbol
        );

        tradeShapeStore.selectedTradeShape.updateStopLoss(
          Number(value),
          labelInfo
        );
        chartStore.chart?.update();
      }
    }
  );

  chartStore.shapeToolInstance?.addListener(
    "placeneworder",
    "update-tp-value",
    (value: number) => {
      if (
        tradeShapeStore.selectedTradeShape &&
        selectedTrade.value &&
        selectedTradeBuyOrSell.value
      ) {
        const labelInfo: ILabelInfo = calculateTpLabels(
          {
            volume: selectedTrade.value.volume,
            price: selectedTrade.value.open_price,
            tp: value,
            orderType: selectedTradeBuyOrSell.value
          },
          selectedTrade.value.symbol
        );

        tradeShapeStore.selectedTradeShape.updateTakeProfit(
          Number(value),
          labelInfo
        );
        chartStore.chart?.update();
      }
    }
  );

  chartStore.shapeToolInstance?.addListener(
    "placeneworder",
    "update-tp1-value",
    (value: number) => {
      if (
        tradeShapeStore.selectedTradeShape &&
        selectedTrade.value &&
        selectedTradeBuyOrSell.value
      ) {
        const labelInfo: ILabelInfo = calculateTpLabels(
          {
            volume: selectedTrade.value.volume,
            price: selectedTrade.value.open_price,
            tp: value,
            orderType: selectedTradeBuyOrSell.value
          },
          selectedTrade.value.symbol
        );

        tradeShapeStore.selectedTradeShape.updateTakeProfit1(
          Number(value),
          labelInfo
        );
        chartStore.chart?.update();
      }
    }
  );

  chartStore.shapeToolInstance?.addListener(
    "shapetool",
    "sl-drag-edit",
    (sl: any) => {
      if (
        selectedTrade.value &&
        selectedTradeType.value &&
        tradeShapeStore.selectedTradeShape
      ) {
        handleTradeEditOrder(selectedTrade.value, selectedTradeType.value);

        editOrderValues.value.sl = sl;

        bottomNavDrawerStore.toggleEditTradeModal(true);
      }
    }
  );

  chartStore.shapeToolInstance?.addListener(
    "shapetool",
    "tp-drag-edit",
    (tp: any) => {
      if (
        selectedTrade.value &&
        selectedTradeType.value &&
        tradeShapeStore.selectedTradeShape
      ) {
        handleTradeEditOrder(selectedTrade.value, selectedTradeType.value);

        editOrderValues.value.tp = tp;

        bottomNavDrawerStore.toggleEditTradeModal(true);
      }
    }
  );

  chartStore.shapeToolInstance?.addListener(
    "shapetool",
    "tp1-drag-edit",
    (tp1: any) => {
      if (
        selectedTrade.value &&
        selectedTradeType.value &&
        tradeShapeStore.selectedTradeShape
      ) {
        handleTradeEditOrder(selectedTrade.value, selectedTradeType.value);

        editOrderValues.value.tp1 = tp1;
        bottomNavDrawerStore.updateTp1 = true;

        bottomNavDrawerStore.toggleEditTradeModal(true);
      }
    }
  );

  chartStore.shapeToolInstance?.addListener(
    "shapetool",
    "price-drag-edit",
    (price: any) => {
      if (
        selectedTrade.value &&
        selectedTradeType.value &&
        tradeShapeStore.selectedTradeShape
      ) {
        handleTradeEditOrder(selectedTrade.value, selectedTradeType.value);

        editOrderValues.value.price = price;

        bottomNavDrawerStore.toggleEditTradeModal(true);
      }
    }
  );
});

watch(
  () => candleStickStore.activeOrders,
  (activeOrders) => {
    if (
      selectedTrade.value &&
      (selectedTrade.value as IActiveOrders).symbol_contract_size
    ) {
      const { ticket } = selectedTrade.value;

      let isTradeActive = false;
      activeOrders.forEach((order) => {
        if (order.ticket === ticket) {
          isTradeActive = true;
        }
      });

      if (!isTradeActive) {
        handleRemoveSelectedTradeShape();
        bottomNavDrawerStore.editTradeModal = false;
      }
    }
  }
);

watch(
  () => candleStickStore.placedOrders,
  (placedOrders) => {
    if (
      selectedTrade.value &&
      !(selectedTrade.value as IActiveOrders).symbol_contract_size
    ) {
      const { ticket } = selectedTrade.value;

      let isTradeActive = false;
      placedOrders.forEach((order) => {
        if (order.ticket === ticket) {
          isTradeActive = true;
        }
      });

      if (!isTradeActive) {
        handleRemoveSelectedTradeShape();
        bottomNavDrawerStore.editTradeModal = false;
      }
    }
  }
);

const tableColspanLength = computed(() => {
  let offsetCol = 0;

  if (selectedToggleHeaders.value.includes("swap")) {
    offsetCol = -1;
  }

  return 11 + selectedToggleHeaders.value.length + offsetCol;
});

const openTradeList = computed(() => {
  if (sortColumn.value === "") {
    return candleStickStore.activeOrders;
  }

  return [...candleStickStore.activeOrders].sort((a, b) => {
    const modifier = sortDirection.value === "asc" ? 1 : -1;

    // @ts-ignore
    if (a[sortColumn.value] < b[sortColumn.value]) return -1 * modifier;

    // @ts-ignore
    if (a[sortColumn.value] > b[sortColumn.value]) return 1 * modifier;

    return 0;
  });
});

const placeOrderList = computed(() => {
  if (sortColumn.value === "") {
    return candleStickStore.placedOrders;
  }

  return [...candleStickStore.placedOrders].sort((a, b) => {
    const modifier = sortDirection.value === "asc" ? 1 : -1;

    // @ts-ignore
    if (a[sortColumn.value] < b[sortColumn.value]) return -1 * modifier;

    // @ts-ignore
    if (a[sortColumn.value] > b[sortColumn.value]) return 1 * modifier;

    return 0;
  });
});

const totalMargin = computed(() => {
  let margin = 0;

  candleStickStore.activeOrders.forEach((order) => {
    margin += order.margin;
  });

  return parseFloat(margin.toFixed(2));
});

const totalExpectedProfit = computed(() => {
  let profit = 0;

  candleStickStore.activeOrders.forEach((order) => {
    profit += order.exp_profit;
  });

  return parseFloat(profit.toFixed(2));
});

const totalExpectedLoss = computed(() => {
  let loss = 0;

  candleStickStore.activeOrders.forEach((order) => {
    loss += order.exp_loss;
  });

  return parseFloat(loss.toFixed(2));
});

watch(
  () => bottomNavDrawerStore.editTradeModal,
  (isModalOpen) => {
    if (
      !isModalOpen &&
      selectedTrade.value &&
      !bottomNavDrawerStore.wasTradeRecentlyUpdated
    ) {
      handleRemoveSelectedTradeShape();
      showSelectedTradeOnChart(selectedTrade.value);
    }
  }
);

watch(
  () => candleStickStore.marketWatchOnlineStatus,
  () => {
    appbarStore.toggleModal("deleteTradeModal", false);
    closeAllTradeModal.value = false;
  }
);

watch(
  () => [tradeShapeStore.selectedTradeShape, selectedTrade.value],
  () => {
    let orderType: "buy" | "sell";
    if (tradeShapeStore.selectedTradeShape && selectedTrade.value) {
      if (
        selectedTrade.value.type === "POSITION_TYPE_BUY" ||
        selectedTrade.value.type === "ORDER_TYPE_BUY_LIMIT"
      ) {
        orderType = "buy";
      } else {
        orderType = "sell";
      }

      selectedTradeBuyOrSell.value = orderType;
    }
  }
);

watch(
  () => [candleStickStore.bidPrice, candleStickStore.askPrice],
  () => {
    if (selectedTrade.value) {
      updatePriceLineValidation(selectedTrade.value.type);

      udpateStopLossLineValidation(selectedTrade.value.type);
    }
  }
);

function initializeTableHeaders() {
  const headers = candleStickStore.chartSettings.open_trades_headers;

  tableHeaders.value.forEach((v) => {
    if (headers.includes(v.id)) {
      v.toggle = true;
    }
  });

  selectedToggleHeaders.value = headers;
}

function handleTableHeaderToggle(id: string) {
  const idx = tableHeaders.value.findIndex((v) => v.id === id);

  const prevState = tableHeaders.value[idx].toggle;

  tableHeaders.value[idx].toggle = !prevState;

  candleStickStore.storeChartSettings({
    open_trades_headers: selectedToggleHeaders.value
  });
}

function handleSorting(column: string) {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === "asc" ? "desc" : "asc";
  } else {
    sortColumn.value = column;
    sortDirection.value = "asc";
  }
}

function toggleSortIcon(column: string, direction: string) {
  return (
    `${sortColumn.value}_${sortDirection.value}` !== `${column}_${direction}`
  );
}

function showSelectedTradeOnChart(item: IActiveOrders | IPlacedOrders) {
  handleRemoveSelectedTradeShape();

  const orderData: OrderData = {
    price: item.open_price,
    stopLoss: item.sl,
    takeProfit: item.tp,
    takeProfit1: (item as IActiveOrders).tp1
  };

  const orderType: OrderType = item.type.toLowerCase().includes("buy")
    ? "buy"
    : "sell";

  const orderExecution: OrderExecution = item.type
    .toLowerCase()
    .includes("position")
    ? "instant"
    : "order";

  const slLabel: ILabelInfo = calculateSlLabels(
    {
      volume: item.volume,
      price: item.open_price,
      sl: item.sl,
      orderType: orderType
    },
    item.symbol
  );

  let volume = item.volume;

  if (
    (item as IActiveOrders).vol1 &&
    (item as IActiveOrders).tp1_status !== "executed"
  ) {
    volume -= (item as IActiveOrders).vol1;
  }

  const tpLabel: ILabelInfo = calculateTpLabels(
    {
      volume,
      price: item.open_price,
      tp: item.tp,
      orderType: orderType
    },
    item.symbol
  );

  const tp1Label: ILabelInfo | null =
    (item as IActiveOrders).tp1_status !== "executed"
      ? calculateTpLabels(
          {
            volume: (item as IActiveOrders).vol1,
            price: item.open_price,
            tp: (item as IActiveOrders).tp1,
            orderType: orderType
          },
          item.symbol
        )
      : null;

  if (!chartStore.shapeToolInstance) {
    throw new Error("Shape tool instance not found");
  }

  tradeShapeStore.selectedTradeShape =
    chartStore.shapeToolInstance?.showSelectedOrder(
      orderData,
      orderType,
      orderExecution,
      slLabel,
      tpLabel,
      tp1Label ? tp1Label : undefined
    );

  updatePriceLineValidation(item.type);

  udpateStopLossLineValidation(item.type);
}

function udpateStopLossLineValidation(itemType: string) {
  if (
    itemType.includes("POSITION_TYPE") &&
    tradeShapeStore.selectedTradeShape
  ) {
    if (itemType.toLowerCase().includes("buy")) {
      tradeShapeStore.selectedTradeShape.stopLossHLine.setMaxValue(
        candleStickStore.askPrice
      );
    } else {
      tradeShapeStore.selectedTradeShape.stopLossHLine.setMinValue(
        candleStickStore.bidPrice
      );
    }
  }
}

function updatePriceLineValidation(itemType: string) {
  const type = EPlacedOrders[itemType as keyof typeof EPlacedOrders];

  if (tradeShapeStore.selectedTradeShape) {
    if (type === EPlacedOrders.ORDER_TYPE_BUY_LIMIT) {
      tradeShapeStore.selectedTradeShape.priceHLine.setMaxValue(
        candleStickStore.askPrice
      );
    }

    if (type === EPlacedOrders.ORDER_TYPE_BUY_STOP) {
      tradeShapeStore.selectedTradeShape.priceHLine.setMinValue(
        candleStickStore.askPrice
      );
    }

    if (type === EPlacedOrders.ORDER_TYPE_SELL_LIMIT) {
      tradeShapeStore.selectedTradeShape.priceHLine.setMinValue(
        candleStickStore.bidPrice
      );
    }

    if (type === EPlacedOrders.ORDER_TYPE_SELL_STOP) {
      tradeShapeStore.selectedTradeShape.priceHLine.setMaxValue(
        candleStickStore.bidPrice
      );
    }
  }
}

async function handleClick(
  event: MouseEvent,
  item: IActiveOrders | IPlacedOrders,
  type: string
) {
  leftNavDrawerStore.togglePlaceNewOrderModal(false);
  tradeShapeStore.removeOrderShape();

  if (event.detail === 1) {
    if (tradeShapeStore.selectedTradeShape) {
      chartStore.shapeToolInstance?.removeOrder(
        tradeShapeStore.selectedTradeShape.uuid
      );
    }

    if (selectedTrade.value && selectedTrade.value.ticket === item.ticket) {
      handleRemoveSelectedTradeShape();
      selectedTrade.value = null;
      selectedTradeType.value = null;
      bottomNavDrawerStore.toggleEditTradeModal(false);
      return;
    }

    selectedTrade.value = item;
    selectedTradeType.value = type;

    if (item) {
      if (candleStickStore.marketWatchSymbol !== item.symbol) {
        await chartStore.changeCurrentSymbol(item.symbol);
        chartStore.handleZoomReset();
      }

      showSelectedTradeOnChart(item);
    }
  }
}

async function handleDoubleClick(
  item: IActiveOrders | IPlacedOrders,
  type: string
) {
  handleTradeEditOrder(item, type);

  await nextTick();

  bottomNavDrawerStore.toggleEditTradeModal(false);

  selectedTrade.value = item;
  selectedTradeType.value = type;

  showSelectedTradeOnChart(item);

  await nextTick();

  bottomNavDrawerStore.toggleEditTradeModal(true);
}

function handleTradeEditOrder(
  item: IActiveOrders | IPlacedOrders,
  type: string
) {
  let editTrade = {} as ITrade;

  editTrade.sl = item.sl;
  editTrade.tp = item.tp;
  editTrade.ticket = item.ticket;
  editTrade.volume = item.volume;
  editTrade.price = item.open_price;
  editTrade.symbol = item.symbol;

  if (type === "active_orders") {
    const activeOrder = item as IActiveOrders;

    editTrade.action = ETradeOperationType.TRADE_ACTION_SLTP;
    // @ts-expect-error
    editTrade.type = EActiveOrders[activeOrder.type];

    editTrade.tp1 = activeOrder.tp1;
    editTrade.tp1_status = activeOrder.tp1_status;
    editTrade.vol1 = activeOrder.vol1;
    editTrade.vol1_status = activeOrder.vol1_status;
    editTrade.sl_trail = activeOrder.sl_trail;

    editOrderValues.value = editTrade;

    return;
  }

  const placedOrder = item as IPlacedOrders;

  editTrade.action = ETradeOperationType.TRADE_ACTION_MODIFY;
  // @ts-expect-error
  editTrade.type = EPlacedOrders[placedOrder.type];
  editTrade.type_time = placedOrder.type_time;
  editTrade.expiration = placedOrder.expiration;

  editOrderValues.value = editTrade;
}

function closeAllActiveTrades() {
  closeAllTradeModal.value = false;
///ea bata ako data
  eaSocket?.emit(`trade_data_${userStore.eaAccessToken}`, {
    action: ETradeOperationType.TRADE_ACTION_CUSTOM_CLOSE_ALL
  });
}

function openDeleteActiveTradeModal(activeTrade: IActiveOrders) {
  const { symbol, tp, ticket, volume, open_price } = activeTrade;

  deleteTrade.value = {
    symbol,
    tp,
    ticket,
    volume,
    open_price
  };

  bottomNavDrawerStore.toggleEditTradeModal(false);

  appbarStore.toggleModal("deleteTradeModal", true);
}

function closeActiveDeleteTradeModal() {
  removeSelectedTradeShapeFromChart();
}

function closePendingTrade(ticket: number) {
  eaSocket?.emit(`trade_data_${userStore.eaAccessToken}`, {
    action: ETradeOperationType.TRADE_ACTION_REMOVE,
    ticket
  });

  removeSelectedTradeShapeFromChart();
}

function removeSelectedTradeShapeFromChart() {
  selectedTrade.value = null;
  selectedTradeType.value = null;

  handleRemoveSelectedTradeShape();
}
</script>

<template>
  <Teleport to="body">
    <EditTradeModal
      v-bind="editOrderValues"
      v-if="bottomNavDrawerStore.editTradeModal"
    />

    <DeleteTradeModal
      v-bind="deleteTrade"
      v-if="appbarStore.modals.deleteTradeModal"
      @close-modal="closeActiveDeleteTradeModal"
    />

    <div
      class="absolute left-1/2 top-1/2 z-10 w-[350px] -translate-x-1/2 -translate-y-1/2 rounded-md border bg-white shadow-lg"
      v-if="closeAllTradeModal"
    >
      <div class="relative flex items-center justify-end border-b px-1 py-0.5">
        <div class="absolute left-1/2 top-2.5 -translate-x-1/2 font-bold">
          Close All Trades
        </div>

        <div>
          <NavItem @click="closeAllTradeModal = false">
            <FontAwesomeIcon size="xl" icon="fa-solid fa-xmark" />
          </NavItem>
        </div>
      </div>

      <div class="px-4 py-3 text-sm">
        Are you sure you want to close all trades?
      </div>

      <div class="flex gap-x-2 px-4 pb-2">
        <PrimaryButton
          class="w-full !bg-danger hover:!bg-red-700"
          @click="closeAllActiveTrades"
        >
          Close Trades
        </PrimaryButton>

        <PrimaryButton class="w-full" @click="closeAllTradeModal = false">
          Cancel
        </PrimaryButton>
      </div>
    </div>
  </Teleport>

  <div
    class="overflow-auto p-2"
    :style="{
      height: bottomNavDrawerStore.bottomNavContentAreaHeight - 49 + 'px'
    }"
  >
    <div
      class="pt-5 text-center text-sm"
      v-if="!candleStickStore.marketWatchOnlineStatus"
    >
      Connect EA to see open trades.
    </div>

    <div v-else>
      <table class="w-full text-left text-[11px]">
        <thead>
          <tr>
            <template v-for="header in tableHeaders" :key="header.id">
              <th class="sticky top-[-9px] bg-gray-50" v-show="header.toggle">
                <div
                  class="flex px-2 pb-1.5 pt-2 text-xs font-semibold"
                  @click="handleSorting(header.id)"
                >
                  <div
                    class="pr-1"
                    v-if="header.id === 'open_time'"
                    @click.stop="() => {}"
                  >
                    <Dropdown
                      class="!p-0"
                      id="open-trade-settings-dropdown"
                      toggle-id="open-trade-settings-toggle-dropdown"
                      :icon="false"
                      :offset-distance="-42"
                      :offset-skidding="-36"
                    >
                      <template #text>
                        <FontAwesomeIcon
                          class="rotate-90"
                          icon="fa-solid fa-ellipsis"
                        />
                      </template>

                      <template #content>
                        <div class="my-1">
                          <div class="px-3 pt-1.5 text-xs text-gray-600">
                            Toggle Headers
                          </div>

                          <div
                            class="flex items-center gap-x-2.5 px-3 pb-1 pt-1.5"
                            v-for="header in toggleTableHeaders"
                            :key="header.id"
                          >
                            <Checkbox
                              :id="header.id"
                              :value="header.id"
                              v-model="selectedToggleHeaders"
                              @change="handleTableHeaderToggle(header.id)"
                            />

                            <InputLabel class="!mb-0" :for="header.id">
                              {{ header.text }}
                            </InputLabel>
                          </div>
                        </div>
                      </template>
                    </Dropdown>
                  </div>

                  <div class="flex grow items-center justify-between gap-x-2">
                    <div class="flex items-center justify-between gap-x-1">
                      {{ header.text }}

                      <FontAwesomeIcon
                        icon="fa-solid fa-arrow-down"
                        :class="{
                          hidden: toggleSortIcon(header.id, 'asc')
                        }"
                      />

                      <FontAwesomeIcon
                        icon="fa-solid fa-arrow-up"
                        :class="{
                          hidden: toggleSortIcon(header.id, 'desc')
                        }"
                      />
                    </div>

                    <div
                      v-if="
                        openTradeList.length !== 0 && header.id === 'profit'
                      "
                    >
                      <Tooltip
                        id="close-all-active-trades-tooltip"
                        trigger-id="close-all-active-trades-trigger-tooltip"
                        class="-mr-[1px]"
                        @click.stop="closeAllTradeModal = true"
                      >
                        <template #trigger>
                          <FontAwesomeIcon icon="fa-solid fa-xmark" size="lg" />
                        </template>

                        <template #content>Close All Trades</template>
                      </Tooltip>
                    </div>
                  </div>
                </div>
              </th>
            </template>
          </tr>
        </thead>

        <tbody>
          <tr
            class="even:bg-gray-100"
            v-for="(item, idx) in openTradeList"
            :class="{
              '!bg-blue-100':
                selectedTrade &&
                tradeShapeStore.selectedTradeShape &&
                item.ticket === selectedTrade.ticket
            }"
            @click="handleClick($event, item as IActiveOrders, 'active_orders')"
            @dblclick="
              handleDoubleClick(item as IActiveOrders, 'active_orders')
            "
            :key="item.ticket"
          >
            <td class="px-2 pb-1.5 pt-2">
              {{ item.open_time }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.symbol }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.ticket }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ EActiveOrders[item.type as keyof typeof EActiveOrders] }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.volume ? item.volume : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.sl ? item.sl : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.tp ? item.tp : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              <div class="flex items-center justify-between">
                <template v-if="item.tp1">
                  {{ item.tp1 }}

                  <Tooltip
                    :id="`open-trade-tp1-tooltip-${idx}`"
                    :trigger-id="`open-trade-tp1-trigger-tooltip-${idx}`"
                  >
                    <template #trigger>
                      <div
                        class="h-1.5 w-1.5 rounded-full bg-success"
                        :class="{
                          '!bg-danger': item.tp1_status === 'executed'
                        }"
                      ></div>
                    </template>

                    <template #content>
                      Trade
                      <template v-if="item.tp1_status === 'not_executed'">
                        Not
                      </template>
                      Executed
                    </template>
                  </Tooltip>
                </template>
              </div>
            </td>

            <td class="px-2 pb-1.5 pt-2">
              <div class="flex items-center justify-between">
                <template v-if="item.vol1">
                  {{ item.vol1 }}

                  <Tooltip
                    :id="`open-trade-tp1-vol-tooltip-${idx}`"
                    :trigger-id="`open-trade-tp1-vol-trigger-tooltip-${idx}`"
                  >
                    <template #trigger>
                      <div
                        class="h-1.5 w-1.5 rounded-full bg-success"
                        :class="{
                          '!bg-danger': item.vol1_status === 'executed'
                        }"
                      ></div>
                    </template>

                    <template #content>
                      Trade
                      <template v-if="item.tp1_status === 'not_executed'">
                        Not
                      </template>
                      Executed
                    </template>
                  </Tooltip>
                </template>
              </div>
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.open_price ? item.open_price : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.current_price ? item.current_price : "" }}
            </td>

            <td
              class="px-2 pb-1.5 pt-2"
              v-show="selectedToggleHeaders.includes('comment')"
            >
              <template v-if="item.comment">
                <Tooltip
                  :id="`open-trade-comment-tooltip-${idx}`"
                  :trigger-id="`open-trade-comment-trigger-tooltip-${idx}`"
                >
                  <template #trigger>
                    <div class="w-16 truncate text-left">
                      {{ item.comment }}
                    </div>
                  </template>

                  <template #content>
                    {{ item.comment }}
                  </template>
                </Tooltip>
              </template>
            </td>

            <td
              class="px-2 pb-1.5 pt-2"
              v-show="selectedToggleHeaders.includes('sl_trail')"
            >
              {{ item.sl_trail ? item.sl_trail : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              <template v-if="item.margin">
                {{ item.margin }}

                ({{
                  (
                    (item.margin / candleStickStore.eaAccount?.equity!) *
                    100
                  ).toFixed(2)
                }}%)
              </template>
            </td>

            <td class="px-2 pb-1.5 pt-2">
              <template v-if="item.exp_profit">
                {{ item.exp_profit }}
                ({{
                  (
                    (item.exp_profit / candleStickStore.eaAccount?.balance!) *
                    100
                  ).toFixed(2)
                }}%)
              </template>
            </td>

            <td class="px-2 pb-1.5 pt-2">
              <template v-if="item.exp_profit">
                {{ item.exp_loss }}
                ({{
                  (
                    (item.exp_loss / candleStickStore.eaAccount?.balance!) *
                    100
                  ).toFixed(2)
                }}%)
              </template>
            </td>

            <td
              class="px-2 pb-1.5 pt-2"
              v-show="selectedToggleHeaders.includes('swap')"
            >
              {{ item.swap ? item.swap : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              <div
                class="flex items-center justify-between"
                :class="{
                  'text-success': item.profit > 0,
                  'text-danger': item.profit < 0
                }"
              >
                {{ item.profit }}

                ({{
                  (
                    (item.profit / candleStickStore.eaAccount?.balance!) *
                    100
                  ).toFixed(2)
                }}%)

                <Tooltip
                  :id="`close-active-trade-tooltip-${idx}`"
                  :trigger-id="`close-trade-active-trigger-tooltip-${idx}`"
                  @click.stop="openDeleteActiveTradeModal(item)"
                >
                  <template #trigger>
                    <FontAwesomeIcon
                      size="lg"
                      class="text-black"
                      icon="fa-solid fa-xmark"
                    />
                  </template>

                  <template #content>Close Trade</template>
                </Tooltip>
              </div>
            </td>
          </tr>

          <tr v-if="candleStickStore.eaAccount">
            <td :colspan="tableColspanLength" class="px-2 pb-2 pt-3">
              <div class="flex gap-x-2 font-bold">
                <div>Balance: {{ candleStickStore.eaAccount.balance }}</div>

                <div>{{ candleStickStore.eaAccount.currency }}</div>

                <div>Equity: {{ candleStickStore.eaAccount.equity }}</div>

                <div>Margin: {{ candleStickStore.eaAccount.margin }}</div>

                <div>
                  Margin Level:
                  {{ candleStickStore.eaAccount.margin_level.toFixed(2) }}%
                </div>

                <div>
                  Margin Free: {{ candleStickStore.eaAccount.margin_free }}
                </div>
              </div>
            </td>

            <td class="px-2 pb-2 pt-3 font-bold">
              <template v-if="totalMargin">
                {{ totalMargin }}
                ({{
                  (
                    (totalMargin / candleStickStore.eaAccount.equity) *
                    100
                  ).toFixed(2)
                }}%)
              </template>
            </td>

            <td class="px-2 pb-2 pt-3 font-bold">
              <template v-if="totalExpectedProfit">
                {{ totalExpectedProfit }}
                ({{
                  (
                    (totalExpectedProfit / candleStickStore.eaAccount.balance) *
                    100
                  ).toFixed(2)
                }}%)
              </template>
            </td>

            <td class="px-2 pb-2 pt-3 font-bold">
              <template v-if="totalExpectedLoss">
                {{ totalExpectedLoss }}
                ({{
                  (
                    (totalExpectedLoss / candleStickStore.eaAccount.balance) *
                    100
                  ).toFixed(2)
                }}%)
              </template>
            </td>

            <td
              class="px-2 pb-2 pt-3 font-bold"
              v-show="selectedToggleHeaders.includes('swap')"
            ></td>

            <td class="px-2 pb-2 pt-3 font-bold">
              {{ candleStickStore.eaAccount.profit }} ({{
                (
                  (candleStickStore.eaAccount.profit /
                    candleStickStore.eaAccount.balance) *
                  100
                ).toFixed(2)
              }}%)
            </td>
          </tr>

          <tr v-else>
            <td colspan="18" class="px-2 pb-1.5 pt-2 text-center text-xs">
              No actives trade found.
            </td>
          </tr>

          <tr
            class="odd:bg-gray-50"
            :class="{
              '!bg-amber-100':
                selectedTrade &&
                tradeShapeStore.selectedTradeShape &&
                item.ticket === selectedTrade.ticket
            }"
            v-for="(item, idx) in placeOrderList"
            :key="item.ticket"
            @click="handleClick($event, item, 'placed_orders')"
            @dblclick="handleDoubleClick(item, 'placed_orders')"
          >
            <td class="px-2 pb-1.5 pt-2">
              {{ item.open_time }}
            </td>

            <td class="px-2 pb-1.5 pt-2">{{ item.symbol }}</td>

            <td class="px-2 pb-1.5 pt-2">{{ item.ticket }}</td>

            <td class="px-2 pb-1.5 pt-2">
              {{ EPlacedOrders[item.type as keyof typeof EPlacedOrders] }}
            </td>

            <td class="px-2 pb-1.5 pt-2">{{ item.volume }}</td>

            <td class="px-2 pb-1.5 pt-2">{{ item.sl ? item.sl : "" }}</td>

            <td class="px-2 pb-1.5 pt-2">{{ item.tp ? item.tp : "" }}</td>

            <td class="px-2 pb-1.5 pt-2"></td>

            <td class="px-2 pb-1.5 pt-2"></td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.open_price ? item.open_price : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.current_price ? item.current_price : "" }}
            </td>

            <td
              class="px-2 pb-1.5 pt-2"
              v-show="selectedToggleHeaders.includes('comment')"
            >
              {{ item.comment }}
            </td>

            <td
              class="px-2 pb-1.5 pt-2"
              v-show="selectedToggleHeaders.includes('sl_trail')"
            ></td>

            <td class="px-2 pb-1.5 pt-2"></td>

            <td class="px-2 pb-1.5 pt-2"></td>

            <td class="px-2 pb-1.5 pt-2"></td>

            <td
              class="px-2 pb-1.5 pt-2"
              v-show="selectedToggleHeaders.includes('swap')"
            ></td>

            <td class="px-2 pb-1.5 pt-2">
              <div class="flex justify-end">
                <Tooltip
                  :id="`close-pending-trade-tooltip-${idx}`"
                  :trigger-id="`close-pending-trade-trigger-tooltip-${idx}`"
                  @click.stop="closePendingTrade(item.ticket)"
                >
                  <template #trigger>
                    <FontAwesomeIcon
                      size="lg"
                      class="text-black"
                      icon="fa-solid fa-xmark"
                    />
                  </template>

                  <template #content>Close Trade</template>
                </Tooltip>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
table th,
td {
  border: 1px solid #dfdfdf;
}
</style>
