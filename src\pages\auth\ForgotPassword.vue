<script setup lang="ts">
import { nextTick, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { object, string } from "yup";

import { axios } from "@/api";
import {
  getClientValidationErrors,
  getServerErrors
} from "@/helpers/getErrors";

import Alert from "@/components/Alert.vue";
import InputText from "@/components/InputText.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";
import { toast } from "vue3-toastify";

const router = useRouter();

const userCredentials = ref({
  email: ""
});
const doesEmailExists = ref(false);
const emailValidationError = ref<typeof userCredentials.value | null>(null);
const otpCodes = ref({
  code_1: "",
  code_2: "",
  code_3: "",
  code_4: "",
  code_5: "",
  code_6: ""
});
const OTPCode = ref("");
const isOTPVerified = ref(false);
const optValidationError = ref<typeof otpCodes.value | null>(null);
const userPassword = ref({
  password: ""
});
const passwordValidationError = ref<typeof userPassword.value | null>(null);
const serverErrors = ref<string[]>([]);
const btnLoading = ref(false);

const userValidationSchema = object({
  email: string().email("Invalid email address.").required("Email is required.")
});
const optValidationSchema = object({
  code_1: string().required("OPT is required."),
  code_2: string().required("OPT is required."),
  code_3: string().required("OPT is required."),
  code_4: string().required("OPT is required."),
  code_5: string().required("OPT is required."),
  code_6: string().required("OPT is required.")
});
const passwordValidationSchema = object({
  password: string()
    .min(8, "Password must be at least 8 characters long.")
    .required("Password is required.")
});

onMounted(() => {
  // doesEmailExists.value = true
  // isOTPVerified.value = true
  // initializeOPT()
});

async function sendEmail() {
  try {
    emailValidationError.value = null;
    serverErrors.value = [];
    btnLoading.value = true;

    const { email } = await userValidationSchema.validate(
      userCredentials.value,
      {
        abortEarly: false
      }
    );

    await axios.post("/user/forgot-password", {
      email
    });

    doesEmailExists.value = true;

    initializeOPT();
  } catch (e) {
    emailValidationError.value = getClientValidationErrors(e);
    serverErrors.value = getServerErrors(e);
  } finally {
    btnLoading.value = false;
  }
}

async function initializeOPT() {
  await nextTick();

  const focusNextInput = (
    el: Element,
    prevId: string | null,
    nextId: string | null
  ) => {
    // @ts-expect-error
    if (el.value.length === 0) {
      if (prevId) {
        document.getElementById(prevId)?.focus();
      }
    } else {
      if (nextId) {
        document.getElementById(nextId)?.focus();
      }
    }
  };

  document.querySelectorAll("[data-focus-input-init]").forEach((el) => {
    el.addEventListener("keyup", function () {
      const prevId = el.getAttribute("data-focus-input-prev");
      const nextId = el.getAttribute("data-focus-input-next");
      focusNextInput(el, prevId, nextId);
    });
  });
}

async function verifyOPTCode() {
  try {
    optValidationError.value = null;
    serverErrors.value = [];
    btnLoading.value = true;

    const { code_1, code_2, code_3, code_4, code_5, code_6 } =
      await optValidationSchema.validate(otpCodes.value, {
        abortEarly: false
      });

    const token = code_1 + code_2 + code_3 + code_4 + code_5 + code_6;

    OTPCode.value = token;

    console.log(token);

    const resp = await axios.post("/user/verify-reset-token", {
      token
    });
  } catch (e) {
    optValidationError.value = getClientValidationErrors(e);
    serverErrors.value = getServerErrors(e);
  } finally {
    btnLoading.value = false;
  }
}

async function changePassword() {
  try {
    passwordValidationError.value = null;
    serverErrors.value = [];
    btnLoading.value = true;

    const { password } = await passwordValidationSchema.validate(
      userPassword.value,
      {
        abortEarly: false
      }
    );

    const resp = await axios.post("/user/reset-password", {
      token: OTPCode.value,
      newPassword: password
    });

    await router.replace({ name: "login" });

    toast.info(resp.data.message, {
      position: "bottom-center"
    });
  } catch (e) {
    passwordValidationError.value = getClientValidationErrors(e);
    serverErrors.value = getServerErrors(e);
  } finally {
    btnLoading.value = false;
  }
}
</script>

<template>
  <div class="grid grow place-items-center bg-accent py-20">
    <div class="w-[400px] border bg-white py-4 shadow-lg">
      <template v-if="!doesEmailExists">
        <h1 class="border-b px-6 pb-2 text-2xl font-semibold">
          Forgot Password
        </h1>

        <Alert
          variant="danger"
          class="mx-6 mt-3"
          v-if="serverErrors.length !== 0"
        >
          {{ serverErrors[0] }}
        </Alert>

        <p class="mt-4 px-6 text-sm text-gray-500">
          Enter your email address and we'll send you a OTP code to reset your
          password.
        </p>

        <form @submit.prevent="sendEmail">
          <div class="mt-3 px-6">
            <InputText
              type="email"
              class="px-3 pb-3 pt-4"
              placeholder="Enter email address"
              :error="emailValidationError?.email"
              v-model="userCredentials.email"
            />
          </div>

          <div class="mt-5 px-6">
            <PrimaryButton
              type="submit"
              class="flex w-full justify-center pb-2 pt-2.5"
              :loading="btnLoading"
            >
              Send Email
            </PrimaryButton>
          </div>
        </form>
      </template>

      <template v-if="doesEmailExists && !isOTPVerified">
        <h1 class="border-b px-6 pb-2 text-2xl font-semibold">Email Sent</h1>

        <Alert
          variant="danger"
          class="mx-6 mt-3"
          v-if="serverErrors.length !== 0"
        >
          {{ serverErrors[0] }}
        </Alert>

        <p class="mt-4 px-6 text-sm text-gray-500">
          Enter the six-digit code we've sent via email.
        </p>

        <div class="mt-4 flex justify-center gap-x-2">
          <div>
            <label for="code-1" class="sr-only">First code</label>

            <input
              id="code-1"
              type="text"
              maxlength="1"
              data-focus-input-init
              data-focus-input-next="code-2"
              class="block h-9 w-9 rounded-lg border border-gray-300 bg-white py-3 text-center text-sm font-extrabold text-gray-900"
              :class="{
                'border-danger focus:!border-danger focus:!ring-danger':
                  optValidationError?.code_1
              }"
              v-model="otpCodes.code_1"
            />
          </div>

          <div>
            <label for="code-2" class="sr-only">Second code</label>

            <input
              id="code-2"
              type="text"
              maxlength="1"
              data-focus-input-init
              data-focus-input-prev="code-1"
              data-focus-input-next="code-3"
              class="block h-9 w-9 rounded-lg border border-gray-300 bg-white py-3 text-center text-sm font-extrabold text-gray-900"
              :class="{
                'border-danger': optValidationError?.code_2
              }"
              v-model="otpCodes.code_2"
            />
          </div>

          <div>
            <label for="code-3" class="sr-only">Third code</label>

            <input
              id="code-3"
              type="text"
              maxlength="1"
              data-focus-input-init
              data-focus-input-prev="code-2"
              data-focus-input-next="code-4"
              class="block h-9 w-9 rounded-lg border border-gray-300 bg-white py-3 text-center text-sm font-extrabold text-gray-900"
              :class="{
                'border-danger': optValidationError?.code_3
              }"
              v-model="otpCodes.code_3"
            />
          </div>

          <div>
            <label for="code-4" class="sr-only">Fourth code</label>

            <input
              id="code-4"
              type="text"
              maxlength="1"
              data-focus-input-init
              data-focus-input-prev="code-3"
              data-focus-input-next="code-5"
              class="block h-9 w-9 rounded-lg border border-gray-300 bg-white py-3 text-center text-sm font-extrabold text-gray-900"
              :class="{
                'border-danger': optValidationError?.code_4
              }"
              v-model="otpCodes.code_4"
            />
          </div>

          <div>
            <label for="code-5" class="sr-only">Fifth code</label>

            <input
              id="code-5"
              type="text"
              maxlength="1"
              data-focus-input-init
              data-focus-input-prev="code-4"
              data-focus-input-next="code-6"
              class="block h-9 w-9 rounded-lg border border-gray-300 bg-white py-3 text-center text-sm font-extrabold text-gray-900"
              :class="{
                'border-danger': optValidationError?.code_5
              }"
              v-model="otpCodes.code_5"
            />
          </div>

          <div>
            <label for="code-6" class="sr-only">Sixth code</label>

            <input
              id="code-6"
              type="text"
              maxlength="1"
              data-focus-input-init
              data-focus-input-prev="code-5"
              class="block h-9 w-9 rounded-lg border border-gray-300 bg-white py-3 text-center text-sm font-extrabold text-gray-900"
              :class="{
                'border-danger': optValidationError?.code_6
              }"
              v-model="otpCodes.code_6"
            />
          </div>
        </div>

        <Alert class="mx-6 mt-4">
          The code that you have received expires in 1 hour.
        </Alert>

        <div class="mt-6 px-6">
          <PrimaryButton
            class="flex w-full justify-center pb-2 pt-2.5"
            :loading="btnLoading"
            @click="verifyOPTCode"
          >
            Verify Code
          </PrimaryButton>
        </div>

        <div class="mt-6 text-center text-sm">
          Didn't get your OPT code?
          <button type="button" class="cursor-pointer font-semibold underline">
            Send again!
          </button>
        </div>
      </template>

      <template v-if="isOTPVerified">
        <h1 class="border-b px-6 pb-2 text-2xl font-semibold">
          Reset Password
        </h1>

        <Alert
          variant="danger"
          class="mx-6 mt-3"
          v-if="serverErrors.length !== 0"
        >
          {{ serverErrors[0] }}
        </Alert>

        <div class="mt-4 px-6">
          <InputText
            type="password"
            class="px-3 pb-3 pt-4"
            placeholder="New Password"
            :error="passwordValidationError?.password"
            v-model="userPassword.password"
          />

          <div class="mt-2 text-xs text-gray-500">
            Use mix of alphabets, number, and special characters to create new
            password.
          </div>
        </div>

        <div class="mt-5 px-6">
          <PrimaryButton
            class="flex w-full justify-center pb-2 pt-2.5"
            :loading="btnLoading"
            @click="changePassword"
          >
            Change Password
          </PrimaryButton>
        </div>
      </template>
    </div>
  </div>
</template>
