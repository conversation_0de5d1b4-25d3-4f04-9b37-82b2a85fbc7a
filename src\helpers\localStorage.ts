/**
 * Store value to local storage.
 */
export function storeToLocalStorage(key: string, value: any, isJson = false) {
  let item = value;

  if (isJson) {
    item = JSON.stringify(value);
  }

  localStorage.setItem(key, item);
}

/**
 * Returns value from local storage.
 */
export function getFromLocalStorage(key: string, isJson = false) {
  const item = localStorage.getItem(key);

  if (item && isJson) {
    return JSON.parse(item);
  }

  return item;
}
