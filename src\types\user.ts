export type UserRole = 'admin' | 'user' | 'moderator'; // Add other roles as needed
export type LoginType = 'frontend'
export type PreferenceName = 'analytics' | 'history' | 'opentrade'; d

export interface UserSession {
  ip_address: string;
  last_login_date: string;
  access_token: string;
  login_type: LoginType;
  _id: string;
}

export interface UserPreference {
  name: PreferenceName;
  enabled: boolean;
  _id: string;
}

export interface User {
  _id: string;
  name: string;
  email: string;
  user_name:string;
  phone_number: string | null;
  gender: string | null;
  roles: UserRole[];
  is_suspended: boolean;
  sessions: UserSession[];
  payment: any | null;
  profile_picture: string;
  preferences: UserPreference[];
}