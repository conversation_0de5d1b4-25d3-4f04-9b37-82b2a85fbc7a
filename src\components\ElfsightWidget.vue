<script setup lang="ts">
import { watch } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

watch(
  () => route.name,
  (routeName) => {
    if (routeName === "home") {
      initializeMessenger();
    } else {
      toggleChatWidget("hide");
    }
  }
);

function initializeMessenger() {
  const el = document.getElementById("elfsight-platform-script");

  if (el) {
    toggleChatWidget("show");
    return;
  }

  const script = document.createElement("script");

  script.id = "elfsight-platform-script";

  script.src = "https://static.elfsight.com/platform/platform.js";

  script.async = true;

  document.head.appendChild(script);
}

function toggleChatWidget(type: "show" | "hide") {
  const el = document.getElementById("__EAAPS_PORTAL");

  if (el) {
    const displayType = type === "show" ? "block" : "none";

    el.style.display = displayType;
  }
}
</script>

<template>
  <div
    class="elfsight-app-44fbe6d0-6c33-4b9e-97f9-7394aa24ddd8"
    data-elfsight-app-lazy
  ></div>
</template>
