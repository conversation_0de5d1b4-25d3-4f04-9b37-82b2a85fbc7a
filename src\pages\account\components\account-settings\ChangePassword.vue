<script setup lang="ts">
import { ref } from "vue";
import { toast } from "vue3-toastify";
import { object, string, ref as YupRef } from "yup";

import { axios } from "@/api";
import {
  getServerErrors,
  getClientValidationErrors
} from "@/helpers/getErrors";

import ProfileHeader from "./ProfileHeader.vue";

import <PERSON>ert from "@/components/Alert.vue";
import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const userPassword = ref({
  current_password: "",
  new_password: "",
  confirm_password: ""
});
const btnLoading = ref(false);
const validationErrors = ref<typeof userPassword.value | null>(null);
const serverErrors = ref<string[]>([]);

const validationSchema = object({
  current_password: string().required("Current password is required."),
  new_password: string()
    .notOneOf(
      [YupRef("current_password")],
      "New password cannot be the same as current password."
    )
    .min(8, "New password must be at least 8 characters long.")
    .required("New password is required."),
  confirm_password: string()
    .oneOf(
      [YupRef("new_password")],
      "New password and confirm password must match."
    )
    .required("Confirm password is required.")
});

async function changePassword() {
  try {
    validationErrors.value = null;
    serverErrors.value = [];

    btnLoading.value = true;

    const user = await validationSchema.validate(userPassword.value, {
      abortEarly: false
    });

    const resp = await axios.post("/user/change-password", {
      currentPassword: user.current_password,
      newPassword: user.new_password
    });

    toast.info(resp.data.message);
  } catch (e) {
    validationErrors.value = getClientValidationErrors(e);
    serverErrors.value = getServerErrors(e);
  } finally {
    btnLoading.value = false;
  }
}
</script>

<template>
  <ProfileHeader text="Change Password" route-name="account" />

  <form class="px-5">
    <Alert variant="info">
      Your password must be at least 8 characters long. <br />
      It should include a combination of numbers, letters and special
      characters.
    </Alert>

    <div class="mt-4">
      <InputLabel for="current_password">Current Password</InputLabel>

      <InputText
        type="password"
        id="current_password"
        name="current_password"
        :error="validationErrors?.current_password"
        v-model="userPassword.current_password"
      />
    </div>

    <div class="mt-4">
      <InputLabel for="new_password">New Password</InputLabel>

      <InputText
        type="password"
        id="new_password"
        name="new_password"
        :error="validationErrors?.new_password"
        v-model="userPassword.new_password"
      />
    </div>

    <div class="mt-4">
      <InputLabel for="confirm_password">Confirm New Password</InputLabel>

      <InputText
        type="password"
        id="confirm_password"
        name="confirm_password"
        :error="validationErrors?.confirm_password"
        v-model="userPassword.confirm_password"
      />
    </div>

    <div class="mt-5">
      <PrimaryButton
        class="flex w-full justify-center"
        :loading="btnLoading"
        @click="changePassword"
      >
        Save Changes
      </PrimaryButton>
    </div>
  </form>
</template>
