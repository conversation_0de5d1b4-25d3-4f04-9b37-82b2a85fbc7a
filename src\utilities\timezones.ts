import { DateTime } from "luxon";

import { Timezone } from "@/types";

const zones = [
  "UTC",
  "Pacific/Honolulu",
  "America/Los_Angeles",
  "America/Vancouver",
  "America/Phoenix",
  "America/Chicago",
  "America/Mexico_City",
  "America/New_York",
  "America/Toronto",
  "America/Santiago",
  "America/Sao_Paulo",
  "Europe/Dublin",
  "Europe/Lisbon",
  "Europe/London",
  "Europe/Berlin",
  "Europe/Madrid",
  "Europe/Oslo",
  "Europe/Paris",
  "Europe/Rome",
  "Europe/Zurich",
  "Europe/Athens",
  "Europe/Helsinki",
  "Africa/Johannesburg",
  "Europe/Istanbul",
  "Europe/Moscow",
  "Asia/Qatar",
  "Asia/Riyadh",
  "Asia/Dubai",
  "Asia/Karachi",
  "Asia/Dhaka",
  "Asia/Bangkok",
  "Asia/Jakarta",
  "Asia/Hong_Kong",
  "Asia/Manila",
  "Asia/Shanghai",
  "Asia/Singapore",
  "Asia/Seoul",
  "Asia/Tokyo",
  "Australia/Brisbane",
  "Australia/Sydney"
];

function generateTimezones(): Timezone[] {
  const arr: Timezone[] = [];

  zones.forEach((timezone) => {
    const time = DateTime.now().setZone(timezone);

    let utcOffsetHours = "";
    let utcOffsetMinutes = "";

    let sign = "";

    if (time.offset > 0) {
      sign = "+";
    }

    // For timezone that is in UTC (e.g. London)
    if (time.offset === 0) {
      // Do nothing
    } else if (time.offset % 60 === 0) {
      utcOffsetHours = (time.offset / 60).toString();
    } else {
      utcOffsetHours = Math.trunc(time.offset / 60).toString();

      const offsetMins = ((time.offset / 60) % 1) * 60;

      utcOffsetMinutes = `:${offsetMins}`;

      // For timezone like Kolkata that has UTC+5:30, extra zero must be appended.
      if (offsetMins.toString().length === 1) {
        utcOffsetMinutes += "0";
      }
    }

    let name = timezone.split("/")[1];

    if (name) {
      name = `(UTC${sign}${utcOffsetHours}${utcOffsetMinutes}) ${name.replace("_", " ")}`;
    } else {
      // For UTC
      name = timezone;
    }

    arr.push({
      id: timezone.toLowerCase(),
      name,
      offset: `(UTC${sign}${utcOffsetHours}${utcOffsetMinutes})`,
      timezone
    });
  });

  return arr;
}

export const timezones = generateTimezones();
