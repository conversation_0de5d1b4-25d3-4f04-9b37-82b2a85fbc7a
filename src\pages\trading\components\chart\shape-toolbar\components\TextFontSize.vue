<script setup lang="ts">
import { onMounted, ref } from "vue";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { useChartStore } from "@/store/chartStore";

import DropdownTooltip from "@/components/DropdownTooltip.vue";
import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"text"> | undefined =
  chartStore.selectedShape;

const fontSizeDropdown = ref(false);
const fontSizes = ref([10, 12, 14, 16, 18, 20]);
const selectedFontSize = ref(12);

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set text font size");
  }

  selectedFontSize.value = selectedShape.properties.textProperties.font_size;
});

function handleFontSize(fontSize: number) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to change text font size");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to change text font size");
  }

  selectedFontSize.value = fontSize;

  selectedShape.setProperty(
    "textProperties",
    "font_size",
    selectedFontSize.value
  );

  chartStore.chart.update();
}
</script>

<template>
  <DropdownTooltip
    dropdown-id="shape-toolbar-text-font-size-dropdown"
    dropdown-toggle-id="shape-toolbar-text-font-size-toggle-dropdown"
    tooltip-id="shape-toolbar-text-font-size-tooltip"
    tooltip-trigger-id="shape-toolbar-text-font-size-trigger-tooltip"
    class="flex h-[40px] items-center rounded px-2 text-sm hover:bg-accent"
    :class="{ 'bg-accent': fontSizeDropdown }"
    :dropdown-offset-distance="2"
    :dropdown-offset-skidding="1"
    @show="fontSizeDropdown = true"
    @hide="fontSizeDropdown = false"
  >
    <template #text> {{ selectedFontSize }}px </template>

    <template #dropdown-content="{ close }">
      <div class="my-1 w-16 cursor-default">
        <div
          class="flex items-center gap-x-1 px-3 pb-1.5 pt-2 hover:bg-accent"
          :class="{
            'bg-selected text-white hover:bg-selected':
              size === selectedFontSize
          }"
          v-for="size in fontSizes"
          @click="(close(), handleFontSize(size))"
          :key="size"
        >
          {{ size }}px
        </div>
      </div>
    </template>

    <template #tooltip-content>Font size</template>
  </DropdownTooltip>
</template>
