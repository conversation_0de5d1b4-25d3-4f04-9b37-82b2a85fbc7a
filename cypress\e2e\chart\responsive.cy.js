describe("Responsiveness Tests", () => {
  beforeEach(() => {
    cy.viewport(1920, 1080);

    cy.visit("/login");

    cy.get("#email").type("<EMAIL>");
    cy.get("#password").type("qwertyasdf");

    cy.get("#login-button").click();

    cy.url().should("include", "trading");
  });

  it("should change chart dimentions when window is resized", () => {
    cy.visit("/trading");

    let initialWidth, initialHeight;

    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(4000);

    cy.get("#chart-container").then(($div) => {
      initialWidth = $div[0].clientWidth;
      initialHeight = $div[0].clientHeight;

      cy.log(
        `Initial width: ${initialWidth}px, initial height: ${initialHeight}px`
      );
    });

    cy.viewport(1200, 720);

    // eslint-disable-next-line cypress/no-unnecessary-waiting
    cy.wait(500);

    cy.get("#chart-container").then(($div) => {
      const newWidth = $div[0].clientWidth;
      const newHeight = $div[0].clientHeight;

      cy.log(`After resize:`);
      cy.log(`Chart width: ${newWidth}px, chart height: ${newHeight}`);

      expect(newWidth).not.to.equal(initialWidth);
      expect(newHeight).not.to.equal(initialHeight);
    });
  });
});
