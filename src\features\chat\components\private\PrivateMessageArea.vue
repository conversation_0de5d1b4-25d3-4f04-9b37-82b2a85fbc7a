<script setup lang="ts">
import { PropType, computed, onMounted, onUnmounted, ref, watch } from "vue";
import { MoreVertical, Trash2 } from "lucide-vue-next";
import { chatAxios } from "@/api";
import { useUserStore } from "@/store/userStore";
import getInitials from "@/helpers/getInitials";
import { Message } from "@/types/chat";
const userStore = useUserStore();
const { messages } = defineProps({
  messages: {
    type: Array as PropType<Message[]>,
    required: true
  }
});

const emit = defineEmits(['message-deleted', 'delete-request']);

const chatContainer = ref<HTMLElement | null>(null);
const userHasScrolled = ref(false);
const activeMessageMenu = ref<string | null>(null);

// Function to toggle message menu
const toggleMessageMenu = (messageId: string) => {
  if (activeMessageMenu.value === messageId) {
    activeMessageMenu.value = null;
  } else {
    activeMessageMenu.value = messageId;
  }
};

// Function to close menu when clicking outside
const closeMenuOnOutsideClick = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (!target.closest('.message-menu') && !target.closest('.message-menu-button')) {
    activeMessageMenu.value = null;
  }
};

// Add event listener for outside clicks
onMounted(() => {
  document.addEventListener('click', closeMenuOnOutsideClick);
});

onUnmounted(() => {
  document.removeEventListener('click', closeMenuOnOutsideClick);
});

// Update the delete function to emit a request instead
const deleteMessage = async (messageId: string) => {
  console.log('PrivateMessageArea: Emitting delete-request for:', messageId);
  emit('delete-request', messageId);
};

// Check if the current user is the message sender
const isCurrentUserMessage = (senderId: string) => {
  return userStore.user && userStore.user._id === senderId;
};

const formatTime = (timestamp:any) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false
  });
};
const nl2br = (str: string) => {
  return str.replace(/\n/g, "<br>");
};
const formatDate = (date:any) => {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (isSameDay(date, today)) {
    return "Today";
  } else if (isSameDay(date, yesterday)) {
    return "Yesterday";
  } else {
    // Format like "Monday, May 5th"
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric"
    });
  }
};

const isSameDay = (date1:any, date2:any) => {
  return (
    date1.getFullYear() === date2.getFullYear() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getDate() === date2.getDate()
  );
};

const isDifferentDay = (date1:any, date2:any) => {
  return !isSameDay(date1, date2);
};

const messagesWithDaySeparators = computed(() => {
  if (!messages.length) return [];

  const result: any = [];
  let lastDate: Date | null = null;

  // Filter out deleted messages first
  const filteredMessages = messages.filter(message => !message.deleted);

  const sortedMessages = [...filteredMessages].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );

  sortedMessages.forEach((message, index) => {
    const messageDate = new Date(message.timestamp);
    const currentDate = new Date(
      messageDate.getFullYear(),
      messageDate.getMonth(),
      messageDate.getDate()
    );

    // First message or new day
    if (index === 0 || !lastDate || isDifferentDay(lastDate, currentDate)) {
      result.push({
        separator: true,
        date: formatDate(currentDate)
      });
    }

    result.push(message);
    lastDate = currentDate;
  });

  return result;
});

// Only auto-scroll when new messages arrive or on initial load
watch(
  () => messages.length,
  (newLength, oldLength) => {
    if (newLength > oldLength && chatContainer.value) {
      // Only auto-scroll if user was already at the bottom
      const container = chatContainer.value;
      const isNearBottom =
        container.scrollHeight - container.clientHeight <=
        container.scrollTop + 50;

      if (isNearBottom || !userHasScrolled.value) {
        setTimeout(() => {
          if (chatContainer.value) {
            chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
          }
        }, 0);
      }
    }
  }
);

// Track when user manually scrolls
const handleScroll = () => {
  if (!chatContainer.value) return;

  const container = chatContainer.value;
  const isAtBottom =
    container.scrollHeight - container.clientHeight <= container.scrollTop + 50;

  // If user scrolls away from bottom, mark as manually scrolled
  if (!isAtBottom) {
    userHasScrolled.value = true;
  }

  // If user scrolls back to bottom, reset the flag
  if (isAtBottom) {
    userHasScrolled.value = false;
  }
};

// Scroll to bottom on initial mount
onMounted(() => {
  if (chatContainer.value) {
    chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
    chatContainer.value.addEventListener("scroll", handleScroll);
  }
});

// Clean up event listener
onUnmounted(() => {
  if (chatContainer.value) {
    chatContainer.value.removeEventListener("scroll", handleScroll);
  }
});
const handleTagClick = (event: Event) => {
  const tag = (event.target as HTMLSpanElement).textContent;
  if (tag) {
    emit('update:search', tag);
  }
};  
const highlightTags = (content: string) => {
  // Regular expression to match hashtags
  const tagRegex = /#(\w+)/g;
  
  // Replace hashtags with highlighted spans
  return nl2br(content.replace(tagRegex, '<span class="text-blue-500 font-medium cursor-pointer">#$1</span>'));
};

// Add a function to handle mention clicks
const handleMentionClick = (event: Event) => {
  const mention = (event.target as HTMLSpanElement).textContent;
  if (mention && mention.startsWith('@')) {
    const username = mention.substring(1); // Remove the @ symbol
    emit('update:search', '@' + username);
  }
};

// Combined click handler for both tags and mentions
const handleContentClick = (e: Event) => {
  const target = e.target as HTMLElement;
  if (target.classList.contains('text-blue-500')) {
    const content = target.textContent;
    if (content?.startsWith('#')) {
      handleTagClick(e);
    } else if (content?.startsWith('@')) {
      handleMentionClick(e);
    }
  }
};

// Highlight mentions in blue
const highlightMentions = (content: string) => {
  // Regular expression to match @username mentions
  const mentionRegex = /@(\w+)/g;
  
  // Replace mentions with highlighted spans
  return content.replace(mentionRegex, '<span class="text-blue-500 font-medium cursor-pointer">@$1</span>');
};

// Combined function to highlight both tags and mentions
const highlightContent = (content: string) => {
  // First highlight hashtags
  let highlighted = highlightTags(content);
  
  // Then highlight mentions
  highlighted = highlightMentions(highlighted);
  
  return highlighted;
};
</script>

<template>
  <div
    ref="chatContainer"
    class="h-64 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-sm"
    @click="handleContentClick"
  >
    <div
      v-for="(message, index) in messagesWithDaySeparators"
      :key="message._id || index"
    >
      <div v-if="!message.deleted">
        <!-- Day separator -->
        <div
          v-if="message.separator"
          class="flex items-center justify-center py-2"
        >
          <div
            class="bg-gray-100 rounded-full px-3 py-1 text-xs text-gray-600 font-medium"
          >
            {{ message.date }}
          </div>
        </div>

        <!-- Message -->
        <div
          v-else
          :id="message._id ? 'msg-' + message._id : undefined"
          class="py-3 px-4 flex items-start gap-3 border-b border-gray-100 last:border-0 hover:bg-gray-50 transition-colors relative group"
        >
          <div class="flex-shrink-0">
            <div
              v-if="message.sender.profile_picture"
              class="w-8 h-8 rounded-full overflow-hidden"
            >
              <img
                :src="message.sender.profile_picture"
                alt="User avatar"
                class="w-full h-full object-cover"
              />
            </div>
            <div
              v-else
              class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium text-sm"
            >
              {{ getInitials(message.sender?.user_name ||'No Username') }}
            </div>
          </div>

          <div class="flex-1 min-w-0">
            <div class="flex items-center justify-between mb-1">
              <div class="flex items-center gap-2">
                <span class="font-medium text-gray-900">
                  {{ message.sender?.user_name || 'Unknown User' }}
                </span>
                <span class="text-xs text-gray-500">
                  {{ formatTime(message.timestamp) }}
                </span>
              </div>
              
              <!-- Message menu button - only show for user's own messages -->
              <button 
                v-if="isCurrentUserMessage(message.sender?._id || message.sender)"
                class="message-menu-button p-1 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 opacity-0 group-hover:opacity-100 transition-opacity"
                @click.stop="toggleMessageMenu(message._id)"
              >
                <MoreVertical class="w-4 h-4" />
              </button>
              
              <!-- Message menu dropdown -->
              <div 
                v-if="activeMessageMenu === message._id"
                class="message-menu absolute right-4 -top-10 bg-white shadow-lg rounded-md border border-gray-200 z-10"
              >
                <button 
                  class="flex items-center gap-2 w-full px-4 py-2 text-left text-red-600 hover:bg-gray-50 cursor-pointer"
                  @click="()=>{
                    console.log('Message deleted:', message._id);
                    deleteMessage(message._id);
                  }"
                >
                  <Trash2 class="w-4 h-4" />
                  <span>Delete</span>
                </button>
              </div>
            </div>

            <div 
              class="text-gray-700 break-words"
              v-html="highlightContent(message.content)"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Add any additional styles here */
.message-menu {
  min-width: 120px;
}
</style>
