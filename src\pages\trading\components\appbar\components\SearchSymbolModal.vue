<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { onClickOutside, onKeyUp } from "@vueuse/core";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { useChartStore } from "@/store/chartStore";
import { useAppbarStore } from "@/store/appbarStore";
import { useCandleStickStore } from "@/store/candleStickStore";

import Search from "@/components/Search.vue";
import NavItem from "@/components/NavItem.vue";

interface FilteredSymbol {
  symbol: string;
  display_name: string;
  broker: string;
}

const chartStore = useChartStore();
const appbarStore = useAppbarStore();
const candlestickStore = useCandleStickStore();

const search = ref("");
const searchModal = ref<HTMLElement | null>(null);

onKeyUp("Escape", () => closeModal());

onClickOutside(searchModal, () => closeModal());

onMounted(() => {
  document.getElementById("search-symbol")?.focus();
});

const filteredSymbolList = computed(() => {
  const results: FilteredSymbol[] = [];

  candlestickStore.symbolList.forEach((w) => {
    w.instruments.forEach((x) => {
      if (x.toUpperCase().includes(search.value.toUpperCase())) {
        results.push({
          symbol: x,
          display_name: w.display_name,
          broker: w.broker
        });
      }
    });
  });

  return results;
});

function closeModal() {
  appbarStore.toggleModal("searchSymbolModal", false);
}

function handleSymbolChange(symbol: string) {
  closeModal();

  chartStore.changeCurrentSymbol(symbol);
}
</script>

<template>
  <div
    class="fixed left-0 top-0 z-10 h-full w-full bg-backdrop opacity-30"
  ></div>

  <div
    ref="searchModal"
    class="absolute left-1/2 top-1/2 z-20 w-[400px] -translate-x-1/2 -translate-y-1/2 cursor-default rounded-md bg-white text-sm shadow-lg"
  >
    <div class="relative flex justify-end p-0.5">
      <div class="absolute left-1/2 top-2 -translate-x-1/2 text-base font-bold">
        SEARCH SYMBOL
      </div>

      <NavItem @click="closeModal">
        <FontAwesomeIcon size="xl" icon="fa-solid fa-xmark" />
      </NavItem>
    </div>

    <div class="border-t py-2">
      <Search id="search-symbol" placeholder="Search..." v-model="search" />

      <div class="mt-3 px-3 text-xs text-gray-600">SYMBOL AND DESCRIPTION</div>
    </div>

    <div class="scrollbar h-[282px] overflow-y-auto pb-1">
      <div
        class="flex justify-between px-3 pb-1.5 pt-2 hover:bg-accent"
        v-for="item in filteredSymbolList"
        :key="item.symbol"
        @click="handleSymbolChange(item.symbol)"
      >
        <div>
          <div>{{ item.symbol }}</div>

          <div class="text-gray-600">
            {{ item.symbol }}
          </div>
        </div>

        <div class="text-right text-gray-600">
          <div>{{ item.display_name }}</div>

          <div class="text-xs">
            {{ item.broker }}
          </div>
        </div>
      </div>

      <div
        class="mt-5 text-center text-gray-600"
        v-if="filteredSymbolList.length === 0"
      >
        No results found.
      </div>
    </div>
  </div>
</template>
