<script setup lang="ts">
import { onMounted, ref } from "vue";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";
import { useChartStore } from "@/store/chartStore";

import ColorPicker from "@/components/ColorPicker.vue";
import DropdownTooltip from "@/components/DropdownTooltip.vue";

import PencilSVG from "@/assets/svg/pencil.svg";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"trend-line"> | undefined =
  chartStore.selectedShape;

const lineColorDropdown = ref(false);
const selectedLineColor = ref("");

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set shape color");
  }

  selectedLineColor.value = selectedShape.properties.lineProperties.line_color;
});

function handleLineColor(lineColor: string) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to change shape color");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to change shape color");
  }

  selectedLineColor.value = lineColor;

  selectedShape.setProperty(
    "lineProperties",
    "line_color",
    selectedLineColor.value
  );

  chartStore.chart.update();
}
</script>

<template>
  <DropdownTooltip
    dropdown-id="shape-toolbar-line-color-dropdown"
    dropdown-toggle-id="shape-toolbar-line-color-toggle-dropdown"
    tooltip-id="shape-toolbar-line-color-tooltip"
    tooltip-trigger-id="shape-toolbar-line-color-trigger-tooltip"
    class="flex h-[40px] items-center rounded px-2 text-sm hover:bg-accent"
    :class="{ 'bg-accent': lineColorDropdown }"
    :dropdown-offset-distance="2"
    :dropdown-offset-skidding="110"
    @show="lineColorDropdown = true"
    @hide="lineColorDropdown = false"
    v-if="selectedLineColor"
  >
    <template #text>
      <div>
        <PencilSVG />

        <div
          class="mt-0.5 h-1 w-5"
          :style="{ backgroundColor: selectedLineColor }"
        ></div>
      </div>
    </template>

    <template #dropdown-content>
      <ColorPicker :color="selectedLineColor" @update-color="handleLineColor" />
    </template>

    <template #tooltip-content>Color</template>
  </DropdownTooltip>
</template>
