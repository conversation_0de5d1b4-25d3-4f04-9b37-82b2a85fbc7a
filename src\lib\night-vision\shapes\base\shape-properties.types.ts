import { LabelPosition } from "../utils/PreferencesUtils";

export interface IChartSidebarDisplay {
  show_sidebar_default: boolean;
  show_sidebar_hover: boolean;
  show_sidebar_selected: boolean;
}

export interface IChartBotbarDisplay {
  show_botbar_default: boolean;
  show_botbar_hover: boolean;
  show_botbar_selected: boolean;
}

export interface ILinePointDisplay {
  show_point_default: boolean;
  show_point_hover: boolean;
  show_point_selected: boolean;
}

export interface Background {
  fill_color: string;
  fill_opacity: number;
}

export interface ILinePoint {
  point_shape: "circle" | "square" | "arrow";
  point_position: "coords" | "midpoint";
  point_width_default: number;
  point_width_hover: number;
  point_width_selected: number;
}

export interface ILine {
  line_color: string;
  line_width: number;
  line_type: "dashed" | "solid" | "dotted";
  extend_dir_1: boolean;
  extend_dir_2: boolean;
  show_label: boolean;
  show_price_range: boolean;
  show_percent_change: boolean;
  show_change_in_pips: boolean;
  show_diff: boolean;
  show_bars_range: boolean;
  show_date_time_range: boolean;
  show_distance: boolean;
  show_angle: boolean;
}

export interface LabelProperties {
  show_label: boolean;
  label_position: LabelPosition;
  show_price_range: boolean;
  show_percent_change: boolean;
  show_change_in_pips: boolean;
  show_diff: boolean;
  show_bars_range: boolean;
  show_date_time_range: boolean;
  show_distance: boolean;
  show_angle: boolean;
  labelStyle: LabelStyle;
}

export type PriceRangeLabelProperties = Omit<
  LabelProperties,
  | "show_bars_range"
  | "show_angle"
  | "show_date_time_range"
  | "show_distance"
  | "label_position"
> & {
  label_position: "vertical-arrow";
};

export type DateRangeLabelProperties = Omit<
  LabelProperties,
  | "show_angle"
  | "show_change_in_pips"
  | "show_diff"
  | "show_percent_change"
  | "show_price_range"
  | "label_position"
> & {
  label_position: "horizontal-arrow";
};

export interface LabelStyle {
  fontSize: number;
  fontFace: string;
  fontColor: string;
  bgColor: string;
  lineSpacing: number;
}

export interface IText {
  font_color: string;
  font_size: number;
  font: string;
  bg_color: string;
}

export interface IFibLevel {
  level_position: number;
  level_line_color: string;
  level_show: boolean;
}

export interface IFib {
  level_line_type: "dashed" | "solid" | "dotted";
  level_line_color: string | null;
  level_line_width: number;
  level_text_position: "left" | "right";
  extend_dir_1: boolean;
  extend_dir_2: boolean;
  reverse_order: boolean;
}
