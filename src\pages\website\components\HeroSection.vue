<script setup lang="ts">
import { ref } from "vue";

import { ArrowRight as ArrowRightIcon, PlayCircleIcon } from "lucide-vue-next";

import { useUserStore } from "@/store/userStore";

import VideoModal from "./VideoModal.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const userStore = useUserStore();

const toggleVideoModal = ref(false);
</script>

<template>
  <Teleport to="body">
    <VideoModal
      link="https://www.youtube.com/embed/K4qZ2joYBIE"
      @close="toggleVideoModal = false"
      v-if="toggleVideoModal"
    />
  </Teleport>

  <div class="relative bg-gradient-to-br from-blue-50 to-blue-100">
    <div
      class="bg-grid-slate-100 absolute inset-0 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]"
    />

    <div class="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="flex flex-col items-center justify-center text-center">
        <div class="space-y-8 py-20">
          <h1
            class="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl md:text-7xl"
          >
            <span>FX Trading School</span>
          </h1>
          <p class="mx-auto max-w-6xl text-lg text-gray-600 sm:text-xl">
            FX Trading school with a revolutionary hands-on learning approach.
            We offer online courses to teach you the fundamentals, along with
            proprietary trading software for practice. We track your results
            throughout the journey and provide feedback to help you become
            profitable.
          </p>

          <div
            class="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0"
          >
            <router-link
              :to="{ name: 'trading' }"
              class="flex items-center gap-x-2 rounded-lg bg-primary px-5 pb-2.5 pt-3 text-white hover:bg-secondary"
              v-if="userStore.user"
            >
              Trading Console
              <FontAwesomeIcon icon="fa-solid fa-arrow-right" />
            </router-link>
            <router-link :to="{ name: 'login' }" v-else>
              <button
                class="group flex cursor-pointer items-center space-x-2 rounded-lg bg-blue-700 px-6 py-3 text-white transition-all hover:bg-blue-800"
              >
                <span>Log In</span>
                <ArrowRightIcon
                  class="h-4 w-4 transition-transform group-hover:translate-x-1"
                />
              </button>
            </router-link>
            <button
              class="group flex cursor-pointer items-center space-x-2 rounded-lg border border-gray-300 bg-white px-6 py-3 text-gray-700 transition-all hover:bg-gray-50"
              @click="toggleVideoModal = true"
            >
              <PlayCircleIcon class="h-5 w-5 text-blue-700" />
              <span>Watch Demo</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
