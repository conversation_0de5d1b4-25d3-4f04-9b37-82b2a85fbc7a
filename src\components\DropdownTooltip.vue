<script setup lang="ts">
import { onMounted } from "vue";
import { Dropdown, Tooltip } from "flowbite";
import type { DropdownOptions } from "flowbite";
import type { Placement } from "@popperjs/core";

defineOptions({
  inheritAttrs: false
});

const props = withDefaults(
  defineProps<{
    dropdownId: string;
    dropdownToggleId: string;
    dropdownPlacement?: Placement;
    dropdownOffsetDistance?: number;
    dropdownOffsetSkidding?: number;
    tooltipId: string;
    tooltipTriggerId: string;
  }>(),
  {
    dropdownPlacement: "bottom",
    dropdownOffsetDistance: 0,
    dropdownOffsetSkidding: 0
  }
);

const emit = defineEmits(["show", "hide"]);

let dropdown: Dropdown;

onMounted(() => {
  const targetEl = document.getElementById(props.dropdownToggleId);
  const triggerEl = document.getElementById(props.dropdownId);

  if (triggerEl === null || triggerEl === null) {
    return;
  }

  const options: DropdownOptions = {
    placement: props.dropdownPlacement,
    offsetDistance: props.dropdownOffsetDistance,
    offsetSkidding: props.dropdownOffsetSkidding,
    onShow: () => {
      emit("show");
    },
    onHide: () => {
      emit("hide");
    }
  };

  dropdown = new Dropdown(targetEl, triggerEl, options);

  const toolTipTargetEl = document.getElementById(props.tooltipId);
  const tooltipTriggerEl = document.getElementById(props.tooltipTriggerId);

  new Tooltip(toolTipTargetEl, tooltipTriggerEl);
});

function closeDropdown() {
  dropdown.hide();
}
</script>

<template>
  <button type="button" :id="dropdownId">
    <div :id="tooltipTriggerId" v-bind="$attrs">
      <slot name="text" />
    </div>
  </button>

  <Teleport to="#modal-section">
    <div
      :id="dropdownToggleId"
      class="z-10 hidden overflow-hidden rounded-md border border-gray-200 bg-white text-sm shadow-lg"
    >
      <slot name="dropdown-content" :close="closeDropdown"></slot>
    </div>

    <div
      :id="tooltipId"
      role="tooltip"
      class="tooltip invisible absolute z-50 inline-block rounded bg-black px-2 pb-1 pt-1.5 text-xs font-medium text-white opacity-0 transition-opacity duration-300"
    >
      <slot name="tooltip-content" />

      <div class="tooltip-arrow" data-popper-arrow></div>
    </div>
  </Teleport>
</template>
