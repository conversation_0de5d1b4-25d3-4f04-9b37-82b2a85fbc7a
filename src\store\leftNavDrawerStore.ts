import { ref } from "vue";
import { defineStore } from "pinia";
import { AvailableShapes } from "@/lib/night-vision/groups/ShapeTool";

export const useLeftNavDrawerStore = defineStore("left-nav-drawer", () => {
  const placeNewOrderModal = ref(false);
  const isPlaceNewOrderModalMinimized = ref(false);
  const placeNewOrderModalPosition = ref({
    top: 45,
    left: 52
  });
  const selectedShapeId = ref<AvailableShapes>("no-shape");

  function togglePlaceNewOrderModal(toggle: boolean, isMinimized = false) {
    placeNewOrderModal.value = toggle;

    isPlaceNewOrderModalMinimized.value = isMinimized;
  }

  function setPlaceNewOrderModalPosition(top: number, left: number) {
    placeNewOrderModalPosition.value = {
      top,
      left
    };
  }

  return {
    placeNewOrderModal,
    isPlaceNewOrderModalMinimized,
    placeNewOrderModalPosition,
    selectedShapeId,
    togglePlaceNewOrderModal,
    setPlaceNewOrderModalPosition
  };
});
