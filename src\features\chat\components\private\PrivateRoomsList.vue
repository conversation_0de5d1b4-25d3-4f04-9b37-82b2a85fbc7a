<script setup lang="ts">
import { PropType, ref } from "vue";

import { ChevronDown, ChevronUp, MessageCircle } from "lucide-vue-next";

import { convertToRelative } from "@/lib/utils/time";

import {PrivateRoom } from "@/types/chat";
import { useUserStore } from "@/store/userStore";
const userStore = useUserStore();
const { rooms } = defineProps({
  fetchingPrivateRooms: {
    type: Boolean,
    required: true
  },
  rooms: {
    type: Array as PropType<PrivateRoom[]>,
    required: true
  },
  currentRoom: {
    type: Object as PropType<PrivateRoom>,
    required: true
  }
});

const expanded = ref<boolean>(true);

const emit = defineEmits(["select-room", "collapse-change"]);

const toggleExpand = () => {
  expanded.value = !expanded.value;
  emit("collapse-change", !expanded.value);
};
</script>

<template>
  <div class="bg-white shadow-sm border-r border-gray-200 flex flex-col h-full">
    <!-- Header -->
    <div
      class="flex items-center justify-between px-4 py-3 border-b border-gray-100 bg-gray-50"
    >
      <h3 class="font-semibold text-gray-800 flex items-center gap-2">
        <MessageCircle class="w-4 h-4" />
        Private Chats
      </h3>
      <span>{{ rooms.length }} Chats</span>
      <!-- <button
        class="p-1.5 rounded-lg hover:bg-gray-200 transition-colors duration-200 text-gray-600 hover:text-gray-800"
        @click="toggleExpand"
      >
        <ChevronDown v-if="expanded" class="w-4 h-4" />
        <ChevronUp v-else class="w-4 h-4" />
      </button> -->
    </div>

    <!-- Rooms List -->
    <div v-if="expanded" class="flex-1 overflow-y-auto">
      <!-- Skeleton loader while fetching -->
      <div v-if="fetchingPrivateRooms" class="flex-1 overflow-y-auto">
        <div 
          v-for="i in 5" 
          :key="i" 
          class="px-4 py-3 border-b border-gray-50 animate-pulse"
        >
          <!-- Room Name Skeleton -->
          <div class="flex items-center justify-between mb-2">
            <div class="h-5 bg-gray-200 rounded w-3/4"></div>
          </div>
          
          <!-- Room Metadata Skeleton -->
          <div class="flex items-center gap-2">
            <div class="h-3 bg-gray-200 rounded w-1/4"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
      
      <!-- Actual rooms list -->
      <div
        v-else
        v-for="room in rooms"
        :key="room._id"
        class="group px-4 py-3 border-b border-gray-50 cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-100"
        :class="{
          'bg-blue-100 border-blue-200 shadow-sm':
            currentRoom.otherUser.user_name === room.otherUser.user_name,
          'hover:shadow-sm': currentRoom.otherUser.user_name !== room.otherUser.user_name
        }"
        @click="$emit('select-room', room)"
      >
        <!-- Room Name -->
        <div class="flex items-center justify-between mb-2">
          <span
            class="font-medium text-gray-900 transition-colors"
            :class="{ 'text-blue-700': currentRoom.otherUser.user_name === room.otherUser.user_name }"
          >
            {{ room.otherUser.user_name }}
          </span>
        </div>
        <span v-if="room && room.lastMessage !=null" class=" truncate text-sm text-gray-500">{{ room.lastMessage.sender.user_name==userStore?.user?.user_name? "You": room.lastMessage.sender.user_name  }} : {{ room.lastMessage.content.slice(0, 30)+ (room.lastMessage.content.length > 31 ? "...":"")+ " " }}</span>
                <span v-if="room && room.lastMessage !=null" class="text-gray-500">•</span>
            <span v-if="room && room.lastMessage !=null" class="whitespace-nowrap">{{
              convertToRelative(room.lastMessage.timestamp) === "0m"
                ? "Just Now"
                : convertToRelative(room.lastMessage.timestamp)
            }}</span>


        <!-- Room Metadata -->
        <div class="flex items-center gap-2 text-xs text-gray-500">
          <!-- Room metadata content -->
        </div>
      </div>
      
      <!-- Empty state when no rooms are available -->
      <div 
        v-if="!fetchingPrivateRooms && rooms.length === 0" 
        class="flex items-center justify-center h-full p-4 text-center"
      >
      
      </div>
    </div>
    
    <!-- Collapsed state -->
    <div v-else class="px-4 py-1 text-center text-xs text-gray-500 truncate">
      {{ rooms.length }} {{ rooms.length === 1 ? 'chat' : 'chats' }}
    </div>
  </div>
</template>
