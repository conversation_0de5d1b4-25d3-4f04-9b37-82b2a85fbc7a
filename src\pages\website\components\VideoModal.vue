<script setup lang="ts">
import { onMounted, useTemplateRef } from "vue";
import { onClickOutside } from "@vueuse/core";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const iframeRef = useTemplateRef("iframe-modal");

defineProps<{
  link: string;
}>();

const emit = defineEmits(["close"]);

onMounted(() => {
  document.body.style.overflow = "hidden";
});

onClickOutside(iframeRef, () => {
  document.body.style.overflow = "auto";

  emit("close");
});
</script>

<template>
  <div
    class="fixed left-0 top-0 z-10 min-h-screen w-full bg-backdrop opacity-50"
  ></div>

  <div class="fixed right-4 top-4 z-20 text-white">
    <FontAwesomeIcon
      size="2xl"
      icon="fa-solid fa-xmark"
      class="cursor-pointer"
    />
  </div>

  <div
    ref="iframe-modal"
    class="fixed top-1/2 z-20 w-full -translate-y-1/2 px-5 lg:left-1/2 lg:w-3/4 lg:-translate-x-1/2 lg:px-0"
  >
    <iframe class="mx-auto w-full lg:w-[900px]" height="500" :src="link">
    </iframe>
  </div>
</template>
