export interface Message {
  _id?: string;
  content: string;
  createdAt?: string;
  deleted?: boolean;
  deleted_by_moderator?: boolean;
  downvotes?: number;
  groupId?: string;
  message_tag?: string | null;
  receiver?: string | null;
  replyCount?: number;
  sender: {
    _id: string;
    user_name: string;
  };
  media:[string];
  timestamp: string;
  updatedAt?: string;
  upvotes?: number;
  parentId?: Message;
}

export interface Room {
  _id: string;
  otherUser?:{
    _id?:string;
    user_name?:string;
  }
  createdAt: string;
  createdBy: string;
  description: string;
  isDirect: boolean;
  lastMessage?: Message;
  name: string;
  participants: string[];
}
export interface PrivateRoom {
  _id: string;
  otherUser:{
    _id:string,
    user_name:string,
    profile_picture:string,
  }
  lastMessage?:{
    _id:string,
    content:string,
    sender:{
      _id:string,
      user_name:string
    },
    receiver:string,
    groupId:string,
    parentId?:string,
    upvotes:number,
    downvotes:number,
    usertags:[string],
    message_tags:[string],
    timestamp:string,
    deleted:boolean,
    deleted_by_moderator:boolean,
    updatedAt:string,
    createdAt:string
  }
}

export type NewMessage = Omit<Message, "createdAt">;

export interface Messages {
  [groupId: string]: Message[];
}
