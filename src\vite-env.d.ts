/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_ENV: string;
  readonly VITE_APP_TITLE: string;
  readonly VITE_APP_BASE_URL: string;
  readonly VITE_APP_SOCKET_OHLC_URL: string;
  readonly VITE_APP_SOCKET_TRADE_MARKET_URL: string;
  readonly VITE_APP_SOCKET_URL_EVENTS_AND_NOTIFICATION: string;
  readonly VITE_APP_STRIPE_PUBLISHABLE_KEY: string;
  readonly VITE_APP_GOOGLE_ANALYTICS_TAG: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
