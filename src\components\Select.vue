<script setup lang="ts">
defineOptions({
  inheritAttrs: false
});

withDefaults(
  defineProps<{
    defaultOption?: boolean;
    error?: string;
  }>(),
  {
    defaultOption: true
  }
);

const model = defineModel<string>();
</script>

<template>
  <select
    class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
    :class="{
      '!border-danger focus:border-red-500 focus:ring-red-500': error
    }"
    v-bind="$attrs"
    v-model="model"
  >
    <option value="" v-if="defaultOption">Select Option</option>

    <slot></slot>
  </select>

  <div class="mt-1 px-1 text-xs text-danger" v-if="error">
    {{ error }}
  </div>
</template>
