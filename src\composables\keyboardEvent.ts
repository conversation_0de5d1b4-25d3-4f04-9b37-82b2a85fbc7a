import { ref, onMounted, onUnmounted } from "vue";

export function useKeyboardEvent(self = true) {
  const key = ref("");

  function update(event: KeyboardEvent) {
    key.value = event.key;
  }

  onMounted(() => {
    if (self) {
      addKeyListener();
    }
  });

  onUnmounted(() => removeKeyListener());

  function addKeyListener() {
    window.addEventListener("keydown", update);
  }

  function removeKeyListener() {
    window.removeEventListener("keydown", update);
  }

  return { key, addKeyListener, removeKeyListener };
}
