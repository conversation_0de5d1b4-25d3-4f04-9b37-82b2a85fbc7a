<script lang="ts" setup>
import { ref } from "vue";

import { ChevronDown } from "lucide-vue-next";

interface FAQ {
  question: string;
  answer: string;
}

const openIndex = ref<number | null>(null);

const faqs: FAQ[] = [
  {
    question: "What are the key factors for success in forex trading?",
    answer: `Success comes with:
 <ul class="list-disc pl-4">
 <li><b>Discipline and patience</b> - Discipline to follow your trading plan and patience to wait for the right set up.</li>
 <li><b>Emotional Control</b> - Trading is a decision making process, emotional decisions can lead to revenge trading, overtrading which can lead to significant losses.</li>
 <li><b>Adaptability and good decision making skills</b> - Markets can move fast, so being to adapt quickly and make.</li>
 </ul>
 <br />
 Trading system can be taught, practiced and conditioned, however you need the above to execute it successfully.`
  },
  {
    question: "What is practice trading?",
    answer:
      "Practice trading is stimulated trading in live trading enviornment with virtual money. It is exactly the same as trading in a live account."
  },
  {
    question: "How many hours a day do I need to trade?",
    answer: "4-5 hours."
  },
  {
    question: "Which trading session should I trade?",
    answer:
      "We spilt the 24 hour market into Asia open (11 pm GMT), London open (8 am GMT) and US open (1 pm GMT). You can choose which session you want to trade. The best time is 7 am GMT to 4 pm GMT (US open). You can choose which session to trade, the best time is 7 am GMT to 1pm GMT (Europe and US open)."
  },
  {
    question: "I am not new trading, can I join?",
    answer:
      "Absolutely, you can learn our strategy and practice or live trade. If you require special trading tools, it's someting we can discuss."
  },
  {
    question: "Can I use this trading system for prop trading?",
    answer:
      "Our trading strategy and risk management solutions are perfectly suited for prop trading. We offer custom-tailored solutions specifically designed for prop traders. Our tools help you monitor your drawdown to ensure you stay within the drawdown limits. If you encounter a drawdown, we are here for feedback and support and we recommend that go back to trading in a demo account and return to your prop account once you're ready, especially when you are already funded. It ciritcal to gives yourself plenty of room for drawdown so you dont have to go back to the challenge phase. If you are new to prop trading, we can also help you navigate the prop firm industry so you make the right choice."
  },
  {
    question: "Which brokers can I use?",
    answer:
      "Our platform supports different number of brokers for trading which are listed below: EightCap, FTMO, ThinkMarket, Purple Trading, Fintokei, Blueberry, and Monevis. We recommend opening a practice account with blueberry or Purple trading, note that FTMO demo account is only valid for 2 weeks. If your broker is not on the list, please get in touch with us."
  },
  {
    question: "What instruments can I use for trading?",
    answer: `Our platform supports different number of instruments which are listed below:<br/><br /><span class="font-bold">Major Currency Pairs</span>: AUDUSD, EURUSD, GBPUSD, NZDUSD, USDCAD, USDJPY, USDCHF, AUDJPY, CHFJPY, GBPJPY, EURJPY, CADJPY, NZDJPY, EURAUD, EURCAD, EURGBP, EURNZD, EURCHF, GBPCAD, GBPAUD, GBPNZD, GBPCHF, AUDCHF, CADCHF, NZDCHF, AUDCAD, AUDNZD, NZDCAD<br/><br/><span class="font-bold">CFD INDICES</span>: SPX500, NAS100, US30, DAX40, UK100<br/><br/><span class="font-bold">CRYPTO</span>: BTCUSD<br/><br/><span class="font-bold">Commodities</span>: XAUUSD, XAGUSD, BRENT, WTI<br/><br/>We will be adding more instruments in the future.`
  },
  {
    question: "Can I use chart in multiple windows?",
    answer:
      "Currently, our platform does not support multiple chart windows. However, we do have plan for implementing such feature in the future."
  },
  {
    question: "Are indicators available?",
    answer:
      "Currently, our platform does not include indicators. Our strategy price action trading, which means you trade based on the price movements you see on the charts. However, we do plan to implement price-action indicators in the future, including algorithm-driven pattern recognition, statistical price data, and more."
  },
  {
    question: "How to set up Tradeawaay Expert Advisor in MetaTrader 5?",
    answer:
      'You can set up Expert Advisor in MetaTrader 5 by watching video tutorial (look for "Watch the video" button above).'
  },
  {
    question: "What about chart data provider and market watch?",
    answer:
      "We have multiple data provider, all of them are top-tier brokers. We monitor market price data and switch our chart data between brokers to accurately reflect market prices. The bid and ask prices on the market watch, as well as order data, are streamed directly from your terminal."
  },
  {
    question:
      "What is the connection speed between Tradeawaay and my trading terminal?",
    answer:
      "From our testing, the typical speed of MetaTrader 5 is around 1 millisecond (ms). However, this can vary depending on the speed of your internet connection. If you experience slower speeds, we recommend hosting MetaTrader 5 in the cloud. This setup offers a more stable and faster connection. Cloud servers can be set up for as little as $15/month. If you need assistance with setting up your cloud server, feel free to contact us for support."
  },
  {
    question: "My trading terminal is not working, how can I troubleshoot it?",
    answer: `If your trading terminal is not working, here are some steps you can follow to troubleshoot the issue:<br/><ol class="list-decimal px-4 mt-1"><li><span class="font-medium">Check Broker Support:</span> Ensure that your broker is supported by our platform. If your broker is not currently supported, please email us, and we'll work on adding support for it as soon as possible.</li><li><span class="font-medium">Verify Expert Advisor:</span> Make sure that your Expert Advisor (EA) is running in your MetaTrader 5 (MT5) terminal and that you are logged into your broker account.</li><li><span class="font-medium">Refesh Market Watch:</span> Click the refresh button in the Market Watch panel to update the data.</li></ol><br/>If the problem persists, please email us with the details, and we will investigate further to resolve the issue.`
  },
  {
    question: "What trading strategies do you offer?",
    answer: `We teach two intra-day trading systems, both based on pure price action. Our trading platform enables you to pinpoint trades directly from the chart, which is key to the success of these strategies. Trading sessions are structured around the Asia, Europe, US, and close sessions.
    <br /><br />

    <b>Session trend system</b>:
    <br /><br />

    This trading system utilizes our in-house data analytics tool to identify price trends within each currency, ranking them based on their strength. The key to this system is detecting early shifts in currency strength in each session and selecting the correct currency pair to trade. Additionally, chart patterns are used to identify breakouts, continuations, or reversal. This system focuses on taking only a few trades per day, with the entry strategy centered around trading the continuation or reversal of the daily trend.
    <br /><br />

    <b>Candle Scalping system</b>:
    This strategy is a scalper's paradise. Rather than relying on fixed pip sizes, it uses candlestick patterns to scalp the markets. It trades both the continuation and reversal of daily or mini trends. The trading tools involve chart patterns, swing high and low formations, and individual or groups of candlestick patterns to execute entry strategies. This system is designed to make more trades with very little risk per trade.
    <br /><br />

    More impotently we focus on risk management like position sizing, open order risk management, market news risk and account risk to complete the trading system. We are also in the process of automating signal alerts for these strategies. 
    <br /><br />

    We also encourage our students to develop and create their own systems, and we are happy to provide feedback and guidance along the way`
  }
];

const toggleFaq = (index: number) => {
  openIndex.value = openIndex.value === index ? null : index;
};
</script>

<template>
  <div id="faq" class="bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-3xl">
      <div class="mb-12 text-center">
        <h1 class="mb-4 text-4xl font-bold text-gray-900">FAQ</h1>
        <p class="text-lg text-gray-600">
          Have your questions? We've got you covered!
        </p>
      </div>

      <div class="space-y-4">
        <div
          v-for="(faq, index) in faqs"
          :key="index"
          class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm transition-all duration-200 ease-in-out hover:shadow-md"
        >
          <button
            class="flex w-full cursor-pointer items-center justify-between px-6 py-4 text-left focus:outline-none"
            @click="toggleFaq(index)"
          >
            <span class="text-lg font-medium text-gray-900">{{
              faq.question
            }}</span>
            <ChevronDown
              class="h-5 w-5 text-gray-500 transition-transform duration-200"
              :class="{ 'rotate-180 transform': openIndex === index }"
            />
          </button>

          <div
            class="overflow-hidden px-6 transition-all duration-200 ease-in-out"
            :class="openIndex === index ? 'max-h-[1000px] py-4' : 'max-h-0'"
          >
            <p class="text-gray-600" v-html="faq.answer"></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
