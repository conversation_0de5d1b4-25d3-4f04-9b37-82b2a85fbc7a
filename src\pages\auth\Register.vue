<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

import { bool, object, string } from "yup";

import { StripeError } from "@/helpers/errors";
import {
  getClientValidationErrors,
  getServerErrors
} from "@/helpers/getErrors";

import { useUserStore } from "@/store/userStore";

import { STRIPE_APPEARANCE, STRIPE_CLASSES } from "@/utilities/stripe";

import CompanyLogoSVG from "@/assets/logo_white.svg";

import Alert from "@/components/Alert.vue";
import Checkbox from "@/components/Checkbox.vue";
import InputLabel from "@/components/InputLabel.vue";
import InputText from "@/components/InputText.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";
import Select from "@/components/Select.vue";

import Appbar from "../website/components/Appbar.vue";
import { axios } from "@/api";
import { Broker, SubscriptionPlan } from "@/types";
import {
  StripeError as IStripeError,
  Stripe,
  StripeAddressElement,
  StripeCardCvcElement,
  StripeCardExpiryElement,
  StripeCardNumberElement,
  StripeElements,
  loadStripe
} from "@stripe/stripe-js";

const route = useRoute();
const router = useRouter();

const userStore = useUserStore();

const user = ref({
  name: "",
  email: "",
  password: "",
  broker: "",
  broker_name: "",
  subscription_plan: (route.query["subscription_plan"] as string) ?? "",
  tos: false
});
const brokerList = ref<Broker[]>([]);
const subscriptionPlans = ref<SubscriptionPlan[]>([]);
const isStripePaymentLoaded = ref(false);
const btnLoading = ref(false);
const stripeErrors = ref<IStripeError | null>(null);
const validationErrors = ref<typeof user.value | null>(null);
const serverErrors = ref<string[]>([]);

let stripe: Stripe | null;
let stripeElement: StripeElements;
let stripeCardNumberEl: StripeCardNumberElement;
let stripeCardExpiryDateEl: StripeCardExpiryElement;
let stripeCardCVCEl: StripeCardCvcElement;
let stripeAddressEl: StripeAddressElement;

const validationSchema = object({
  name: string().required("Name is required."),
  email: string()
    .email("Email addrss in invalid.")
    .required("Email is required."),
  password: string()
    .min(8, "Password must be at least 8 characters long.")
    .required("Password is required."),
  broker: string().required("Broker is required."),
  broker_name: string().when("broker", ([broker], schema) => {
    return broker === "others"
      ? schema.required("Broker name is required.")
      : schema.optional();
  }),
  subscription_plan: string().required("Subscription plan is required."),
  tos: bool().oneOf([true], "The terms and conditions must be accepted.")
});

onMounted(() => {
  getBrokers();

  getSubscriptionPlans();

  intializeStripePayment();
});

async function getBrokers() {
  try {
    const resp = await axios.get("/brokers");

    const brokers: string[] = [];

    brokerList.value = resp.data.data.filter(({ broker }: Broker) => {
      if (brokers.includes(broker)) {
        return false;
      }

      brokers.push(broker);
      return true;
    });
  } catch (e) {
    console.error(e);
  }
}

async function getSubscriptionPlans() {
  try {
    const resp = await axios.get("/payments/subscription-plan", {
      params: {
        trial: true
      }
    });

    subscriptionPlans.value = resp.data.data;
  } catch (e) {
    console.error(e);
  }
}

async function intializeStripePayment() {
  try {
    stripe = await loadStripe(import.meta.env.VITE_APP_STRIPE_PUBLISHABLE_KEY);

    if (!stripe) {
      throw new StripeError("Cannot load Stripe payment form.");
    }

    stripeElement = stripe.elements({
      appearance: STRIPE_APPEARANCE
    });

    stripeCardNumberEl = stripeElement.create("cardNumber", {
      showIcon: true,
      iconStyle: "solid",
      style: {
        base: {
          iconColor: "#233876"
        }
      },
      classes: STRIPE_CLASSES,
      placeholder: "Credit Card Number"
    });

    stripeCardExpiryDateEl = stripeElement.create("cardExpiry", {
      classes: STRIPE_CLASSES
    });

    stripeCardCVCEl = stripeElement.create("cardCvc", {
      classes: STRIPE_CLASSES
    });

    stripeAddressEl = stripeElement.create("address", {
      mode: "billing"
    });

    stripeCardNumberEl.mount("#payment-card-number");
    stripeCardExpiryDateEl.mount("#payment-card-expiry-date");
    stripeCardCVCEl.mount("#payment-card-cvc");
    stripeAddressEl.mount("#payment-card-address");

    isStripePaymentLoaded.value = true;
  } catch (e) {
    isStripePaymentLoaded.value = false;
  }
}

function handleBroker() {
  if (user.value.broker === "others") {
    user.value.broker_name = "";
  }
}

async function handleRegister() {
  try {
    validationErrors.value = null;
    serverErrors.value = [];

    btnLoading.value = true;

    const validatedUser = await validationSchema.validate(user.value, {
      abortEarly: false
    });

    if (!stripe) {
      throw new StripeError("Cannot load Stripe payment form.");
    }

    const billingDetails = await stripeAddressEl.getValue();

    const { paymentMethod, error } = await stripe.createPaymentMethod({
      type: "card",
      card: stripeCardNumberEl,
      billing_details: billingDetails.value
    });

    if (error) {
      stripeErrors.value = error;
      throw new StripeError("Stripe error occurred.");
    }

    let brokerName: string | undefined | null = null;

    if (validatedUser.broker === "others") {
      brokerName = validatedUser.broker_name;
    }

    const data = {
      name: validatedUser.name,
      email: validatedUser.email,
      password: validatedUser.password,
      broker: validatedUser.broker,
      broker_name: brokerName,
      subscription_plan: validatedUser.subscription_plan,
      payment_method: "stripe",
      card_nonce: paymentMethod.id,
      billing_details: billingDetails.value,
      tos: validatedUser.tos
    };

    const resp = await axios.post("/auth/register", data);

    userStore.user = resp.data.user;

    const userToken = resp.data.user_token.access_token;
    userStore.userAccessToken = userToken;

    router.push({ name: "trading" });
  } catch (e) {
    btnLoading.value = false;

    validationErrors.value = getClientValidationErrors(e);
    serverErrors.value = getServerErrors(e);
  }
}
</script>

<template>
  <div class="hidden min-h-screen grid-cols-2 md:grid">
    <div
      id="introduction"
      class="fixed grid min-h-screen w-1/2 items-center text-white"
    >
      <div class="px-5 lg:px-16">
        <router-link
          class="inline-flex items-center gap-x-1 text-4xl font-bold lg:text-5xl"
          :to="{ name: 'home' }"
        >
          <CompanyLogoSVG width="80" height="80" />
          Tradeawaay
        </router-link>

        <h2 class="mt-1 text-lg italic md:text-xl">
          "TRADING IS DECISION MAKING."
        </h2>

        <p class="mt-4 text-sm text-gray-300">
          Stock trading involves a high level of risk, and may not be suitable
          for all investors. You should carefully consider your objectives,
          financial situation, needs and level of experience before entering
          into any margined transactions with us, and seek independent advice if
          necessary.
        </p>
      </div>

      <div class="absolute bottom-2 w-full">
        <p class="text-center text-sm">
          © {{ new Date().getFullYear() }} Tradeawaay. All rights reserved.
        </p>
      </div>
    </div>

    <div></div>

    <div class="flex items-center justify-center bg-accent py-20">
      <div class="w-3/4 rounded-md border bg-white p-5 text-sm 2xl:w-3/5">
        <h2 class="mt-1 text-xl font-bold">Sign Up</h2>

        <p class="mt-1 text-gray-600">
          Discover a better way of trading with us.
        </p>

        <Alert
          variant="danger"
          class="mt-2 text-xs"
          v-if="serverErrors.length !== 0"
        >
          <div v-for="error in serverErrors">
            {{ error }}
          </div>
        </Alert>

        <form novalidate class="mt-3" @submit.prevent="handleRegister">
          <div>
            <InputLabel for="name">
              Name
              <span class="text-danger">*</span>
            </InputLabel>

            <InputText
              id="name"
              type="text"
              placeholder="Name"
              :error="validationErrors?.name"
              v-model="user.name"
            />
          </div>

          <div class="mt-5">
            <InputLabel for="email">
              Email
              <span class="text-danger">*</span>
            </InputLabel>

            <InputText
              id="email"
              type="email"
              placeholder="Email"
              :error="validationErrors?.email"
              v-model="user.email"
            />
          </div>

          <div class="mt-5">
            <InputLabel for="password">
              Password
              <span class="text-danger">*</span>
            </InputLabel>

            <InputText
              id="password"
              type="password"
              placeholder="Password"
              :error="validationErrors?.password"
              v-model="user.password"
            />
          </div>

          <div class="mt-5">
            <InputLabel for="broker">
              Broker
              <span class="text-danger">*</span>
            </InputLabel>

            <Select
              id="broker"
              placeholder="Broker"
              :error="validationErrors?.broker"
              v-model="user.broker"
              @change="handleBroker"
            >
              <option
                :value="item.broker"
                v-for="item in brokerList"
                :key="item.broker"
              >
                {{ item.display_name }}
              </option>

              <option value="others">Others</option>
            </Select>
          </div>

          <div class="mt-5" v-if="user.broker === 'others'">
            <InputLabel for="broker_name">
              Broker Name
              <span class="text-danger">*</span>
            </InputLabel>

            <InputText
              id="broker_name"
              placeholder="Broker Name"
              :error="validationErrors?.broker_name"
              v-model="user.broker_name"
            />
          </div>

          <div class="mt-5">
            <InputLabel for="subscription_plan">
              Subscription Plan
              <span class="text-danger">*</span>
            </InputLabel>

            <Select
              id="subscription_plan"
              placeholder="Subscription Plan"
              :error="validationErrors?.subscription_plan"
              v-model="user.subscription_plan"
            >
              <option
                :value="subscription.name"
                v-for="subscription in subscriptionPlans"
                :key="subscription._id"
              >
                {{ subscription.service_name }} (${{ subscription.charge }}/{{
                  subscription.duration
                }})
              </option>
            </Select>
          </div>

          <div class="mt-5 flex items-center justify-between pr-2">
            <h2 class="text-base">
              <span class="font-medium">Payment Information</span>
              <span class="text-danger"> *</span>
            </h2>

            <img
              src="https://cdn.worldvectorlogo.com/logos/stripe-4.svg"
              width="48"
            />
          </div>

          <Alert
            variant="danger"
            class="mt-1 text-xs"
            v-if="!isStripePaymentLoaded"
          >
            Unable to load payment form. Please refesh the page and try again.
          </Alert>

          <div class="mt-2 grid grid-cols-2 gap-x-3 gap-y-2">
            <div
              id="payment-card-number"
              class="col-span-2 rounded-lg border border-gray-300 bg-accent px-2 pb-3 pt-3"
              :class="{ '!border-danger': stripeErrors }"
            ></div>

            <div
              id="payment-card-expiry-date"
              class="rounded-lg border border-gray-300 bg-accent px-2 pb-3 pt-3"
              :class="{ '!border-danger': stripeErrors }"
            ></div>

            <div
              id="payment-card-cvc"
              class="rounded-lg border border-gray-300 bg-accent px-2 pb-3 pt-3"
              :class="{ '!border-danger': stripeErrors }"
            ></div>

            <div class="px-1 text-xs text-danger" v-if="stripeErrors">
              {{ stripeErrors.message }}
            </div>

            <div id="payment-card-address" class="col-span-2 mt-2"></div>
          </div>

          <div class="mt-2 text-xs text-gray-600">
            Note: <br />
            Subscription starts after 1 week of trial period.* <br />
            Cancel your subscription anytime you want.*
          </div>

          <div class="mt-5 flex items-center gap-x-2">
            <Checkbox
              id="tos"
              :error="validationErrors?.tos ? true : false"
              v-model="user.tos"
            />

            <InputLabel for="tos" class="!mb-0 font-normal">
              By creating an account, you agree to the

              <router-link
                :to="{ name: 'terms-of-service' }"
                class="font-semibold text-primary underline"
              >
                Terms of Service.
              </router-link>
            </InputLabel>
          </div>

          <div class="mt-1 text-xs text-danger" v-if="validationErrors?.tos">
            {{ validationErrors.tos }}
          </div>

          <div class="mt-3">
            <PrimaryButton
              type="submit"
              class="flex w-full justify-center"
              :loading="btnLoading"
            >
              Register
            </PrimaryButton>
          </div>
        </form>

        <div class="mt-6 text-center">
          Already have an account?

          <router-link :to="{ name: 'login' }" class="font-semibold underline">
            Sign In
          </router-link>
        </div>
      </div>
    </div>
  </div>

  <div class="flex min-h-screen flex-col md:hidden">
    <Appbar />

    <div class="grid grow place-items-center bg-accent px-5">
      <div class="container mx-auto">
        <div class="flex justify-center">
          <FontAwesomeIcon size="3x" icon="fa-solid fa-computer" />
        </div>

        <div class="mt-5 text-center text-lg font-bold">
          Oops! Sign Up Not Available on Mobile Devices
        </div>

        <div class="mt-4 text-center">
          Our website is currently not supported on mobile devices. For the best
          experience, please access the site using a laptop or desktop computer.
          We apologize for any inconvenience and appreciate your understanding!
          <br />
          😊
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
#introduction {
  background-image: url("@/assets/shiny.svg");
  background-size: cover;
}
</style>
