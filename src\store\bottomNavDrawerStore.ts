import { ref } from "vue";
import { defineStore } from "pinia";

export const useBottomNavDrawerStore = defineStore("bottom-nav-drawer", () => {
  const bottomNavContentArea = ref(false);
  const bottomNavContentAreaHeight = ref(300);
  const editTradeModal = ref(false);
  const editTradeModalPosition = ref({
    top: 45,
    left: 52
  });
  const updateTp1 = ref(false);
  const deleteTradeModal = ref(false);

  const wasTradeRecentlyUpdated = false;

  function toggleBottomNavContentArea(toggle: boolean) {
    bottomNavContentArea.value = toggle;
  }

  function toggleEditTradeModal(toggle: boolean) {
    editTradeModal.value = toggle;
  }

  function setEditTradeModalPosition(top: number, left: number) {
    editTradeModalPosition.value = {
      top,
      left
    };
  }

  function toggleDeleteTradeModal(toggle: boolean) {
    deleteTradeModal.value = toggle;
  }

  return {
    bottomNavContentArea,
    bottomNavContentAreaHeight,
    editTradeModal,
    editTradeModalPosition,
    updateTp1,
    deleteTradeModal,
    wasTradeRecentlyUpdated,
    toggleBottomNavContentArea,
    toggleEditTradeModal,
    setEditTradeModalPosition,
    toggleDeleteTradeModal
  };
});
