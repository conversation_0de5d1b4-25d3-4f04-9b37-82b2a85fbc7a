<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { onClickOutside } from "@vueuse/core";

import { useAppbarStore } from "@/store/appbarStore";
import { Indicators } from "@/utilities/constants";

import Search from "@/components/Search.vue";
import NavItem from "@/components/NavItem.vue";

import CloseSVG from "@/assets/svg/close.svg";

const appbarStore = useAppbarStore();

const search = ref("");
const indicatorModal = ref<HTMLElement | null>(null);

const indicatorsList = Object.keys(Indicators);

onClickOutside(indicatorModal, () => appbarStore.toggleIndicatorToolbar(false));

onMounted(() => {
  document.getElementById("search-indicator")?.focus();
});

const filteredIndicatorsList = computed(() => {
  return indicatorsList.filter((indicator) =>
    indicator.toLowerCase().includes(search.value.toLowerCase())
  );
});
</script>

<template>
  <div
    class="absolute left-0 top-0 z-10 h-full w-full bg-backdrop opacity-30"
  ></div>

  <div
    ref="indicatorModal"
    id="indicators-toolbar"
    class="absolute left-1/2 top-1/2 z-20 w-[400px] -translate-x-1/2 -translate-y-1/2 overflow-hidden rounded-md border border-gray-200 bg-white shadow-lg"
  >
    <div id="indicators-toolbar-header" class="relative flex justify-end p-0.5">
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 font-medium"
      >
        INDICATORS
      </div>

      <NavItem @click="appbarStore.toggleIndicatorToolbar(false)">
        <CloseSVG />
      </NavItem>
    </div>

    <div class="border-t border-gray-200 py-2">
      <Search
        class="pb-1.5 pt-2"
        id="search-indicator"
        placeholder="Search..."
        v-model="search"
      />

      <div class="mt-3 px-3 text-xs text-gray-500">SCRIPT NAME</div>
    </div>

    <div class="scrollbar h-[275px] overflow-y-auto pb-1 text-sm">
      <div
        class="cursor-default px-3 py-2 hover:bg-accent"
        v-for="indicator in filteredIndicatorsList"
      >
        {{ indicator }}
      </div>

      <div
        class="mt-5 text-center text-gray-500"
        v-if="filteredIndicatorsList.length === 0"
      >
        No results found.
      </div>
    </div>
  </div>
</template>
