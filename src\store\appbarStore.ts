import { ref } from "vue";
import { defineStore } from "pinia";

export const useAppbarStore = defineStore("appbar", () => {
  const modals = ref({
    searchSymbolModal: false,
    chartSettingsModal: false,
    placeNewOrderToolbar: false,
    deleteTradeModal: false
  });

  function toggleModal(id: keyof typeof modals.value, state: boolean) {
    for (let [key, _] of Object.entries(modals.value)) {
      const k = key as keyof typeof modals.value;

      if (key === id) {
        modals.value[k] = state;
      } else {
        modals.value[k] = false;
      }
    }
  }

  function closeAllModals() {
    for (let [key, _] of Object.entries(modals.value)) {
      const k = key as keyof typeof modals.value;
      modals.value[k] = false;
    }
  }

  return {
    modals,
    toggleModal,
    closeAllModals
  };
});
