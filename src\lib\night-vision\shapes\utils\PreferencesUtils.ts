import {
  Background,
  DateRangeLabelProperties,
  IChartBotbarDisplay,
  IChartSidebarDisplay,
  IFib,
  IFibLevel,
  ILine,
  ILinePoint,
  ILinePointDisplay,
  IText,
  LabelProperties,
  PriceRangeLabelProperties
} from "../base/shape-properties.types";

const defaultLineProperties: ILine = {
  line_color: "#00b3e6",
  line_type: "solid",
  line_width: 2,
  extend_dir_1: false,
  extend_dir_2: false,
  show_label: false,
  show_price_range: true,
  show_percent_change: true,
  show_change_in_pips: true,
  show_diff: true,
  show_bars_range: true,
  show_date_time_range: true,
  show_distance: true,
  show_angle: true
};

const defaultLabelProperties: LabelProperties = {
  show_label: false,
  label_position: "center",
  show_price_range: true,
  show_percent_change: true,
  show_change_in_pips: true,
  show_diff: true,
  show_bars_range: true,
  show_date_time_range: true,
  show_distance: true,
  show_angle: true,
  labelStyle: {
    bgColor: "#e2e8f0",
    fontColor: "#000",
    fontSize: 15,
    fontFace: "Roboto",
    lineSpacing: 8
  }
};

const defaultPriceRangeLabelProperties: PriceRangeLabelProperties = {
  show_label: true,
  label_position: "vertical-arrow",
  show_price_range: true,
  show_percent_change: true,
  show_change_in_pips: true,
  show_diff: true,
  labelStyle: defaultLabelProperties.labelStyle
};

const defaultDateRangeLabelProperties: DateRangeLabelProperties = {
  show_label: true,
  label_position: "horizontal-arrow",
  labelStyle: defaultLabelProperties.labelStyle,
  show_bars_range: true,
  show_date_time_range: true,
  show_distance: true
};

const defaultShowPointDisplay: ILinePointDisplay = {
  show_point_default: false,
  show_point_hover: true,
  show_point_selected: true
};

// const defaultHidePointDisplay: ILinePointDisplay = {
//   show_point_default: false,
//   show_point_hover: false,
//   show_point_selected: false
// }

const defaultArrowPointDisplay: ILinePointDisplay = {
  show_point_default: true,
  show_point_hover: true,
  show_point_selected: true
};

const defaultArrowPointProperties: ILinePoint = {
  point_position: "coords",
  point_shape: "arrow",
  point_width_default: 10,
  point_width_hover: 12,
  point_width_selected: 14
};

const defaultSquarePointProperties: ILinePoint = {
  point_position: "midpoint",
  point_shape: "square",
  point_width_default: 0,
  point_width_hover: 5,
  point_width_selected: 7
};

const defaultCirclePointProperties: ILinePoint = {
  point_position: "coords",
  point_shape: "circle",
  point_width_default: 0,
  point_width_hover: 3,
  point_width_selected: 5
};

const defaultHideSidebarDisplay: IChartSidebarDisplay = {
  show_sidebar_default: false,
  show_sidebar_hover: false,
  show_sidebar_selected: false
};

const defaultShowSidebarDisplay: IChartSidebarDisplay = {
  show_sidebar_default: false,
  show_sidebar_hover: false,
  show_sidebar_selected: true
};

const defaultHideBotbarDisplay: IChartBotbarDisplay = {
  show_botbar_default: false,
  show_botbar_hover: false,
  show_botbar_selected: false
};

const defaultShowBotbarDisplay: IChartBotbarDisplay = {
  show_botbar_default: false,
  show_botbar_hover: false,
  show_botbar_selected: true
};

const defaultBackgroundProperties: Background = {
  fill_color: "#ffffff1a",
  fill_opacity: 0
};

export type ILineProperties = {
  lineProperties: ILine;
  sidebarDisplay: IChartSidebarDisplay;
  botbarDisplay: IChartBotbarDisplay;
  pointDisplay: ILinePointDisplay;
  pointProperties: ILinePoint;
  labelProperties: LabelProperties;
  labelPosition?: LabelPosition;
};

export interface DateRangeProperties
  extends Omit<ILineProperties, "labelProperties"> {
  labelProperties: DateRangeLabelProperties;
}

export interface PriceRangeProperties
  extends Omit<ILineProperties, "labelProperties"> {
  labelProperties: PriceRangeLabelProperties;
}

export interface BoxProperties
  extends Omit<ILineProperties, "labelProperties"> {
  backgroundProperties: Background;
}

export type ITextProperties = {
  textProperties: IText;
  sidebarDisplay: IChartSidebarDisplay;
  botbarDisplay: IChartBotbarDisplay;
  pointDisplay: ILinePointDisplay;
  pointProperties: ILinePoint;
};

export type IFibProperties = {
  fibLevels: IFibLevel[];
  sidebarDisplay: IChartSidebarDisplay;
  botbarDisplay: IChartBotbarDisplay;
  lineProperties: ILine;
  levelProperties: IFib;
  levelTextProperties: IText;
  pointDisplay: ILinePointDisplay;
  pointProperties: ILinePoint;
};

/**
 * Draft for User Preference Settings for each type of shapes
 * Must use shapeInstance.type to save any changes done to the line
 * Example:
 * Update Color, Update Line Width etc. are in the same function.
 * So we can call updatePreference method from BaseLine class which takes lineStyles, sidebarStyles, botbarStyles
 * to update the static preferences.
 * Also we should have unique updatePreference for Text and Fib Line since they do not fit in this category
 */
export type PreferenceUtilsType = typeof PreferenceUtils;
export type LabelPosition =
  | "top"
  | "left"
  | "right"
  | "bottom"
  | "center"
  | "vertical-arrow"
  | "horizontal-arrow";

export class PreferenceUtils {
  static "trend-line": ILineProperties = {
    lineProperties: { ...defaultLineProperties },
    sidebarDisplay: { ...defaultShowSidebarDisplay },
    botbarDisplay: { ...defaultShowBotbarDisplay },
    pointDisplay: { ...defaultShowPointDisplay },
    pointProperties: { ...defaultCirclePointProperties },
    labelProperties: { ...defaultLabelProperties }
  };

  static "horizontal-line": ILineProperties = {
    lineProperties: { ...defaultLineProperties },
    sidebarDisplay: {
      ...defaultShowSidebarDisplay,
      show_sidebar_default: true
    },
    botbarDisplay: { ...defaultHideBotbarDisplay },
    pointDisplay: { ...defaultShowPointDisplay },
    pointProperties: { ...defaultSquarePointProperties },
    labelProperties: { ...defaultLabelProperties }
  };

  static "vertical-line": ILineProperties = {
    lineProperties: { ...defaultLineProperties },
    sidebarDisplay: { ...defaultHideSidebarDisplay },
    botbarDisplay: {
      ...defaultShowBotbarDisplay,
      show_botbar_default: true
    },
    pointDisplay: { ...defaultShowPointDisplay },
    pointProperties: { ...defaultSquarePointProperties },
    labelProperties: { ...defaultLabelProperties }
  };

  static "price-range": PriceRangeProperties = {
    lineProperties: {
      ...defaultLineProperties,
      show_label: true
    },
    sidebarDisplay: { ...defaultHideSidebarDisplay },
    botbarDisplay: { ...defaultShowBotbarDisplay },
    pointDisplay: { ...defaultArrowPointDisplay },
    pointProperties: { ...defaultArrowPointProperties },
    labelProperties: { ...defaultPriceRangeLabelProperties }
  };

  static "date-range": DateRangeProperties = {
    lineProperties: {
      ...defaultLineProperties,
      show_label: true
    },
    sidebarDisplay: { ...defaultHideSidebarDisplay },
    botbarDisplay: { ...defaultShowBotbarDisplay },
    pointDisplay: { ...defaultArrowPointDisplay },
    pointProperties: { ...defaultArrowPointProperties },
    labelProperties: {
      ...defaultDateRangeLabelProperties
    }
  };

  static "date-and-price-range" = {
    lineProperties: { ...defaultLineProperties, show_label: false },
    sidebarDisplay: { ...defaultShowSidebarDisplay },
    botbarDisplay: { ...defaultShowBotbarDisplay },
    pointDisplay: { ...defaultArrowPointDisplay },
    pointProperties: { ...defaultArrowPointProperties },
    coordsDisplay: { ...defaultShowPointDisplay },
    coordsPointProperties: { ...defaultCirclePointProperties },
    backgroundProperties: {
      ...defaultBackgroundProperties,
      fill_color: "#3366e61a"
    },
    labelProperties: {
      ...defaultLabelProperties,
      label_position: "vertical-arrow",
      show_angle: false,
      show_distance: false,
      show_label: true
    }
  };

  static box: BoxProperties = {
    lineProperties: { ...defaultLineProperties },
    sidebarDisplay: { ...defaultShowSidebarDisplay },
    botbarDisplay: { ...defaultShowBotbarDisplay },
    pointDisplay: { ...defaultShowPointDisplay },
    pointProperties: {
      ...defaultCirclePointProperties,
      point_width_default: 0,
      point_width_hover: 6,
      point_width_selected: 8
    },
    backgroundProperties: { ...defaultBackgroundProperties }
  };

  static crosshair: ILineProperties = {
    lineProperties: {
      ...defaultLineProperties,
      extend_dir_1: true,
      extend_dir_2: true,
      line_width: 2
    },
    sidebarDisplay: {
      ...defaultShowSidebarDisplay,
      show_sidebar_default: true
    },
    botbarDisplay: { ...defaultShowBotbarDisplay },
    pointDisplay: {
      show_point_default: true,
      show_point_hover: true,
      show_point_selected: true
    },
    pointProperties: {
      ...defaultSquarePointProperties,
      point_width_default: 8,
      point_width_hover: 8,
      point_width_selected: 10
    },
    labelProperties: { ...defaultLabelProperties }
  };

  static "fib-level": IFibProperties = {
    levelProperties: {
      // level_line_color: "#eaeaea22",
      level_line_color: null,
      level_line_type: "solid",
      level_line_width: 2,
      level_text_position: "left",
      extend_dir_1: false,
      extend_dir_2: false,
      reverse_order: true
    },
    levelTextProperties: {
      font: "Poppins",
      font_color: "",
      font_size: 14,
      bg_color: ""
    },
    lineProperties: { ...defaultLineProperties, line_type: "dashed" },
    pointDisplay: { ...defaultShowPointDisplay },
    pointProperties: { ...defaultCirclePointProperties },
    sidebarDisplay: { ...defaultShowSidebarDisplay },
    botbarDisplay: { ...defaultShowBotbarDisplay },
    fibLevels: [
      {
        level_position: 0,
        level_line_color: "#8f46b9",
        level_show: true
      },
      {
        level_position: 0.236,
        level_line_color: "#b95546",
        level_show: true
      },
      {
        level_position: 0.382,
        level_line_color: "#6666ff",
        level_show: true
      },
      {
        level_position: 0.5,
        level_line_color: "#46aab9",
        level_show: true
      },
      {
        level_position: 0.618,
        level_line_color: "#8f46b9",
        level_show: true
      },
      {
        level_position: 0.786,
        level_line_color: "#b95546",
        level_show: true
      },
      {
        level_position: 1,
        level_line_color: "#9900b3",
        level_show: true
      },
      {
        level_position: 1.618,
        level_line_color: "#46aab9",
        level_show: true
      },
      {
        level_position: 2.618,
        level_line_color: "#8f46b9",
        level_show: true
      },
      {
        level_position: 3.618,
        level_line_color: "#b95546",
        level_show: true
      },
      {
        level_position: 3.92,
        level_line_color: "#70b946",
        level_show: false
      },
      {
        level_position: 4.236,
        level_line_color: "#70b946",
        level_show: false
      }
    ]
  };

  static text: ITextProperties = {
    textProperties: {
      font_color: "#3366e6",
      font_size: 14,
      font: "sans_serif",
      bg_color: "#ffffff1a"
    },
    sidebarDisplay: { ...defaultShowSidebarDisplay },
    botbarDisplay: { ...defaultShowBotbarDisplay },
    pointDisplay: { ...defaultShowPointDisplay },
    pointProperties: { ...defaultCirclePointProperties }
  };

  static "no-shape" = {};
}
