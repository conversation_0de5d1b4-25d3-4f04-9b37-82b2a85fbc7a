/**
 * Converts snake case word to title case word.
 */
export function snakeToTitleCase(word: string) {
  return word
    .trim()
    .split("_")
    .map((w) => {
      if (w !== "") {
        w = w[0].toUpperCase() + w.slice(1);
      }
      return w;
    })
    .join(" ");
}

/**
 * Converts kebab case word to title case word.
 */
export function kebabToTitleCase(word: string) {
  return snakeToTitleCase(word.trim().replaceAll("-", "_"));
}

/**
 * Converts camel case word to title case word.
 */
export function camelToTitleCase(word: string) {
  let w = "";

  for (let i = 0; i < word.length; i++) {
    if (word[i] === word[i].toUpperCase()) {
      w += "_";
    }

    w += word[i].toLowerCase();
  }

  return snakeToTitleCase(w);
}
