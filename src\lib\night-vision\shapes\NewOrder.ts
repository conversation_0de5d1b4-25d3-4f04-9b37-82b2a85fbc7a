import { countDecimals } from "@/helpers/numberUtils";

import { TCoreData } from "../types";
import { PriceHLine } from "./PriceHLine";
import { BaseShape } from "./shapes/BaseShape";
import { DistanceUtils } from "./utils/DistanceUtils";

export interface OrderData {
  price: number;
  stopLoss: number;
  takeProfit: number;
  takeProfit1?: number;
}

export interface ILabelInfo {
  profit?: number;
  profitPercentage?: number;
  pip?: number;
  ratio?: number;
  volume1?: number;
  margin?: number;
  marginPercentage?: number;
}

export type OrderType = "buy" | "sell";
export type OrderExecution = "instant" | "order";

interface NewOrderOptions {
  orderData: OrderData;
  orderType: OrderType;
  orderExecution: OrderExecution;
  fixedX?: boolean;
  orderTime?: number;
  priceLabelInfo?: ILabelInfo;
  slLabelInfo?: ILabelInfo;
  tpLabelInfo?: ILabelInfo;
  tp1LabelInfo?: ILabelInfo;
}

export class NewOrder extends BaseShape {
  isNewOrder: boolean = true;
  minStopLoss: number | null = null;
  maxStopLoss: number | null = null;
  minTakeProfit: number | null = null;
  maxTakeProfit: number | null = null;
  orderData: OrderData;
  priceHLine: PriceHLine;
  stopLossHLine: PriceHLine;
  takeProfitHLine: PriceHLine;
  takeProfit1HLine: PriceHLine;
  orderType: OrderType;
  orderExecution: OrderExecution;
  fixedX: boolean = false;
  startX: number = 0;
  fibX: number = 0;
  orderTime: number = 0;
  labelDecimalPoints: number = 2;
  priceLabelInfo: ILabelInfo | null = {};
  slLabelInfo: ILabelInfo | null = {};
  tpLabelInfo: ILabelInfo | null = {};
  ratioLabel?: string;

  constructor(
    $core: TCoreData,
    uuid: string,
    {
      orderData,
      orderType = "buy",
      orderExecution = "instant",
      fixedX = false,
      orderTime = 0,
      priceLabelInfo,
      slLabelInfo,
      tpLabelInfo,
      tp1LabelInfo
    }: NewOrderOptions
  ) {
    super($core, uuid, []);
    this.type = "no-shape";
    this.orderData = orderData;
    this.orderType = orderType;
    this.orderExecution = orderExecution;
    this.orderTime = orderTime;
    this.fixedX = fixedX;

    if (priceLabelInfo) {
      this.priceLabelInfo = priceLabelInfo;
    }

    if (slLabelInfo) {
      this.slLabelInfo = slLabelInfo;
    }

    if (tpLabelInfo) {
      this.tpLabelInfo = tpLabelInfo;
    }

    if (slLabelInfo?.profit && tpLabelInfo?.profit) {
      this.ratioLabel = (slLabelInfo?.profit / tpLabelInfo?.profit).toString();
    }

    this.computeMinMax();

    this.labelDecimalPoints = countDecimals(orderData.price);

    this.computeRatioLabel();

    this.priceHLine = new PriceHLine(
      $core,
      `${uuid}-price`,
      [{ x: 0, y: orderData.price }],
      false,
      {
        onValueChange: this.updatePrice.bind(this),
        onDragEnd: this.onPriceDragEnd.bind(this),
        draggable: this.orderExecution === "instant" ? false : true,
        labelDeicmalPoints: this.labelDecimalPoints,
        labelInfo: priceLabelInfo,
        ratioLabel: this.ratioLabel
      }
    );

    this.stopLossHLine = new PriceHLine(
      $core,
      `${uuid}-sl`,
      [{ x: 0, y: orderData.stopLoss }],
      false,
      {
        onValueChange: this.updateStopLoss.bind(this),
        onDragEnd: this.onStopLossDragEnd.bind(this),
        minValue: this.minStopLoss,
        maxValue: this.maxStopLoss,
        labelDeicmalPoints: this.labelDecimalPoints,
        labelInfo: slLabelInfo
      }
    );

    this.takeProfitHLine = new PriceHLine(
      $core,
      `${uuid}-tp`,
      [{ x: 0, y: orderData.takeProfit }],
      false,
      {
        onValueChange: this.updateTakeProfit.bind(this),
        onDragEnd: this.onTakeProfitDragEnd.bind(this),
        minValue: this.minTakeProfit,
        maxValue: this.maxTakeProfit,
        labelDeicmalPoints: this.labelDecimalPoints,
        labelInfo: tpLabelInfo
      }
    );

    this.takeProfit1HLine = new PriceHLine(
      $core,
      `${uuid}-tp1`,
      [{ x: 0, y: 0 }],
      false,
      {
        onValueChange: this.updateTakeProfit1.bind(this),
        onDragEnd: this.onTakeProfit1DragEnd.bind(this),
        minValue: this.minTakeProfit,
        maxValue: this.maxTakeProfit,
        labelDeicmalPoints: this.labelDecimalPoints,
        labelInfo: tp1LabelInfo
      }
    );

    this.adjustDraggableLines();

    this.setupHLines();
  }

  /**
   * Checks if price, sl, tp, or tp1 lines are too close to each other.
   * If they are, this function will only make of on them draggable.
   */
  adjustDraggableLines() {
    const threshold = DistanceUtils.threshold;

    this.makeDraggable();

    const slPrice = Math.abs(
      this.stopLossHLine.points[0].screenY - this.priceHLine.points[0].screenY
    );
    const slTp = Math.abs(
      this.stopLossHLine.points[0].screenY -
        this.takeProfitHLine.points[0].screenY
    );
    const slTp1 = Math.abs(
      this.stopLossHLine.points[0].screenY -
        this.takeProfit1HLine.points[0].screenY
    );
    const priceTp = Math.abs(
      this.priceHLine.points[0].screenY - this.takeProfitHLine.points[0].screenY
    );
    const priceTp1 = Math.abs(
      this.priceHLine.points[0].screenY -
        this.takeProfit1HLine.points[0].screenY
    );
    const tpTp1 = Math.abs(
      this.takeProfitHLine.points[0].screenY -
        this.takeProfit1HLine.points[0].screenY
    );

    if (slPrice <= threshold && slTp <= threshold && slTp1 <= threshold) {
      this.makeUndraggable();
      this.stopLossHLine.draggable = true;
    } else if (slPrice <= threshold && slTp <= threshold) {
      this.takeProfitHLine.draggable = false;
      this.priceHLine.draggable = false;
    } else if (slPrice <= threshold) {
      this.priceHLine.draggable = false;
    } else if (priceTp <= threshold && priceTp1 <= threshold) {
      this.priceHLine.draggable = false;
      this.takeProfit1HLine.draggable = false;
    } else if (priceTp <= threshold) {
      this.priceHLine.draggable = false;
    } else if (tpTp1 <= threshold) {
      this.takeProfit1HLine.draggable = false;
    }
  }

  computeMinMax() {
    this.minStopLoss = this.orderType === "buy" ? null : this.orderData.price;
    this.maxStopLoss = this.orderType === "buy" ? this.orderData.price : null;

    this.minTakeProfit = this.orderType === "buy" ? this.orderData.price : null;
    this.maxTakeProfit = this.orderType === "buy" ? null : this.orderData.price;
  }

  assignMinMax() {
    this.stopLossHLine.minValue = this.minStopLoss;
    this.stopLossHLine.maxValue = this.maxStopLoss;
    this.takeProfitHLine.minValue = this.minTakeProfit;
    this.takeProfitHLine.maxValue = this.maxTakeProfit;

    if (this.orderType === "buy") {
      this.takeProfit1HLine.maxValue = this.orderData.takeProfit;
      this.takeProfit1HLine.minValue = this.orderData.price;
    } else {
      this.takeProfit1HLine.maxValue = this.orderData.price;
      this.takeProfit1HLine.minValue = this.orderData.takeProfit;
    }
  }

  computeRatioLabel() {
    if (this.slLabelInfo?.pip && this.tpLabelInfo?.pip) {
      const sl = this.stopLossHLine.points[0].y;
      const tp = this.takeProfitHLine.points[0].y;
      const price = this.priceHLine.points[0].y;
      const decimals = countDecimals(sl);

      let factor;

      if (decimals >= 4) {
        factor = 10000;
      } else {
        factor = 100;
      }

      const tpPip = Math.abs((price - tp) * factor);
      const slPip = Math.abs((price - sl) * factor);

      if (slPip === 0 || this.takeProfitHLine.labelInfo?.profit === Infinity) {
        this.ratioLabel = "NA";
      } else {
        const den = Math.abs(tpPip / slPip);
        this.ratioLabel = `1:${den.toFixed(2)}`;
      }
      this.priceHLine.updateRatioLabel(this.ratioLabel);
    }
  }

  updateOrderType(type: OrderType) {
    this.orderType = type;
  }

  updateOrderExecution(executionType: OrderExecution) {
    this.orderExecution = executionType;

    if (executionType === "order") {
      this.priceHLine.draggable = true;
    } else {
      this.priceHLine.draggable = false;
    }
  }

  updatePrice(value: number, labelInfo?: ILabelInfo) {
    this.orderData.price = value;
    this.priceHLine.updatePosition(value, labelInfo);
    this.computeRatioLabel();
    this.computeMinMax();
    this.assignMinMax();
    this.$core.hub.events.emit("redraw");
  }

  updateStopLoss(value: number, labelInfo?: ILabelInfo) {
    this.orderData.stopLoss = value;
    if (labelInfo) {
      this.slLabelInfo = labelInfo;
    }
    this.stopLossHLine.updatePosition(value, labelInfo);
    this.computeRatioLabel();
    this.$core.hub.events.emit("redraw");
  }

  updateTakeProfit(value: number, labelInfo?: ILabelInfo) {
    this.orderData.takeProfit = value;
    if (labelInfo) {
      this.tpLabelInfo = labelInfo;
    }
    this.takeProfitHLine.updatePosition(value, labelInfo);
    this.computeRatioLabel();
    this.assignMinMax();
    this.$core.hub.events.emit("redraw");
  }

  updateTakeProfit1(value: number, labelInfo?: ILabelInfo) {
    this.orderData.takeProfit1 = value;
    this.takeProfit1HLine.updatePosition(value, labelInfo);
    if (labelInfo) {
      this.takeProfit1HLine.labelInfo = labelInfo;
    }
    this.$core.hub.events.emit("redraw");
  }

  onPriceDragEnd() {
    this.$core.hub.events.emitSpec(
      "shapetool",
      "price-drag-end",
      this.priceHLine.points[0].y
    );
  }

  onStopLossDragEnd() {
    this.$core.hub.events.emitSpec(
      "shapetool",
      "sl-drag-end",
      this.stopLossHLine.points[0].y
    );
  }

  onTakeProfitDragEnd() {
    this.$core.hub.events.emitSpec(
      "shapetool",
      "tp-drag-end",
      this.takeProfitHLine.points[0].y
    );
  }

  onTakeProfit1DragEnd() {
    this.$core.hub.events.emitSpec(
      "shapetool",
      "tp1-drag-end",
      this.takeProfit1HLine.points[0].y
    );
  }

  setupHLines() {
    this.priceHLine.properties.lineProperties.line_color = "blue";
    this.stopLossHLine.properties.lineProperties.line_color = "red";
    this.takeProfitHLine.properties.lineProperties.line_color = "green";
    this.takeProfit1HLine.properties.lineProperties.line_color = "gray";

    this.priceHLine.setName("PRICE");
    this.stopLossHLine.setName("SL");
    this.takeProfitHLine.setName("TP");
    this.takeProfit1HLine.setName("TP1");
  }

  draw(ctx: CanvasRenderingContext2D): void {
    if (!this.fixedX) {
      this.startX = this.getLatestBarX();
    } else {
      this.startX = this.$core.layout.time2x(this.orderTime);
    }

    if (this.orderData.price !== 0) {
      // this.priceHLine.setStartX(this.startX);
      this.priceHLine.draw(ctx);
    }

    if (this.orderData.stopLoss !== 0) {
      // this.stopLossHLine.setStartX(this.startX);
      this.stopLossHLine.draw(ctx);
    }

    if (this.orderData.takeProfit !== 0) {
      // this.takeProfitHLine.setStartX(this.startX);
      this.takeProfitHLine.draw(ctx);
    }

    if (this.orderData.takeProfit1 !== 0) {
      // this.takeProfit1HLine.setStartX(this.startX);
      this.takeProfit1HLine.draw(ctx);
    }

    if (this.isNewOrder) {
      this.drawFibPoints(ctx);
    }
  }

  getLatestBarX(): number {
    const mainOv = this.$core.hub.mainOv;
    if (mainOv && mainOv.data && mainOv.data.length > 0) {
      if (this.$core.hub.indexBased) {
        const lastIndex = mainOv.data.length - 1;
        return this.$core.layout.ti2x(lastIndex, lastIndex);
      } else {
        const latestBar = mainOv.data[mainOv.data.length - 1];
        return this.$core.layout.time2x(latestBar[0]);
      }
    }
    return 0;
  }

  drawFibPoints(ctx: CanvasRenderingContext2D): void {
    if (!this.fixedX) {
      this.startX = this.getLatestBarX();
    }

    const startPrice = this.orderData.price;
    const endPrice = this.orderData.stopLoss;

    const fibRatios = [-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

    const priceDifference = Math.abs(startPrice - endPrice);

    ctx.fillStyle = "gray";
    ctx.textAlign = "left";
    ctx.font = "12px Arial";

    fibRatios.forEach((ratio) => {
      let price;
      if (this.orderType === "buy") {
        price = startPrice + priceDifference * ratio;
      } else {
        price = startPrice - priceDifference * ratio;
      }

      const y = this.$core.layout.value2y(price);

      ctx.fillRect(this.$core.layout.width - 100, y - 2, 2, 2);

      if (ratio === -1) {
        ctx.fillStyle = "red";
      } else if (ratio === 0) {
        ctx.fillStyle = "blue";
      }

      const textYPosition = ratio < 1 ? y - 6 : y + 2;

      ctx.fillText(
        `(${ratio}) ${price.toFixed(this.labelDecimalPoints)}`,
        this.$core.layout.width - 90,
        textYPosition
      );

      ctx.fillStyle = "gray";
    });
  }

  mousemove(event: MouseEvent): void {
    this.priceHLine.mousemove(event);
    this.stopLossHLine.mousemove(event);
    this.takeProfitHLine.mousemove(event);
    this.takeProfit1HLine.mousemove(event);

    if (this.priceHLine.dragging) {
      this.$core.hub.events.emitSpec(
        "shapetool",
        "price-line-dragged",
        this.priceHLine.points[0].y
      );
    }

    if (this.stopLossHLine.dragging) {
      this.$core.hub.events.emitSpec(
        "shapetool",
        "sl-line-dragged",
        this.stopLossHLine.points[0].y
      );
    }

    if (this.takeProfitHLine.dragging) {
      this.$core.hub.events.emitSpec(
        "shapetool",
        "tp-line-dragged",
        this.takeProfitHLine.points[0].y
      );
    }

    if (this.takeProfit1HLine.dragging) {
      this.$core.hub.events.emitSpec(
        "shapetool",
        "tp1-line-dragged",
        this.takeProfit1HLine.points[0].y
      );
    }
  }

  mousedown(event: MouseEvent): void {
    this.priceHLine.mousedown(event);
    this.stopLossHLine.mousedown(event);
    this.takeProfitHLine.mousedown(event);
    this.takeProfit1HLine.mousedown(event);
  }

  mouseup(event: MouseEvent): void {
    this.priceHLine.mouseup(event);
    this.stopLossHLine.mouseup(event);
    this.takeProfitHLine.mouseup(event);
    this.takeProfit1HLine.mouseup(event);

    this.adjustDraggableLines();
  }

  mouseover(event: MouseEvent): void {
    this.priceHLine.mouseover(event);
    this.stopLossHLine.mouseover(event);
    this.takeProfitHLine.mouseover(event);
    this.takeProfit1HLine.mouseover(event);
  }

  makeDraggable(): void {
    if (this.orderExecution !== "instant") {
      this.priceHLine.draggable = true;
    }
    this.stopLossHLine.draggable = true;
    this.takeProfitHLine.draggable = true;
    this.takeProfit1HLine.draggable = true;
  }

  makeUndraggable(): void {
    this.priceHLine.draggable = false;
    this.stopLossHLine.draggable = false;
    this.takeProfitHLine.draggable = false;
    this.takeProfit1HLine.draggable = false;
  }
}
