<script setup lang="ts">
import { computed, nextTick, onMounted, ref } from "vue";

import { DateTime } from "luxon";

import { useCandleStickStore } from "@/store/candleStickStore";
import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";

import { EconomicCalenderImportance } from "@/types/enums";

import { timezones } from "@/utilities/timezones";

import DatePicker from "@/components/DatePicker.vue";
import Dropdown from "@/components/Dropdown.vue";
import NavItem from "@/components/NavItem.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";
import Spinner from "@/components/Spinner.vue";

import { axios } from "@/api";
import { IEconomicCalendar } from "@/types";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const candleStickStore = useCandleStickStore();
const rightNavDrawerStore = useRightNavDrawerStore();

const width = ref(1100);
const height = ref(window.innerHeight);
const isLoading = ref(false);
const newsList = ref<IEconomicCalendar[]>([]);
const fromDate = ref("");
const toDate = ref("");
const currentDate = ref("");
const datePickerDropdown = ref(false);
const timezoneDropdown = ref(false);
const timezoneList = ref(timezones);
const timezone = ref("UTC");

onMounted(() => {
  timezone.value = candleStickStore.chartSettings.e_calendar_timezone;

  initializeDate();

  getNews();
});

const filteredNewsList = computed(() => {
  return newsList.value.map((news) => {
    const time = DateTime.fromFormat(news.time, "yyyy.MM.dd HH:mm", {
      zone: "Europe/Athens"
    })
      .setZone(timezone.value)
      .toFormat("yyyy.MM.dd HH:mm");

    return {
      ...news,
      time
    };
  });
});

function initializeDate() {
  const date = DateTime.now().setZone("utc");

  fromDate.value = date.startOf("week").toFormat("yyyy-MM-dd");

  toDate.value = date.endOf("week").toFormat("yyyy-MM-dd");

  currentDate.value = "";
}

async function getNews() {
  try {
    isLoading.value = true;

    newsList.value = [];

    const resp = await axios.get("/calendars", {
      params: {
        fromDate: fromDate.value,
        toDate: toDate.value
      }
    });

    const arr: IEconomicCalendar[] = [];

    resp.data.data.forEach((item: any) => {
      item["event_by_time"].forEach((value: any) => {
        value["events"].forEach((v: IEconomicCalendar) => {
          arr.push(v);
        });
      });
    });

    newsList.value = arr;

    isLoading.value = false;

    await nextTick();

    handleUpNext();
  } catch (e) {
    console.error(e);
  }
}

function isYearMonthDaySame(idx: number) {
  const prevTime = newsList.value[idx - 1]?.time;
  const currentTime = newsList.value[idx].time;

  if (!prevTime) {
    return false;
  }

  const d1 = DateTime.fromFormat(prevTime, "yyyy.MM.dd HH:mm", {
    zone: "Europe/Athens"
  })
    .setZone(timezone.value)
    .toFormat("yyyy-MM-dd");

  const d2 = DateTime.fromFormat(currentTime, "yyyy.MM.dd HH:mm", {
    zone: "Europe/Athens"
  })
    .setZone(timezone.value)
    .toFormat("yyyy-MM-dd");

  return d1 === d2;
}

function handleImpactColor(importance: string) {
  if (importance === EconomicCalenderImportance.NONE) {
    return "text-gray-500";
  } else if (importance === EconomicCalenderImportance.LOW) {
    return "text-yellow-300";
  } else if (importance === EconomicCalenderImportance.MODERATE) {
    return "text-orange-400";
  } else {
    return "text-red-600";
  }
}

function handleImpactTitle(importance: string) {
  if (importance === EconomicCalenderImportance.NONE) {
    return "Non-Economic";
  } else if (importance === EconomicCalenderImportance.LOW) {
    return "Low Impact Expected";
  } else if (importance === EconomicCalenderImportance.MODERATE) {
    return "Medium Impact Expected";
  } else {
    return "High Impact Expected";
  }
}

function handleDatePicker() {
  fromDate.value = currentDate.value;
  toDate.value = currentDate.value;

  getNews();
}

function handleTimezone(idx: number) {
  const tz = timezoneList.value[idx].timezone;

  timezone.value = tz;

  candleStickStore.chartSettings.e_calendar_timezone = tz;

  candleStickStore.storeChartSettings({
    e_calendar_timezone: tz
  });
}

function handleUpNext() {
  const date = DateTime.now().setZone(timezone.value);

  let nextDate = "";

  for (let i = 0; i < newsList.value.length; i++) {
    const d = DateTime.fromFormat(newsList.value[i].time, "yyyy.MM.dd HH:mm", {
      zone: "Europe/Athens"
    }).setZone(timezone.value);

    if (d > date) {
      nextDate = d.toFormat("yyyy-MM-dd-HH-mm");
      break;
    }
  }

  document.getElementById(nextDate)?.scrollIntoView({
    behavior: "smooth"
  });
}

function resetDates() {
  initializeDate();

  getNews();
}
</script>

<template>
  <div
    class="fixed right-0 top-0 border-l bg-white text-sm shadow-lg"
    :style="{ width: width + 'px', height: height + 'px' }"
  >
    <div class="flex items-center justify-between py-3 pl-3 pr-7">
      <div class="text-base font-bold">Economic Calendar</div>

      <div class="flex items-center justify-end gap-x-5">
        <div
          :class="
            isLoading
              ? 'pointer-events-none text-gray-300'
              : 'pointer-events-auto'
          "
          class="flex items-center justify-end gap-x-5"
        >
          <Dropdown
            id="economic-calendar-date-picker-dropdown"
            toggle-id="economic-calendar-date-picker-toggle-dropdown"
            :icon="false"
            :class="{ 'bg-accent': datePickerDropdown }"
            @show="datePickerDropdown = true"
            @hide="datePickerDropdown = false"
          >
            <template #text>
              <FontAwesomeIcon size="lg" icon="fa-regular fa-calendar" />
            </template>

            <template #content="{ close }">
              <DatePicker
                @selected="
                  () => {
                    close(), handleDatePicker();
                  }
                "
                v-model="currentDate"
              />
            </template>
          </Dropdown>

          <Dropdown
            id="economic-calendar-timezone-dropdown"
            toggle-id="economic-calendar-timezone-toggle-dropdown"
            :icon="false"
            :class="{ 'bg-accent': timezoneDropdown }"
            @show="timezoneDropdown = true"
            @hide="timezoneDropdown = false"
          >
            <template #text>
              <FontAwesomeIcon size="lg" icon="fa-regular fa-clock" />
            </template>

            <template #content="{ close }">
              <div class="scrollbar my-1 h-96 w-56 overflow-auto">
                <div
                  class="cursor-default px-3 pb-1.5 pt-2 hover:bg-accent"
                  :class="{
                    'bg-selected text-white hover:bg-selected':
                      time.timezone === timezone
                  }"
                  v-for="(time, idx) in timezoneList"
                  :key="time.id"
                  @click="(close(), handleTimezone(idx))"
                >
                  {{ time.name }}
                </div>
              </div>
            </template>
          </Dropdown>

          <div>
            <PrimaryButton :disabled="isLoading" @click="handleUpNext"
              >Up Next</PrimaryButton
            >
          </div>

          <NavItem @click="resetDates">
            <FontAwesomeIcon size="lg" icon="fa-solid fa-rotate" />
          </NavItem>
        </div>

        <NavItem @click="rightNavDrawerStore.toggleEconomicCalendar(false)">
          <FontAwesomeIcon size="xl" icon="fa-solid fa-xmark" />
        </NavItem>
      </div>
    </div>

    <div
      class="h-full w-full items-center justify-center flex"
      v-if="isLoading"
    >
      <Spinner class="!size-14" />
    </div>

    <div
      v-else
      class="overflow-auto px-3"
      :style="{ height: height - 55 + 'px' }"
    >
      <table class="w-full text-left">
        <thead>
          <tr>
            <th class="bg-gray-50 px-2 pb-1.5 pt-2 font-semibold">Date</th>
            <th class="bg-gray-50 px-2 pb-1.5 pt-2 font-semibold">Time</th>
            <th class="bg-gray-50 px-2 pb-1.5 pt-2 font-semibold">Currency</th>
            <th class="bg-gray-50 px-2 pb-1.5 pt-2 font-semibold">Country</th>
            <th class="bg-gray-50 px-2 pb-1.5 pt-2 font-semibold">Impact</th>
            <th class="bg-gray-50 px-2 pb-1.5 pt-2 font-semibold">
              Description
            </th>
            <th class="bg-gray-50 px-2 pb-1.5 pt-2 font-semibold">
              Actual Value
            </th>
            <th class="bg-gray-50 px-2 pb-1.5 pt-2 font-semibold">Forecast</th>
            <th class="bg-gray-50 px-2 pb-1.5 pt-2 font-semibold">Previous</th>
          </tr>
        </thead>

        <tbody>
          <tr
            class="even:bg-gray-50"
            v-for="(item, idx) in filteredNewsList"
            :key="idx"
          >
            <td class="p-2" v-if="!isYearMonthDaySame(idx)">
              <div>
                {{
                  DateTime.fromFormat(item.time, "yyyy.MM.dd HH:mm").toFormat(
                    "EEE"
                  )
                }}

                <br />

                {{
                  DateTime.fromFormat(item.time, "yyyy.MM.dd HH:mm").toFormat(
                    "MMM"
                  )
                }}

                {{
                  DateTime.fromFormat(item.time, "yyyy.MM.dd HH:mm").toFormat(
                    "dd"
                  )
                }}
              </div>
            </td>

            <td class="p-2" v-else></td>

            <td
              class="scroll-mt-[42px] p-2"
              :id="
                DateTime.fromFormat(item.time, 'yyyy.MM.dd HH:mm').toFormat(
                  'yyyy-MM-dd-HH-mm'
                )
              "
              v-if="
                !(
                  isYearMonthDaySame(idx) &&
                  filteredNewsList[idx - 1]?.time === item.time
                )
              "
            >
              {{
                DateTime.fromFormat(item.time, "yyyy.MM.dd HH:mm").toFormat(
                  "HH:mm"
                )
              }}
            </td>

            <td class="p-2" v-else></td>

            <td class="p-2">
              {{ item.currency }}
            </td>

            <td class="p-2">
              {{ item.country }}
            </td>

            <td class="p-2">
              <FontAwesomeIcon
                icon="fa-solid fa-folder"
                :title="handleImpactTitle(item.importance)"
                :class="handleImpactColor(item.importance)"
              />
            </td>

            <td class="p-2">
              {{ item.description }}
            </td>

            <td class="p-2">
              {{
                item.actual_value !== "nan"
                  ? Number(item.actual_value).toFixed(2)
                  : ""
              }}
            </td>

            <td class="p-2">
              {{
                item.forecast_value !== "nan"
                  ? Number(item.forecast_value).toFixed(2)
                  : ""
              }}
            </td>

            <td class="p-2">
              {{
                item.prev_value !== "nan"
                  ? Number(item.prev_value).toFixed(2)
                  : ""
              }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
table th {
  position: sticky;
  top: 0px;
}
</style>
