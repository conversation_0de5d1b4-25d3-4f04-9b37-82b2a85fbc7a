<script setup lang="ts">
import { DollarSign, Shield, Target } from "lucide-vue-next";

const rules = [
  {
    icon: Shield,
    title: "The Trading Challenge",
    description:
      "Participants enter a trading challenge where they must successfully pass an evaluation process. A fee is paid to enter the challenge."
  },
  {
    icon: DollarSign,
    title: "Access to Capital with No Personal Risk",
    description:
      "Once you pass the challenge, you'll move on to the funded stage, where you receive a profit split based on the returns you generate. Plus, you'll have the opportunity to scale your account."
  },
  {
    icon: Target,
    title: "General Rules",
    description:
      "To pass the challenge, you must generate 10% while not exceeding a loss of 10% in total or 5% in one day. Each firm has its own specific rules, and we’re here to help you navigate this process."
  }
];
</script>

<template>
  <div class="bg-gray-50 py-16">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="mx-auto max-w-4xl text-center">
        <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
          DONT HAVE CAPITAL TO TRADE?
        </h2>
        <p class="mt-4 text-lg leading-8 text-gray-600">
          LET US HELP YOU NAVIGATE THE WORLD OF PROPREITORY TRADING
        </p>
      </div>

      <div
        class="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 sm:gap-y-12 lg:mt-20 lg:max-w-none lg:grid-cols-3"
      >
        <div v-for="(rule, index) in rules" :key="index" class="flex flex-col">
          <div
            class="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-700 text-white"
          >
            <component :is="rule.icon" class="h-6 w-6" />
          </div>
          <h3 class="mt-6 text-lg font-semibold leading-8 text-gray-900">
            {{ rule.title }}
          </h3>
          <p class="mt-2 text-base leading-7 text-gray-600">
            {{ rule.description }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
