import { ILabelInfo } from "@/lib/night-vision/shapes/NewOrder";

import { useCandleStickStore } from "@/store/candleStickStore";

import {
  calculatePips,
  getRefPriceAndContractSize
} from "@/helpers/chartCalculation";

import { SymbolCalcMode } from "@/types/enums";

interface ICalculateSLParams {
  orderType?: "buy" | "sell";
  volume: number;
  price: number;
  sl: number;
}

interface ICalculateTPParams {
  orderType?: "buy" | "sell";
  price: number;
  volume: number;
  tp: number;
}

interface CalculateProfitLabelParams {
  orderType: "buy" | "sell";
  volume: number;
  symbol: string;
}

export function useLabelFunctions() {
  const candleStickStore = useCandleStickStore();

  const calculateSlLabels = (
    slLabel: ICalculateSLParams,
    marketWatchSymbol: string
  ) => {
    if (!candleStickStore.eaAccount) {
      throw new Error("EA Account not found");
    }

    const { price, sl, volume, orderType } = slLabel;

    const { refPrice, contractSize } = getRefPriceAndContractSize(
      marketWatchSymbol,
      candleStickStore.marketWatchSymbolList,
      candleStickStore.eaAccount.currency
    );

    let expLoss = 1;

    if (orderType === "sell") {
      expLoss = ((price - sl) * (contractSize * volume)) / refPrice;
    } else if (orderType === "buy") {
      expLoss = ((sl - price) * (contractSize * volume)) / refPrice;
    }

    const labelInfo: ILabelInfo = {};

    const balance = candleStickStore.eaAccount.balance;

    labelInfo.profit = expLoss;
    labelInfo.profitPercentage = (expLoss / balance) * 100;

    labelInfo.pip = calculatePips(price, sl);

    return labelInfo;
  };

  const calculateTpLabels = (
    tpLabel: ICalculateTPParams,
    marketWatchSymbol: string
  ) => {
    if (!candleStickStore.eaAccount) {
      throw new Error("EA Account not found");
    }

    const { price, tp, volume, orderType } = tpLabel;

    const labelInfo: ILabelInfo = {};

    const balance = candleStickStore.eaAccount.balance;

    const { refPrice, contractSize } = getRefPriceAndContractSize(
      marketWatchSymbol,
      candleStickStore.marketWatchSymbolList,
      candleStickStore.eaAccount.currency
    );

    let expProfit = 1;

    if (orderType === "sell") {
      expProfit = ((price - tp) * (contractSize * volume)) / refPrice;
    } else {
      expProfit = ((tp - price) * (contractSize * volume)) / refPrice;
    }

    labelInfo.profit = expProfit;
    labelInfo.profitPercentage = (expProfit / balance) * 100;

    labelInfo.pip = calculatePips(price, tp);

    return labelInfo;
  };

  const calculatePriceLabels = ({
    symbol,
    volume,
    orderType
  }: CalculateProfitLabelParams) => {
    if (!candleStickStore.eaAccount) {
      throw new Error("EA Account not found");
    }

    let margin = 1;

    const item = candleStickStore.marketWatchSymbolList.find(
      (v) => v.symbol === symbol
    );

    const labelInfo: ILabelInfo = {};

    if (!item) {
      labelInfo.margin = margin;

      return labelInfo;
    }

    let marginCurrency = item.symbol_margin_currency;
    const accountCurrency = candleStickStore.eaAccount.currency;
    const leverage = candleStickStore.eaAccount.leverage;

    let refMarginCurrency = 1;
    let contractSize = 1;

    // For PU Prime Broker
    if (item.symbol_margin_currency === "BTC") {
      marginCurrency = "USD";
    }

    if (marginCurrency === accountCurrency) {
      refMarginCurrency = 1;
    } else {
      const temp = candleStickStore.marketWatchSymbolList.find(
        (v) =>
          v.symbol_margin_currency === marginCurrency &&
          v.symbol_profit_currency === accountCurrency
      );

      if (temp) {
        refMarginCurrency = temp.bid;
      } else {
        const temp = candleStickStore.marketWatchSymbolList.find(
          (v) =>
            v.symbol_margin_currency === accountCurrency &&
            v.symbol_profit_currency === marginCurrency
        )!;

        refMarginCurrency = 1 / temp.bid;
      }
    }

    contractSize = parseInt(item.symbol_contract_size);

    let marginRate = 1;
    let marketPrice = 1;

    if (orderType === "sell") {
      marketPrice = item.bid;
      marginRate = item.symbol_initial_margin_sell;
    } else if (orderType === "buy") {
      marketPrice = item.ask;
      marginRate = item.symbol_initial_margin_buy;
    }

    switch (item.symbol_trade_calc_mode) {
      case SymbolCalcMode.SYMBOL_CALC_MODE_FOREX:
        margin = (volume * contractSize) / (leverage * marginRate);
        break;
      case SymbolCalcMode.SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE:
        margin = volume * contractSize * marketPrice * marginRate;
        break;
      case SymbolCalcMode.SYMBOL_CALC_MODE_CFD:
        margin = volume * contractSize * marketPrice * marginRate;
        break;
      case SymbolCalcMode.SYMBOL_CALC_MODE_CFDINDEX:
        margin =
          volume *
          contractSize *
          marketPrice *
          (item.symbol_tick_value / (item.symbol_tick_size * (1 / marginRate)));
        break;
      case SymbolCalcMode.SYMBOL_CALC_MODE_CFDLEVERAGE:
        margin =
          (volume * contractSize * marketPrice) / (leverage * (1 / marginRate));
        break;
      default:
        margin = 1;
    }

    margin = margin * refMarginCurrency;

    let marginPercentage;

    if (candleStickStore.eaAccount && candleStickStore.eaAccount.balance) {
      marginPercentage = (margin / candleStickStore.eaAccount.balance) * 100;
    }

    candleStickStore.refMarginCurrency = refMarginCurrency;

    labelInfo.margin = margin;
    labelInfo.marginPercentage = marginPercentage;

    return labelInfo;
  };

  return {
    calculateSlLabels,
    calculateTpLabels,
    calculatePriceLabels
  };
}
