import { TCoreData } from "../types";
import { HLine } from "./HLine";
import { ILabelInfo } from "./NewOrder";
import { DistanceUtils } from "./utils/DistanceUtils";

const colors = {
  TP: "#039e55e6",
  SL: "#ff3d3de6",
  PRICE: "#1e5bebe6",
  TP1: "#787878e6"
};

interface PriceHLineOptions {
  onValueChange: (number: number, labelInfo?: ILabelInfo) => void;
  onDragEnd: () => void;
  minValue?: number | null;
  maxValue?: number | null;
  draggable?: boolean;
  labelDeicmalPoints: number;
  labelInfo?: ILabelInfo;
  ratioLabel?: string;
}

export class PriceHLine extends HLine {
  private labelBoundingBox: {
    x: number;
    y: number;
    width: number;
    height: number;
  } | null = null;
  startX: number = 0;
  minValue: number | null = null;
  maxValue: number | null = null;
  draggable: boolean = true;
  wasDragging: boolean = false;
  onValueChange: (number: number, labelInfo?: ILabelInfo) => void;
  onDragEnd: (price: number) => void;
  labelDecimalPoints: number = 2;
  ratioLabel?: string;
  labelInfo?: ILabelInfo;

  constructor(
    $core: TCoreData,
    uuid: string,
    points: { x: number; y: number }[],
    screenPoints: boolean,
    {
      draggable = true,
      labelDeicmalPoints = 2,
      maxValue = null,
      minValue = null,
      onDragEnd,
      onValueChange,
      labelInfo,
      ratioLabel
    }: PriceHLineOptions
  ) {
    super($core, uuid, points, screenPoints);
    this.onValueChange = onValueChange;
    this.onDragEnd = onDragEnd;
    this.minValue = minValue;
    this.maxValue = maxValue;
    this.draggable = draggable;
    this.labelDecimalPoints = labelDeicmalPoints;
    this.labelInfo = labelInfo;
    this.ratioLabel = ratioLabel;
  }

  setStartX(x: number) {
    this.startX = x;
  }

  setMaxValue(x: number) {
    this.maxValue = x;
  }

  setMinValue(x: number) {
    this.minValue = x;
  }

  isWithinLineArea(x: number): boolean {
    const endX = this.$core.layout.width;
    return x >= this.startX && x <= endX;
  }

  private isWithinDraggableArea(x: number, y: number): boolean {
    const lineY = this.points[0].screenY;
    const lineHitBox = 5;
    const isOnLine =
      y >= lineY - lineHitBox && y <= lineY + lineHitBox && x >= this.startX;

    if (this.labelBoundingBox) {
      const isOnLabel =
        x >= this.labelBoundingBox.x &&
        x <= this.labelBoundingBox.x + this.labelBoundingBox.width &&
        y >= this.labelBoundingBox.y &&
        y <= this.labelBoundingBox.y + this.labelBoundingBox.height;
      return isOnLabel || isOnLabel;
    }

    return isOnLine;
  }

  draw(ctx: CanvasRenderingContext2D): void {
    const endX = this.$core.layout.width;
    const y = this.points[0].screenY;

    if (this.points[0].y === 0) {
      return;
    }

    const visibleStartX = this.$core.layout.time2x(this.$core.range[0]);

    ctx.beginPath();
    ctx.moveTo(this.startX, y);
    ctx.lineTo(endX, y);
    ctx.strokeStyle = this.properties.lineProperties.line_color;
    ctx.lineWidth = this.properties.lineProperties.line_width;
    ctx.stroke();

    ctx.fillStyle = this.properties.lineProperties.line_color;

    const normalFont = "14px Arial";
    const boldFont = "bold 14px Arial";

    const labelParts = [];

    labelParts.push({ text: this.name, bold: true });
    // labelParts.push({
    //   text: `: ${this.points[0].y.toFixed(this.labelDecimalPoints)}`,
    //   bold: false
    // })

    if (this.labelInfo?.profit && this.labelInfo.profit !== Infinity) {
      labelParts.push({
        text: ` — `,
        bold: false
      });
      labelParts.push({
        text: "PROFIT",
        bold: true
      });
      labelParts.push({
        text: `: ${this.labelInfo?.profit.toFixed(2)}`,
        bold: false
      });

      if (this.labelInfo.profitPercentage) {
        labelParts.push({
          text: `, `,
          bold: false
        });
        labelParts.push({
          text: "% of Bal",
          bold: true
        });
        labelParts.push({
          text: `: ${this.labelInfo.profitPercentage.toFixed(2)}%`,
          bold: false
        });
      }
    }

    if (this.labelInfo?.pip) {
      labelParts.push({
        text: `, `,
        bold: false
      });
      labelParts.push({
        text: "PIP",
        bold: true
      });
      labelParts.push({
        text: `: ${this.labelInfo?.pip.toFixed(2)}`,
        bold: false
      });
    }

    if (this.ratioLabel) {
      labelParts.push({
        text: ` — `,
        bold: false
      });
      labelParts.push({
        text: "R:R: ",
        bold: true
      });
      labelParts.push({
        text: this.ratioLabel,
        bold: false
      });
    }

    if (this.labelInfo?.margin) {
      labelParts.push({
        text: `, `,
        bold: false
      });
      labelParts.push({
        text: "Margin",
        bold: true
      });
      labelParts.push({
        text: `: ${this.labelInfo?.margin.toFixed(2)}`,
        bold: false
      });

      if (this.labelInfo?.marginPercentage) {
        labelParts.push({
          text: `, `,
          bold: false
        });
        labelParts.push({
          text: "Margin %",
          bold: true
        });
        labelParts.push({
          text: `: ${this.labelInfo?.marginPercentage.toFixed(2)}%`,
          bold: false
        });
      }
    }

    if (this.labelInfo?.volume1) {
      labelParts.push({
        text: `, `,
        bold: false
      });
      labelParts.push({
        text: "Vol1: ",
        bold: true
      });
      labelParts.push({
        text: this.labelInfo.volume1.toString(),
        bold: false
      });
    }

    let labelLength = 0;

    labelParts.forEach((part) => {
      ctx.font = normalFont;
      labelLength += ctx.measureText(part.text).width;
    });

    let labelX = this.startX + 10;
    // if (this.startX < visibleStartX) {
    labelX = this.$core.props.width - labelLength - 180;
    // }

    let labelWidth = 0;
    labelParts.forEach((part) => {
      ctx.font = part.bold ? boldFont : normalFont;
      const { width } = ctx.measureText(part.text);
      labelWidth += width;
    });

    ctx.fillStyle = colors[this.name];
    ctx.roundRect(labelX - 5, y, labelWidth + 10, -24, 2);
    ctx.fill();

    let currentX = labelX;
    ctx.fillStyle = "white";
    labelParts.forEach((part) => {
      ctx.font = part.bold ? boldFont : normalFont;
      ctx.fillText(part.text, currentX, y - 5);
      const { width } = ctx.measureText(part.text);
      currentX += width;
      labelWidth += width;
    });

    this.labelBoundingBox = {
      x: labelX,
      y: y - 20,
      width: labelLength,
      height: 25
    };
  }

  mousedown(event: MouseEvent): void {
    if (this.points[0].y === 0) {
      return;
    }

    if (!this.isWithinLineArea(event.offsetX)) {
      return;
    }

    this.selected = true;

    if (this.dragging && this.draggable) {
      this.wasDragging = true;
    }

    const cursor = { x: event.offsetX, y: event.offsetY };

    if (
      this.isWithinDraggableArea(event.offsetX, event.offsetY) ||
      DistanceUtils.isCursorOnLine(
        this.screenPoints[0],
        this.screenPoints[1],
        cursor
      )
    ) {
      this.onSelect(this);
      this.dragging = true;
      this.draggingPoint = { x: this.$core.cursor.x, y: this.$core.cursor.y };
      this.$core.hub.events.emit("scroll-lock", true);
    } else {
      this.dragging = false;
      this.draggingPoint = null;
    }

    this.$core.hub.events.emit("update-layout");
  }

  mouseup(event: MouseEvent): void {
    if (this.points[0].y === 0) {
      return;
    }

    this.selected = false;
    super.mouseup(event);

    if (this.wasDragging) {
      this.onDragEnd(this.points[0].y);
      this.dragging = false;
    }

    this.wasDragging = false;
  }

  mousemove(event: MouseEvent): void {
    if (this.points[0].y === 0) {
      return;
    }

    if (!this.draggable) {
      return;
    }

    super.mousemove(event);
    if (this.dragging) {
      let newValue = this.points[0].y;
      if (this.minValue !== null) {
        newValue = Math.max(newValue, this.minValue);
      }
      if (this.maxValue !== null) {
        newValue = Math.min(newValue, this.maxValue);
      }
      this.points[0].y = newValue;

      this.onValueChange(this.points[0].y);
    }
  }

  mouseover(event: MouseEvent): void {
    if (this.points[0].y === 0) {
      return;
    }

    if (!this.isWithinLineArea(event.offsetX)) {
      this.hovered = false;
      return;
    }

    if (
      this.cursorOverLine(event) ||
      this.isWithinDraggableArea(event.offsetX, event.offsetY)
    ) {
      this.hovered = true;
    } else {
      this.hovered = false;
    }
  }

  updatePosition(y: number, labelInfo?: ILabelInfo): void {
    this.points[0].y = y;

    if (labelInfo) {
      this.labelInfo = labelInfo;
    }

    this.$core.hub.events.emit("update-layout");
  }

  updateRatioLabel(ratio: string) {
    this.ratioLabel = ratio;
    this.$core.hub.events.emit("update-layout");
  }
}
