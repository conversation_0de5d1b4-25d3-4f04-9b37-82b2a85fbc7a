<script setup lang="ts">
import { ref } from "vue";

import ProfileHeader from "./ProfileHeader.vue";

import Alert from "@/components/Alert.vue";
import Select from "@/components/Select.vue";
import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const currentPhonenNumber = ref("+01-*********");
const countryCode = ref("");
const phoneNumber = ref("");
</script>

<template>
  <ProfileHeader text="Change Phone Number" route-name="edit" />

  <div class="px-5">
    <Alert variant="info">
      Your current phone number is
      <span class="font-semibold"> {{ currentPhonenNumber }} </span>. We use
      your phone number to verify your identity, send SMS code and alert for
      account related activities, to customize your trading experience, etc.
    </Alert>

    <div class="mt-5">
      <InputLabel for="country_code">Country</InputLabel>

      <Select id="country_code" name="country_code" v-model="countryCode">
        <option value="canada">+1 Canada</option>
        <option value="america">+1 United States</option>
        <option value="mexico">+52 Mexico</option>
        <option value="uk">+44 United Kingdom</option>
        <option value="australia">+61 Australia</option>
      </Select>
    </div>

    <div class="mt-5">
      <InputLabel for="phone_number">Phone Number</InputLabel>

      <InputText id="phone_number" v-model="phoneNumber" />
    </div>

    <div class="mt-5">
      <PrimaryButton class="w-full">Save Changes</PrimaryButton>
    </div>
  </div>
</template>
