import { library } from "@fortawesome/fontawesome-svg-core";
import {
  faPen,
  faKey,
  faStar,
  faUser,
  faLock,
  faPlus,
  faBars,
  faPlay,
  faMinus,
  faXmark,
  faCheck,
  faFolder,
  faRotate,
  faUnlock,
  faArrowUp,
  faDownload,
  faComputer,
  faEnvelope,
  faEllipsis,
  faChevronUp,
  faArrowDown,
  faArrowLeft,
  faArrowRight,
  faCircleInfo,
  faChevronDown,
  faChevronLeft,
  faChevronRight,
  faGripVertical,
  faMagnifyingGlass
} from "@fortawesome/free-solid-svg-icons";
import {
  faEye,
  faCopy,
  faBell,
  faClock,
  faEyeSlash,
  faCalendar,
  faPaperPlane,
  faWindowRestore,
  faFaceFrownOpen,
  faStar as faRegularStar
} from "@fortawesome/free-regular-svg-icons";
import { faYoutube, faFacebook } from "@fortawesome/free-brands-svg-icons";

const icons = [
  faEye,
  faPen,
  faKey,
  faStar,
  faCopy,
  faUser,
  faBell,
  faPlay,
  faLock,
  faPlus,
  faBars,
  faMinus,
  faXmark,
  faCheck,
  faClock,
  faFolder,
  faRotate,
  faUnlock,
  faArrowUp,
  faYoutube,
  faComputer,
  faEyeSlash,
  faFacebook,
  faDownload,
  faEnvelope,
  faCalendar,
  faEllipsis,
  faChevronUp,
  faArrowDown,
  faArrowLeft,
  faArrowRight,
  faPaperPlane,
  faCircleInfo,
  faChevronDown,
  faRegularStar,
  faChevronLeft,
  faChevronRight,
  faGripVertical,
  faWindowRestore,
  faFaceFrownOpen,
  faMagnifyingGlass
];

library.add(icons);
