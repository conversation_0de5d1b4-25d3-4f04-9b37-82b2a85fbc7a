{"name": "trading-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc && vite build", "build-no-ts-check": "vite build", "preview": "vite preview", "test": "vitest", "format": "prettier src/ --write", "lint": "eslint", "cypress:open": "cypress open", "cypress:run": "cypress run"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.6.0", "@fortawesome/free-brands-svg-icons": "^6.6.0", "@fortawesome/free-regular-svg-icons": "^6.6.0", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/vue-fontawesome": "^3.0.8", "@pipclimber/night-vision": "^0.4.0-pipclimber.*******.2", "@stripe/stripe-js": "^5.3.0", "@vueuse/core": "^10.7.0", "apexcharts": "^4.0.0", "axios": "^1.6.2", "flowbite": "^2.2.0", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.475.0", "luxon": "^3.5.0", "pinia": "^2.1.7", "socket.io-client": "^4.7.5", "vue": "^3.3.8", "vue-router": "^4.2.5", "vue3-avatar": "^3.1.0", "vue3-toastify": "^0.1.14", "yup": "^1.3.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tailwindcss/forms": "^0.5.7", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/lodash-es": "^4.17.12", "@types/luxon": "^3.4.2", "@types/node": "^20.9.4", "@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.16", "cypress": "^14.3.3", "eslint": "^9.21.0", "eslint-plugin-cypress": "^4.3.0", "eslint-plugin-vue": "^9.32.0", "globals": "^16.0.0", "path": "^0.12.7", "postcss": "^8.4.31", "prettier": "^3.5.1", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "typescript-eslint": "^8.24.1", "vite": "^5.0.0", "vite-svg-loader": "^5.1.0", "vitest": "^1.1.3", "vue-tsc": "^1.8.27"}}