import { test, expect } from "vitest";
import {
  getFromLocalStorage,
  storeToLocalStorage
} from "@/helpers/localStorage";

test("Test for local storage", () => {
  const name = "<PERSON>";

  storeToLocalStorage("name", name);

  expect(getFromLocalStorage("name")).toBe("<PERSON>");

  const person = {
    name: "<PERSON>",
    age: 26,
    address: "New York"
  };

  storeToLocalStorage("person", person, true);

  expect(getFromLocalStorage("person", true)).toStrictEqual({
    name: "<PERSON>",
    age: 26,
    address: "New York"
  });
});
