<script setup lang="ts">
import { useBottomNavDrawerStore } from "@/store/bottomNavDrawerStore";
import { useChartStore } from "@/store/chartStore";
import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";

import NavItem from "@/components/NavItem.vue";

import { ChartInstanceError } from "@/helpers/errors";

import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const chartStore = useChartStore();
const rightNavDrawerStore = useRightNavDrawerStore();
const bottomNavDrawerStore = useBottomNavDrawerStore();

function openBottomNavContentArea() {
  if (!chartStore.chart) {
    throw new ChartInstanceError("Cannot open bottomNavContentArea.");
  }

  chartStore.chartHeight -= 250;

  rightNavDrawerStore.rightNavContentAreaHeight -= 250;

  bottomNavDrawerStore.bottomNavContentAreaHeight = 296;

  bottomNavDrawerStore.toggleBottomNavContentArea(true);
}
</script>

<template>
  <div id="bottom-nav-drawer" class="flex justify-end border-t-4">
    <NavItem
      class="flex items-center gap-x-1.5 rounded-none pb-2.5 pt-3"
      @click="openBottomNavContentArea"
    >
      Trading Panel
      <FontAwesomeIcon icon="fa-solid fa-chevron-up" />
    </NavItem>
  </div>
</template>
