import { AvailableShapes } from "../../groups/ShapeTool";
import { IBaseShape } from "../shapes/BaseShape";
import { PreferenceUtils } from "../utils/PreferencesUtils";

export interface BaseProperties<X extends AvailableShapes> {
  properties: (typeof PreferenceUtils)[X];
  setProperty: <
    Y extends keyof (typeof PreferenceUtils)[X],
    Z extends keyof (typeof PreferenceUtils)[X][Y]
  >(
    baseKey: Y,
    subKey: Z,
    value: (typeof PreferenceUtils)[X][Y][Z]
  ) => void;
}

export type BaseShapeInterface<X extends AvailableShapes> = IBaseShape &
  BaseProperties<X>;
