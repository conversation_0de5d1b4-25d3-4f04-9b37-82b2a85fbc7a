import { Appearance, StripeElementClasses } from "@stripe/stripe-js";

export const STRIPE_APPEARANCE: Appearance = {
  variables: {
    fontFamily: "Open Sans, sans-serif",
    colorBackground: "#f3f4f6",
    spacingUnit: "4px",
    borderRadius: "8px"
  },
  rules: {
    ".Label": {
      marginBottom: "6px"
    },
    ".Input": {
      fontSize: "14px",
      transition: "none",
      marginBottom: "4px"
    },
    ".Input:focus": {
      boxShadow: "0 0 0 1px rgba(59, 130, 246, 1)"
    },
    ".Input--invalid": {
      boxShadow: "none"
    },
    ".Input--invalid:focus": {
      borderColor: "#e10000",
      boxShadow: "0 0 0 0.8px rgba(255, 0, 0, 1)"
    },
    ".Error": {
      color: "#e10000",
      padding: "0 2px",
      fontSize: "12px",
      marginTop: "6px"
    }
  }
};

export const STRIPE_CLASSES: StripeElementClasses = {
  focus: "!border-blue-500 ring-1 ring-blue-500",
  invalid: "!border-danger !ring-red-500"
};
