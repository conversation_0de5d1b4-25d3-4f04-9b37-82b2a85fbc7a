<script setup lang="ts">
import { onMounted, ref } from "vue";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";
import { useChartStore } from "@/store/chartStore";

import ColorPicker from "@/components/ColorPicker.vue";
import DropdownTooltip from "@/components/DropdownTooltip.vue";

import FormatTextSVG from "@/assets/svg/format-text.svg";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"text"> | undefined =
  chartStore.selectedShape;

const fontColorDropdown = ref(false);
const selectedFontColor = ref("");

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set shape text color");
  }

  selectedFontColor.value = selectedShape.properties.textProperties.font_color;
});

function handleFontColor(fontColor: string) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to change shape text color");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to change shape text color");
  }

  selectedFontColor.value = fontColor;

  selectedShape.setProperty(
    "textProperties",
    "font_color",
    selectedFontColor.value
  );

  chartStore.chart.update();
}
</script>

<template>
  <DropdownTooltip
    dropdown-id="shape-toolbar-text-font-color-dropdown"
    dropdown-toggle-id="shape-toolbar-text-font-color-toggle-dropdown"
    tooltip-id="shape-toolbar-text-font-color-tooltip"
    tooltip-trigger-id="shape-toolbar-text-font-color-trigger-tooltip"
    class="flex h-[40px] items-center rounded px-2 text-sm hover:bg-accent"
    :class="{ 'bg-accent': fontColorDropdown }"
    :dropdown-offset-distance="2"
    :dropdown-offset-skidding="110"
    @show="fontColorDropdown = true"
    @hide="fontColorDropdown = false"
  >
    <template #text>
      <div>
        <FormatTextSVG />

        <div
          class="mt-0.5 h-1 w-5"
          :style="{ backgroundColor: selectedFontColor }"
        ></div>
      </div>
    </template>

    <template #dropdown-content>
      <ColorPicker :color="selectedFontColor" @update-color="handleFontColor" />
    </template>

    <template #tooltip-content>Color</template>
  </DropdownTooltip>
</template>
