import { Line } from "./LineTool";

interface PointData {
  x: number;
  y: number;
}

export class Utils {
  static length_squared(
    v: { x: number; y: number },
    w: { x: number; y: number }
  ): number {
    return Math.pow(w.x - v.x, 2) + Math.pow(w.y - v.y, 2); // Squared Euclidean distance
  }

  static distance(
    a: { x: number; y: number },
    b: { x: number; y: number }
  ): number {
    return Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2)); // Euclidean distance
  }
  static dotProduct(e1x: number, e1y: number, e2x: number, e2y: number) {
    return e1x * e2x + e1y * e2y;
  }
  static projection(line: Line, p: PointData) {
    const v = line.points[0].screen;
    const w = line.points[1].screen;
    const l2 = Utils.length_squared(v, w); // Square of the length of the line segment
    if (l2 === 0) return { t: 0.5, projection: v }; // If the segment is just a point, return distance to that point
    // Calculate the projection of point p onto the line segment vw
    const t = Math.max(
      0,
      Math.min(
        1,
        Utils.dotProduct(p.x - v.x, p.y - v.y, w.x - v.x, w.y - v.y) / l2
      )
    );
    // Calculate the coordinates of the projected point
    const projection = { x: v.x + t * (w.x - v.x), y: v.y + t * (w.y - v.y) };

    return { t, projection };
  }
}
