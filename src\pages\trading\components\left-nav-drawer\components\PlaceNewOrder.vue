<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from "vue";

import { DateTime } from "luxon";
import { toast } from "vue3-toastify";

import { ILabelInfo, OrderData } from "@/lib/night-vision/shapes/NewOrder";

import { useCandleStickStore } from "@/store/candleStickStore";
import { useChartStore } from "@/store/chartStore";
import { useLeftNavDrawerStore } from "@/store/leftNavDrawerStore";
import { useTradeShapeStore } from "@/store/tradeShapeStore";
import { useUserStore } from "@/store/userStore";

import { useLabelFunctions } from "@/chart-frontend/labelUtils";

import Checkbox from "@/components/Checkbox.vue";
import InputLabel from "@/components/InputLabel.vue";
import InputText from "@/components/InputText.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";
import Select from "@/components/Select.vue";

import { useDragElement } from "@/composables/useDragElement";

import {
  calculatePips,
  getRefPriceAndContractSize
} from "@/helpers/chartCalculation";
import { getUTCOffset } from "@/helpers/dateConversion";
import { countDecimals } from "@/helpers/numberUtils";

import { HOUR, MINUTE } from "@/utilities/constants";

import { EOrderType, EOrderTypeTime, ETradeOperationType } from "@/types/enums";

import { eaSocket } from "@/socketio";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const userStore = useUserStore();
const chartStore = useChartStore();
const tradeShapeStore = useTradeShapeStore();
const candleStickStore = useCandleStickStore();
const leftNavDrawerStore = useLeftNavDrawerStore();

const { top: posTop, left: posLeft } = useDragElement(
  "place-new-order-toolbar",
  "place-new-order-toolbar-header"
);

const { calculateSlLabels, calculateTpLabels, calculatePriceLabels } =
  useLabelFunctions();

const sellBtn = ref(true);
const buyBtn = ref(false);
const instantBtn = ref(true);
const orderBtn = ref(false);

const tradeAction = ref(ETradeOperationType.TRADE_ACTION_DEAL);
const tradeType = ref(EOrderType.ORDER_TYPE_SELL);

const placeOrder = ref({
  price: candleStickStore.bidPrice,
  deviation: 0,
  sl: 1,
  tp: 1,
  volume: 1,
  comment: "Comment",
  type_time: EOrderTypeTime.ORDER_TIME_GTC,
  expiration: ""
});

const priceStep = ref(1);
const slStep = ref(1);
const slPip = ref(20);
const tpStep = ref(1);
const tpPip = ref(20);
const autoVolume = ref(true);
const tp1VolPercent = ref(50);

const timeTypeList = ref([
  {
    id: EOrderTypeTime.ORDER_TIME_GTC,
    name: "GTC"
  },
  {
    id: EOrderTypeTime.ORDER_TIME_DAY,
    name: "Today"
  },
  {
    id: EOrderTypeTime.ORDER_TIME_SPECIFIED,
    name: "Specified"
  },
  {
    id: EOrderTypeTime.ORDER_TIME_RELATIVE,
    name: "Relative"
  }
]);
const expirationHour = ref("12");
const expirationMinute = ref("5");

const currentTimestamp = ref(Date.now());

// for relative time (EOrderTypeTime.ORDER_TIME_RELATIVE)
const expirationTime = computed(
  () =>
    currentTimestamp.value +
    parseInt(expirationHour.value) * HOUR +
    parseInt(expirationMinute.value) * MINUTE
);

// convert timezone followed by string format
const expirationTimeString = computed(() =>
  DateTime.fromMillis(expirationTime.value, {
    zone: candleStickStore.timezone
  }).toFormat("yyyy-MM-dd, HH:mm:ss")
);

// For Pip Calculation
const specialSymbols = [
  "NDX100",
  "SPX500",
  "US30",
  "GER40",
  "UK100",
  "XAUUSD",
  "BTCUSD",
  "GER40Cash",
  "US500Cash",
  "UK100Cash"
];

let timer: ReturnType<typeof setInterval> | null;

onMounted(() => {
  const priceDecimalCount = countDecimals(placeOrder.value.price);

  priceStep.value = 1 / Math.pow(10, priceDecimalCount);

  calculateInitialSLAndTP();

  const slDecimalCount = countDecimals(placeOrder.value.sl);
  const tpDecimalCount = countDecimals(placeOrder.value.tp);

  slStep.value = 1 / Math.pow(10, slDecimalCount);
  tpStep.value = 1 / Math.pow(10, tpDecimalCount);

  calculateVolumeFromAuto();

  candleStickStore.updateTP1 = false;
  candleStickStore.updateSLTrial = false;

  // Chart
  if (!chartStore.shapeToolInstance) {
    throw new Error("Shape tool instance not found");
  }

  const orderData: OrderData = {
    price: placeOrder.value.price,
    stopLoss: placeOrder.value.sl,
    takeProfit: placeOrder.value.tp
  };

  tradeShapeStore.newOrderShape = chartStore.shapeToolInstance.addOrder(
    orderData,
    sellBtn.value ? "sell" : "buy",
    instantBtn.value ? "instant" : "order"
  );

  chartStore.shapeToolInstance.addListener(
    "placeneworder",
    "update-price-value",
    (value: number) => {
      updateOrdersFromChart("price", value);
    }
  );

  chartStore.shapeToolInstance.addListener(
    "placeneworder",
    "update-sl-value",
    (value: number) => {
      updateOrdersFromChart("sl", value);
    }
  );

  chartStore.shapeToolInstance.addListener(
    "placeneworder",
    "update-tp-value",
    (value: number) => {
      updateOrdersFromChart("tp", value);
    }
  );

  chartStore.shapeToolInstance.addListener(
    "placeneworder",
    "update-tp1-value",
    (value: number) => {
      updateOrdersFromChart("tp1", value);
    }
  );

  timer = setInterval(() => {
    currentTimestamp.value = Date.now();
  }, 1000);
});

onUnmounted(() => {
  tradeShapeStore.removeOrderShape();

  if (timer) {
    clearInterval(timer);
  }
});

watch([posTop, posLeft], ([topPos, leftPos]) => {
  leftNavDrawerStore.setPlaceNewOrderModalPosition(topPos, leftPos);
});

watch([tradeAction, tradeType], () => {
  resetValues();
});

watch(
  () => candleStickStore.bidPrice,
  (newBidPrice) => {
    if (
      tradeAction.value === ETradeOperationType.TRADE_ACTION_DEAL &&
      tradeType.value === EOrderType.ORDER_TYPE_SELL
    ) {
      placeOrder.value.price = newBidPrice;

      calculatePipFromSL();
      calculatePipFromTP();

      if (autoVolume.value) {
        calculateVolumeFromAuto();
      }

      const priceLabel: ILabelInfo = calculatePriceLabels({
        symbol: candleStickStore.marketWatchSymbol,
        volume: placeOrder.value.volume,
        orderType: sellBtn.value ? "sell" : "buy"
      });

      tradeShapeStore.newOrderShape?.updatePrice(newBidPrice, priceLabel);
    }
  }
);

watch(
  () => candleStickStore.askPrice,
  (newAskPrice) => {
    if (
      tradeAction.value === ETradeOperationType.TRADE_ACTION_DEAL &&
      tradeType.value === EOrderType.ORDER_TYPE_BUY
    ) {
      placeOrder.value.price = newAskPrice;

      calculatePipFromSL();
      calculatePipFromTP();

      if (autoVolume.value) {
        calculateVolumeFromAuto();
      }

      tradeShapeStore.newOrderShape?.updatePrice(newAskPrice);

      const slLabel: ILabelInfo = calculateSlLabels(
        {
          orderType: sellBtn.value ? "sell" : "buy",
          price: newAskPrice,
          sl: placeOrder.value.sl,
          volume: placeOrder.value.volume
        },
        candleStickStore.marketWatchSymbol
      );

      let volume = placeOrder.value.volume;

      /*
        Volume = Total Volume - TP1 Vol
               = 1.65 - 0.8
               = 0.85 (For TP volume)
      */
      if (candleStickStore.tp1_vol) {
        volume -= candleStickStore.tp1_vol;
      }

      const tpLabel: ILabelInfo = calculateTpLabels(
        {
          orderType: sellBtn.value ? "sell" : "buy",
          price: newAskPrice,
          tp: placeOrder.value.tp,
          volume
        },
        candleStickStore.marketWatchSymbol
      );

      const tp1Label: ILabelInfo = calculateTpLabels(
        {
          orderType: sellBtn.value ? "sell" : "buy",
          price: newAskPrice,
          tp: candleStickStore.tp1,
          volume: candleStickStore.tp1_vol
        },
        candleStickStore.marketWatchSymbol
      );

      tradeShapeStore.newOrderShape?.updateStopLoss(
        placeOrder.value.sl,
        slLabel
      );

      tradeShapeStore.newOrderShape?.updateTakeProfit(
        placeOrder.value.tp,
        tpLabel
      );

      tradeShapeStore.newOrderShape?.updateTakeProfit1(candleStickStore.tp1, {
        ...tp1Label,
        volume1: candleStickStore.tp1_vol
      });
    }
  }
);

watch(
  () => [placeOrder.value.sl, placeOrder.value.volume],
  ([newSl, newVolume]) => {
    calculateTP1Vol();

    const labelInfo: ILabelInfo = calculateSlLabels(
      {
        orderType: sellBtn.value ? "sell" : "buy",
        price: placeOrder.value.price,
        sl: newSl,
        volume: newVolume
      },
      candleStickStore.marketWatchSymbol
    );

    tradeShapeStore.newOrderShape?.updateStopLoss(newSl, labelInfo);
  }
);

watch(
  () => [
    placeOrder.value.tp,
    placeOrder.value.volume,
    candleStickStore.tp1_vol
  ],
  ([newTp, newVolume, newTP1Vol]) => {
    let volume = newVolume;

    if (candleStickStore.tp1_vol) {
      volume -= newTP1Vol;
    }

    const labelInfo: ILabelInfo = calculateTpLabels(
      {
        orderType: sellBtn.value ? "sell" : "buy",
        price: placeOrder.value.price,
        tp: newTp,
        volume
      },
      candleStickStore.marketWatchSymbol
    );

    const priceLabel: ILabelInfo = calculatePriceLabels({
      symbol: candleStickStore.marketWatchSymbol,
      volume: placeOrder.value.volume,
      orderType: sellBtn.value ? "sell" : "buy"
    });

    tradeShapeStore.newOrderShape?.updateTakeProfit(newTp, labelInfo);

    tradeShapeStore.newOrderShape?.updatePrice(
      placeOrder.value.price,
      priceLabel
    );
  }
);

watch(
  () => [candleStickStore.tp1, candleStickStore.tp1_vol],
  ([newTP1, newTP1Vol]) => {
    const labelInfo: ILabelInfo = calculateTpLabels(
      {
        orderType: sellBtn.value ? "sell" : "buy",
        price: placeOrder.value.price,
        tp: newTP1,
        volume: newTP1Vol
      },
      candleStickStore.marketWatchSymbol
    );

    tradeShapeStore.newOrderShape?.updateTakeProfit1(newTP1, {
      ...labelInfo,
      volume1: newTP1Vol
    });
  }
);

function closeModal(type: string) {
  if (type === "minimize") {
    leftNavDrawerStore.togglePlaceNewOrderModal(true, true);
  } else if (type === "restore") {
    leftNavDrawerStore.togglePlaceNewOrderModal(true, false);
  } else {
    leftNavDrawerStore.togglePlaceNewOrderModal(false);
  }
}

function handleButtonSwitch(switchType: string) {
  if (switchType === "sell") {
    buyBtn.value = false;
    sellBtn.value = true;

    tradeType.value = EOrderType.ORDER_TYPE_SELL;

    resetValues();

    return;
  }

  if (switchType === "buy") {
    sellBtn.value = false;
    buyBtn.value = true;

    tradeType.value = EOrderType.ORDER_TYPE_BUY;

    resetValues();

    return;
  }

  if (switchType === "instant") {
    orderBtn.value = false;
    instantBtn.value = true;

    tradeAction.value = ETradeOperationType.TRADE_ACTION_DEAL;

    resetValues();

    return;
  }

  // Order Trade
  instantBtn.value = false;
  orderBtn.value = true;

  tradeAction.value = ETradeOperationType.TRADE_ACTION_PENDING;

  resetValues();
}

function updateOrdersFromChart(
  type: "price" | "sl" | "tp" | "tp1",
  value: number
) {
  if (type === "price") {
    placeOrder.value.price = value;

    calculatePipFromSL();
    calculatePipFromTP();

    if (autoVolume.value) {
      calculateVolumeFromAuto();
    }
  } else if (type === "sl") {
    placeOrder.value.sl = value;
    calculatePipFromSL();

    if (autoVolume.value) {
      calculateVolumeFromAuto();
    }
  } else if (type === "tp") {
    placeOrder.value.tp = value;
    calculatePipFromTP();
  } else {
    candleStickStore.tp1 = value;
  }
}

function initializeDefaultPipsForSpecialSymbols() {
  const symbol = candleStickStore.symbol;

  if (specialSymbols.includes(symbol)) {
    const price = placeOrder.value.price;

    let pip = 0;

    if (price < 10000) {
      pip = 500;
    } else if (price >= 10000 && price < 50000) {
      pip = 1000;
    } else {
      pip = 10000;
    }

    slPip.value = pip;
    tpPip.value = pip;

    return;
  }

  slPip.value = 20;
  tpPip.value = 20;
}

function calculateInitialSLAndTP() {
  initializeDefaultPipsForSpecialSymbols();

  const price = placeOrder.value.price;

  const priceDecimalCount = countDecimals(price);

  const FACTOR = priceDecimalCount >= 4 ? 10000 : 100;

  const sign = tradeType.value === EOrderType.ORDER_TYPE_SELL ? 1 : -1;

  const sl = price + sign * (slPip.value / FACTOR);

  const tp = price - sign * (tpPip.value / FACTOR);

  placeOrder.value.sl = parseFloat(sl.toFixed(priceDecimalCount));

  placeOrder.value.tp = parseFloat(tp.toFixed(priceDecimalCount));
}

function calculateSLFromPip() {
  const { price } = placeOrder.value;

  const priceDecimalCount = countDecimals(price);

  const FACTOR = priceDecimalCount >= 4 ? 10000 : 100;

  const sign = tradeType.value === EOrderType.ORDER_TYPE_SELL ? 1 : -1;

  const sl = price + sign * (slPip.value / FACTOR);

  placeOrder.value.sl = parseFloat(sl.toFixed(priceDecimalCount));
}

function calculatePipFromSL() {
  const { price, sl } = placeOrder.value;

  slPip.value = calculatePips(price, sl);
}

function calculateTPFromPip() {
  const { price } = placeOrder.value;

  const priceDecimalCount = countDecimals(price);

  const FACTOR = priceDecimalCount >= 4 ? 10000 : 100;

  const sign = tradeType.value === EOrderType.ORDER_TYPE_SELL ? 1 : -1;

  const tp = price - sign * (tpPip.value / FACTOR);

  placeOrder.value.tp = parseFloat(tp.toFixed(priceDecimalCount));
}

function calculatePipFromTP() {
  const { price, tp } = placeOrder.value;

  tpPip.value = calculatePips(price, tp);
}

async function calculateVolumeFromAuto() {
  const eaAccount = candleStickStore.eaAccount;

  if (!eaAccount) {
    return;
  }

  const { balance, currency } = eaAccount;

  const { price, sl } = placeOrder.value;

  const percentBalance = (candleStickStore.auto / 100) * balance;

  const { refPrice, contractSize } = getRefPriceAndContractSize(
    candleStickStore.marketWatchSymbol,
    candleStickStore.marketWatchSymbolList,
    currency
  );

  const priceDecimalCount = countDecimals(price);

  const diff = parseFloat(Math.abs(price - sl).toFixed(priceDecimalCount));

  const volume = (percentBalance * refPrice) / (diff * contractSize);

  const volStepDecimalcount = countDecimals(candleStickStore.volumeStep);

  placeOrder.value.volume = parseFloat(volume.toFixed(volStepDecimalcount));
}

function calculateAutoFromVolume() {
  const eaAccount = candleStickStore.eaAccount;

  if (!eaAccount) {
    return;
  }

  const { balance, currency } = eaAccount;

  const { price, sl, volume } = placeOrder.value;

  const priceDecimalCount = countDecimals(price);

  const diff = parseFloat(Math.abs(price - sl).toFixed(priceDecimalCount));

  const { refPrice, contractSize } = getRefPriceAndContractSize(
    candleStickStore.marketWatchSymbol,
    candleStickStore.marketWatchSymbolList,
    currency
  );

  const percentBalance = (diff * contractSize * volume) / refPrice;

  const result = (percentBalance / balance) * 100;

  candleStickStore.auto = parseFloat(result.toFixed(2));
}

function calculateTP1Vol() {
  if (!candleStickStore.updateTP1) {
    return;
  }

  const volStepDecimalCount = countDecimals(candleStickStore.volumeStep);

  candleStickStore.tp1_vol = parseFloat(
    ((tp1VolPercent.value / 100) * placeOrder.value.volume).toFixed(
      volStepDecimalCount
    )
  );
}

function handleTimeType() {
  if (placeOrder.value.type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED) {
    placeOrder.value.expiration = DateTime.now().toFormat("yyyy-MM-dd");

    expirationHour.value = "12";

    scrollToDirection("bottom");
  }
}

function validationForInstantSell() {
  let errors: string[] = [];

  if (placeOrder.value.sl < placeOrder.value.price) {
    errors.push("SL must be greater than bid price.");
  }

  if (placeOrder.value.tp > placeOrder.value.price) {
    errors.push("TP must be less than bid price.");
  }

  if (placeOrder.value.volume < candleStickStore.minVolume) {
    errors.push(`Volume must be greater than ${candleStickStore.minVolume}.`);
  }

  if (placeOrder.value.volume > candleStickStore.maxVolume) {
    errors.push(`Volume must be smaller than ${candleStickStore.maxVolume}.`);
  }

  if (placeOrder.value.comment === "") {
    errors.push("Comment is required.");
  }

  if (candleStickStore.updateTP1) {
    if (
      candleStickStore.tp1 > placeOrder.value.price &&
      candleStickStore.tp1 < placeOrder.value.tp
    ) {
      errors.push("TP1 must be smaller than bid price and be greater than TP.");
    }

    if (candleStickStore.tp1_vol > placeOrder.value.volume) {
      errors.push("TP1 Vol must be smaller than or equal to volume.");
    }
  }

  return errors;
}

function validationForInstantBuy() {
  let errors: string[] = [];

  if (placeOrder.value.sl > placeOrder.value.price) {
    errors.push("SL must be less than ask price.");
  }

  if (placeOrder.value.tp < placeOrder.value.price) {
    errors.push("TP must be greater than ask price.");
  }

  if (placeOrder.value.volume < candleStickStore.minVolume) {
    errors.push(`Volume must be greater than ${candleStickStore.minVolume}.`);
  }

  if (placeOrder.value.volume > candleStickStore.maxVolume) {
    errors.push(`Volume must be smaller than ${candleStickStore.maxVolume}.`);
  }

  if (placeOrder.value.comment === "") {
    errors.push("Comment is required.");
  }

  if (candleStickStore.updateTP1) {
    if (
      candleStickStore.tp1 < placeOrder.value.price &&
      candleStickStore.tp1 > placeOrder.value.tp
    ) {
      errors.push("TP1 must be greater than ask price and less than TP.");
    }

    if (candleStickStore.tp1_vol > placeOrder.value.volume) {
      errors.push("Lot1 must be smaller than or equal to volume.");
    }
  }

  return errors;
}

function validationForPendingSell() {
  let errors: string[] = [];

  if (placeOrder.value.tp > placeOrder.value.price) {
    errors.push("TP must be smaller than price.");
  }

  if (placeOrder.value.sl < placeOrder.value.price) {
    errors.push("SL must be greater than price.");
  }

  if (placeOrder.value.volume < candleStickStore.minVolume) {
    errors.push(`Volume must be greater than ${candleStickStore.minVolume}.`);
  }

  if (placeOrder.value.volume > candleStickStore.maxVolume) {
    errors.push(`Volume must be smaller than ${candleStickStore.maxVolume}.`);
  }

  if (placeOrder.value.comment === "") {
    errors.push("Comment is required.");
  }

  return errors;
}

function validationForPendingBuy() {
  let errors: string[] = [];

  if (placeOrder.value.tp < placeOrder.value.price) {
    errors.push("TP must be greater than price.");
  }

  if (placeOrder.value.sl > placeOrder.value.price) {
    errors.push("SL must be less than than price.");
  }

  if (placeOrder.value.volume < candleStickStore.minVolume) {
    errors.push(`Volume must be greater than ${candleStickStore.minVolume}.`);
  }

  if (placeOrder.value.volume > candleStickStore.maxVolume) {
    errors.push(`Volume must be smaller than ${candleStickStore.maxVolume}.`);
  }

  if (placeOrder.value.comment === "") {
    errors.push("Comment is required.");
  }

  return errors;
}

function handleValidation() {
  if (tradeAction.value === ETradeOperationType.TRADE_ACTION_DEAL) {
    if (tradeType.value === EOrderType.ORDER_TYPE_SELL) {
      return validationForInstantSell();
    } else {
      return validationForInstantBuy();
    }
  } else {
    if (tradeType.value === EOrderType.ORDER_TYPE_SELL) {
      return validationForPendingSell();
    } else {
      return validationForPendingBuy();
    }
  }
}

function handlePlaceOrder() {
  const validationErrors = handleValidation();

  if (validationErrors.length !== 0) {
    let message = "";

    validationErrors.forEach((err) => {
      message += err + "\n";
    });

    toast.error(message, {
      autoClose: false
    });

    return;
  }

  type TSocketData = {
    action: string;
    type: string;
    symbol: string;
    price: number;
    deviation: number;
    sl: number;
    tp: number;
    volume: number;
    type_time?: string;
    expiration?: string;
    comment: string;
  };

  const orderData: TSocketData = {
    action: tradeAction.value,
    type: tradeType.value,
    symbol: candleStickStore.marketWatchSymbol,
    price: placeOrder.value.price,
    deviation: placeOrder.value.deviation,
    sl: placeOrder.value.sl,
    tp: placeOrder.value.tp,
    volume: placeOrder.value.volume,
    comment: placeOrder.value.comment
  };

  if (tradeAction.value === ETradeOperationType.TRADE_ACTION_PENDING) {
    if (tradeType.value === EOrderType.ORDER_TYPE_BUY) {
      if (placeOrder.value.price > candleStickStore.askPrice) {
        orderData.type = EOrderType.ORDER_TYPE_BUY_STOP;
      } else {
        orderData.type = EOrderType.ORDER_TYPE_BUY_LIMIT;
      }
    } else {
      if (placeOrder.value.price > candleStickStore.bidPrice) {
        orderData.type = EOrderType.ORDER_TYPE_SELL_LIMIT;
      } else {
        orderData.type = EOrderType.ORDER_TYPE_SELL_STOP;
      }
    }

    orderData.type_time = placeOrder.value.type_time;

    if (orderData.type_time === EOrderTypeTime.ORDER_TIME_GTC) {
      orderData.expiration = DateTime.now().toFormat("dd.MM.yy HH:mm:ss");
    } else if (orderData.type_time === EOrderTypeTime.ORDER_TIME_DAY) {
      orderData.expiration = DateTime.now().toFormat("dd.MM.yy");
    } else if (
      placeOrder.value.type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED
    ) {
      const formattedDate = `${placeOrder.value.expiration} ${expirationHour.value}:00`;

      orderData.expiration = DateTime.fromFormat(
        formattedDate,
        "yyyy-MM-dd HH:mm"
      ).toFormat("dd.MM.yy HH:mm:ss");
    } else if (
      placeOrder.value.type_time === EOrderTypeTime.ORDER_TIME_RELATIVE
    ) {
      orderData.type_time = EOrderTypeTime.ORDER_TIME_SPECIFIED;
      orderData.expiration = DateTime.fromMillis(expirationTime.value)
        .setZone("Europe/Moscow")
        .toFormat("dd.MM.yy HH:mm:ss");
    }
  }

  eaSocket?.emit(`trade_data_${userStore.eaAccessToken}`, orderData);

  leftNavDrawerStore.togglePlaceNewOrderModal(false);
}

function resetValues() {
  const price =
    tradeType.value === EOrderType.ORDER_TYPE_SELL
      ? candleStickStore.bidPrice
      : candleStickStore.askPrice;

  placeOrder.value = {
    price,
    deviation: 0,
    sl: 1,
    tp: 1,
    volume: 1,
    comment: "Comment",
    type_time: EOrderTypeTime.ORDER_TIME_GTC,
    expiration: ""
  };

  calculateInitialSLAndTP();

  const priceDecimalCount = countDecimals(placeOrder.value.price);
  const slDecimalCount = countDecimals(placeOrder.value.sl);
  const tpDecimalCount = countDecimals(placeOrder.value.tp);

  priceStep.value = 1 / Math.pow(10, priceDecimalCount);
  slStep.value = 1 / Math.pow(10, slDecimalCount);
  tpStep.value = 1 / Math.pow(10, tpDecimalCount);

  calculateVolumeFromAuto();

  autoVolume.value = true;

  candleStickStore.updateTP1 = false;

  // TODO: Create a function to remove TP1 from chart.
  candleStickStore.tp1 = 0;
  candleStickStore.tp1_vol = 0;

  candleStickStore.updateSLTrial = false;

  tradeShapeStore.newOrderShape?.updatePrice(price, {});

  tradeShapeStore.newOrderShape?.updateOrderExecution(
    tradeAction.value === ETradeOperationType.TRADE_ACTION_DEAL
      ? "instant"
      : "order"
  );

  tradeShapeStore.newOrderShape?.updateOrderType(
    tradeType.value === EOrderType.ORDER_TYPE_SELL ? "sell" : "buy"
  );
}

function resetCustomFields(resetType: "tp1" | "sl_trail") {
  if (resetType === "tp1") {
    if (candleStickStore.updateTP1) {
      const { price, tp, volume } = placeOrder.value;

      const priceDecimalCount = countDecimals(price);

      const sign = tradeType.value === EOrderType.ORDER_TYPE_SELL ? -1 : 1;

      candleStickStore.tp1 = parseFloat(
        (price + sign * Math.abs((tp - price) / 2)).toFixed(priceDecimalCount)
      );

      const volStepDecimalCount = countDecimals(candleStickStore.volumeStep);

      tp1VolPercent.value = 50;

      candleStickStore.tp1_vol = parseFloat(
        ((tp1VolPercent.value / 100) * volume).toFixed(volStepDecimalCount)
      );

      scrollToDirection("bottom");
    } else {
      candleStickStore.tp1 = 0;
      candleStickStore.tp1_vol = 0;
    }
  }

  if (resetType === "sl_trail" && candleStickStore.slTrail) {
    candleStickStore.slTrail = 20;

    scrollToDirection("bottom");
  }
}

function scrollToDirection(direction: "top" | "bottom") {
  const el = document.getElementById("place-new-order");

  if (!el) {
    return;
  }

  if (direction === "bottom") {
    el.scrollTop = el.scrollHeight;
  } else {
    el.scrollTop = 0;
  }
}
</script>

<template>
  <div
    id="place-new-order-toolbar"
    class="absolute w-[370px] overflow-hidden rounded-md border bg-white text-sm shadow-lg"
    :style="{
      top: leftNavDrawerStore.placeNewOrderModalPosition.top + 'px',
      left: leftNavDrawerStore.placeNewOrderModalPosition.left + 'px'
    }"
  >
    <div
      id="place-new-order-toolbar-header"
      class="relative flex cursor-grab select-none justify-end bg-gray-50 p-0.5"
    >
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-base font-semibold"
      >
        NEW ORDER
      </div>

      <button
        type="button"
        class="rounded-md px-2.5 py-2 hover:bg-accent"
        @click="closeModal('minimize')"
      >
        <FontAwesomeIcon icon="fa-solid fa-minus" size="lg" />
      </button>

      <button
        type="button"
        class="rounded-md px-2.5 py-2 hover:bg-accent"
        @click="closeModal('restore')"
      >
        <FontAwesomeIcon icon="fa-regular fa-window-restore" />
      </button>

      <button
        type="button"
        class="rounded-md px-2.5 py-2 hover:bg-accent"
        @click="closeModal('close')"
      >
        <FontAwesomeIcon icon="fa-solid fa-xmark" size="lg" />
      </button>
    </div>

    <div class="grid grid-cols-3 border-y p-2 text-center">
      <div class="border-r">
        <div>SELL</div>
        <div class="font-semibold text-success">
          {{ candleStickStore.bidPrice }}
        </div>
      </div>

      <div class="border-r">
        <div>SPREAD</div>
        <div class="font-semibold text-primary">
          {{ candleStickStore.spread }}
        </div>
      </div>

      <div>
        <div>BUY</div>
        <div class="font-semibold text-danger">
          {{ candleStickStore.askPrice }}
        </div>
      </div>
    </div>

    <div v-show="!leftNavDrawerStore.isPlaceNewOrderModalMinimized">
      <div class="grid grid-cols-2 gap-x-2 px-2 pt-2">
        <div class="grid grid-cols-2 rounded-3xl border p-1">
          <button
            type="button"
            class="rounded-3xl pb-2 pt-2.5"
            :class="{ 'bg-selected text-white': sellBtn }"
            @click="handleButtonSwitch('sell')"
          >
            SELL
          </button>

          <button
            type="button"
            class="rounded-3xl pb-2 pt-2.5"
            :class="{ 'bg-selected text-white': buyBtn }"
            @click="handleButtonSwitch('buy')"
          >
            BUY
          </button>
        </div>

        <div class="grid grid-cols-2 rounded-3xl border p-1">
          <button
            type="button"
            class="rounded-3xl pb-2 pt-2.5"
            :class="{ 'bg-selected text-white': instantBtn }"
            @click="handleButtonSwitch('instant')"
          >
            INSTANT
          </button>

          <button
            type="button"
            class="rounded-3xl pb-2 pt-2.5"
            :class="{ 'bg-selected text-white': orderBtn }"
            @click="handleButtonSwitch('order')"
          >
            ORDER
          </button>
        </div>
      </div>

      <div
        id="place-new-order"
        class="scrollbar h-[185px] overflow-y-auto pb-2 pl-4 pr-2 pt-2"
      >
        <div class="grid grid-cols-12 gap-x-4">
          <div class="col-span-6 flex items-center">
            <InputLabel for="price">Price</InputLabel>

            <div class="pl-2">
              <InputText
                min="0"
                id="price"
                type="number"
                :step="priceStep"
                :class="{
                  'opacity-70':
                    tradeAction === ETradeOperationType.TRADE_ACTION_DEAL
                }"
                :disabled="
                  tradeAction === ETradeOperationType.TRADE_ACTION_DEAL
                "
                v-model="placeOrder.price"
              />
            </div>
          </div>

          <div class="col-span-6 flex items-center">
            <InputLabel for="deviation">Deviation</InputLabel>

            <div class="w-20 pl-4">
              <InputText
                min="0"
                type="number"
                id="deviation"
                v-model="placeOrder.deviation"
              />
            </div>
          </div>
        </div>

        <div class="mt-1 grid grid-cols-12 gap-x-4">
          <div class="col-span-6 flex items-center">
            <InputLabel for="sl">SL</InputLabel>

            <div class="pl-[26px]">
              <InputText
                min="0"
                id="sl"
                type="number"
                :step="slStep"
                v-model="placeOrder.sl"
                @input="calculatePipFromSL"
              />
            </div>
          </div>

          <div class="col-span-6 flex items-center">
            <div class="w-20">
              <InputText
                min="0"
                id="sl_pip"
                type="number"
                :class="{
                  'opacity-70':
                    tradeAction === ETradeOperationType.TRADE_ACTION_DEAL
                }"
                :disabled="
                  tradeAction === ETradeOperationType.TRADE_ACTION_DEAL
                "
                v-model="slPip"
                @input="calculateSLFromPip"
              />
            </div>

            <InputLabel for="sl_pip" class="pl-3">Pips</InputLabel>
          </div>
        </div>

        <div class="mt-1 grid grid-cols-12 gap-x-4">
          <div class="col-span-6 flex items-center">
            <InputLabel for="tp">TP</InputLabel>

            <div class="pl-[25px]">
              <InputText
                min="0"
                id="tp"
                type="number"
                :step="tpStep"
                v-model="placeOrder.tp"
                @input="calculatePipFromTP"
              />
            </div>
          </div>

          <div class="col-span-6 flex items-center">
            <div class="w-20">
              <InputText
                min="0"
                id="tp_pip"
                type="number"
                :class="{
                  'opacity-70':
                    tradeAction === ETradeOperationType.TRADE_ACTION_DEAL
                }"
                :disabled="
                  tradeAction === ETradeOperationType.TRADE_ACTION_DEAL
                "
                v-model="tpPip"
                @input="calculateTPFromPip"
              />
            </div>

            <InputLabel for="tp_pip" class="pl-3">Pips</InputLabel>
          </div>
        </div>

        <div class="mt-1 grid grid-cols-12 gap-x-4">
          <div class="col-span-6 flex items-center">
            <InputLabel for="vol">Vol</InputLabel>

            <div class="grow pl-[20px]">
              <InputText
                id="vol"
                type="number"
                :min="candleStickStore.minVolume"
                :max="candleStickStore.maxVolume"
                :step="candleStickStore.volumeStep"
                :class="{
                  'opacity-70':
                    tradeAction === ETradeOperationType.TRADE_ACTION_DEAL &&
                    autoVolume
                }"
                :disabled="
                  tradeAction === ETradeOperationType.TRADE_ACTION_DEAL &&
                  autoVolume
                "
                v-model="placeOrder.volume"
                @input="calculateAutoFromVolume"
              />
            </div>

            <Checkbox
              class="ml-2"
              :class="{
                'opacity-70':
                  tradeAction === ETradeOperationType.TRADE_ACTION_PENDING
              }"
              :disabled="
                tradeAction === ETradeOperationType.TRADE_ACTION_PENDING
              "
              v-model="autoVolume"
            />
          </div>

          <div class="col-span-6 flex items-center gap-x-4">
            <InputLabel for="auto">Auto</InputLabel>

            <div
              class="flex items-center rounded-md border bg-gray-50 pr-3 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500"
            >
              <InputText
                min="0"
                id="auto"
                max="100"
                step="0.125"
                type="number"
                class="border-0 focus:ring-0"
                v-model="candleStickStore.auto"
                @input="calculateVolumeFromAuto"
              />

              <span>%</span>
            </div>
          </div>
        </div>

        <div class="mt-1 flex items-center">
          <InputLabel for="comment">Com</InputLabel>

          <div class="grow pl-[10px]">
            <InputText id="comment" v-model="placeOrder.comment" />
          </div>
        </div>

        <template v-if="tradeAction === ETradeOperationType.TRADE_ACTION_DEAL">
          <div class="mt-2 flex items-center gap-x-2">
            <Checkbox
              id="update_tp1"
              v-model="candleStickStore.updateTP1"
              @change="resetCustomFields('tp1')"
            />

            <InputLabel for="update_tp1" class="!font-normal">
              Update TP1 and TP1 Vol
            </InputLabel>
          </div>

          <div
            class="mb-2 mt-1 grid grid-cols-12 gap-x-4"
            v-if="candleStickStore.updateTP1"
          >
            <div class="col-span-6 flex items-center">
              <InputLabel for="tp1">TP1</InputLabel>

              <div class="grow pl-[18px]">
                <InputText
                  min="0"
                  id="tp1"
                  type="number"
                  :step="tpStep"
                  v-model="candleStickStore.tp1"
                />
              </div>
            </div>

            <div class="col-span-6 flex items-center gap-x-4">
              <InputLabel for="tp1_vol">Vol</InputLabel>

              <div
                class="flex w-[90px] items-center rounded-md border bg-gray-50 pr-2 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500"
              >
                <InputText
                  min="0"
                  max="100"
                  id="tp1_vol"
                  type="number"
                  class="border-0 focus:ring-0"
                  v-model="tp1VolPercent"
                  @input="calculateTP1Vol"
                />

                <span>%</span>
              </div>
            </div>
          </div>

          <div class="mt-1 flex items-center gap-x-2">
            <Checkbox
              id="update_sl_trail"
              v-model="candleStickStore.updateSLTrial"
              @change="resetCustomFields('sl_trail')"
            />

            <InputLabel for="update_sl_trail" class="!font-normal">
              Update SL Trail
            </InputLabel>
          </div>

          <div
            class="mt-1.5 grid grid-cols-12 gap-x-4"
            v-if="candleStickStore.updateSLTrial"
          >
            <div class="col-span-6 flex items-center">
              <InputLabel for="sl_trail" class="shrink-0">SL Trail</InputLabel>

              <div class="pl-3">
                <InputText
                  min="1"
                  id="sl_trail"
                  type="number"
                  v-model="candleStickStore.slTrail"
                />
              </div>
            </div>
          </div>
        </template>

        <template
          v-if="tradeAction === ETradeOperationType.TRADE_ACTION_PENDING"
        >
          <div class="mt-1 flex items-center">
            <InputLabel for="type_time">Exp</InputLabel>

            <div class="grow pl-[17px]">
              <Select
                id="type_time"
                :defaultOption="false"
                v-model="placeOrder.type_time"
                @change="handleTimeType"
              >
                <option
                  :value="exp.id"
                  v-for="exp in timeTypeList"
                  :key="exp.id"
                >
                  {{ exp.name }}
                </option>
              </Select>
            </div>
          </div>

          <div
            class="mt-1 grid grid-cols-12 gap-x-4"
            v-if="placeOrder.type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED"
          >
            <div class="col-span-7 flex items-center">
              <InputLabel for="expiration">Date</InputLabel>

              <div class="w-36 pl-[10px]">
                <InputText
                  type="date"
                  id="expiration"
                  :min="DateTime.now().toFormat('yyyy-MM-dd')"
                  v-model="placeOrder.expiration"
                />
              </div>
            </div>

            <div class="col-span-5 flex items-center">
              <InputLabel for="expiration-hour">Hour</InputLabel>

              <div class="pl-[14px]">
                <Select
                  id="expiration-hour"
                  :defaultOption="false"
                  v-model="expirationHour"
                >
                  <option :value="i.toString()" v-bind:key="i" v-for="i in 24">
                    {{ i }}
                  </option>
                </Select>
              </div>
            </div>
          </div>
          <div
            class="mt-1 flex flex-col gap-x-4 gap-y-1 pl-10"
            v-if="placeOrder.type_time === EOrderTypeTime.ORDER_TIME_RELATIVE"
          >
            <div class="col-span-5 flex items-center">
              <div class="flex items-center gap-2">
                <div>
                  <Select
                    id="expiration-hour"
                    :defaultOption="false"
                    v-model="expirationHour"
                  >
                    <option
                      :value="(i - 1).toString()"
                      v-bind:key="i"
                      v-for="i in 24"
                    >
                      {{ i - 1 }}
                    </option>
                  </Select>
                </div>
                <InputLabel for="expiration-hour">hours</InputLabel>
              </div>

              <div class="flex items-center gap-2">
                <div class="pl-[14px]">
                  <Select
                    id="expiration-hour"
                    :defaultOption="false"
                    v-model="expirationMinute"
                  >
                    <option
                      :value="(i - 1).toString()"
                      v-bind:key="i"
                      v-for="i in 60"
                    >
                      {{ i - 1 }}
                    </option>
                  </Select>
                </div>
                <label for="expiration-hour">minutes</label>
              </div>
            </div>
            <div>
              {{
                parseInt(expirationHour) !== 0 ? `${expirationHour} hours` : ""
              }}
              {{ expirationMinute }} minutes from now is
              <span class="font-bold"> {{ expirationTimeString }} </span> ({{
                getUTCOffset(candleStickStore.timezone)
              }})
            </div>
          </div>
        </template>
      </div>

      <div class="border-t px-4 pb-1.5 pt-2">
        <PrimaryButton class="w-full" @click="handlePlaceOrder">
          Place Order
        </PrimaryButton>
      </div>
    </div>
  </div>
</template>

<style scoped>
label {
  margin-bottom: 0;
}
</style>
