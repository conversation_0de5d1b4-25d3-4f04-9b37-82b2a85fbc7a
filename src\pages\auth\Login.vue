<script setup lang="ts">
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";

import { object, string } from "yup";

import { useUserStore } from "@/store/userStore";

import Alert from "@/components/Alert.vue";
import InputLabel from "@/components/InputLabel.vue";
import InputText from "@/components/InputText.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

import {
  getClientValidationErrors,
  getServerErrors
} from "@/helpers/getErrors";

import CompanyLogoSVG from "@/assets/logo_white.svg";

import Appbar from "../website/components/Appbar.vue";
import { axios } from "@/api";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const route = useRoute();
const router = useRouter();

const userStore = useUserStore();

const userCredential = ref({
  email: "",
  password: ""
});
const btnLoading = ref(false);
const validationErrors = ref<typeof userCredential.value | null>(null);
const serverErrors = ref<string[]>([]);

const validationSchema = object({
  email: string()
    .email("Invalid email address.")
    .required("Email is required."),
  password: string().required("Password is required.")
});

async function handleLogin() {
  try {
    validationErrors.value = null;
    serverErrors.value = [];

    btnLoading.value = true;

    const user = await validationSchema.validate(userCredential.value, {
      abortEarly: false
    });

    const resp = await axios.post("/auth/login", {
      ...user,
      login_type: "frontend"
    });

    userStore.user = resp.data.data.user;
    const userToken = resp.data.data.user_token.access_token;
    userStore.userAccessToken = userToken;

    if ("redirect" in route.query) {
      const redirect = route.query.redirect as string;
      router.replace(redirect);
      return;
    }

    router.push({ name: "trading" });
  } catch (e) {
    btnLoading.value = false;

    validationErrors.value = getClientValidationErrors(e);
    serverErrors.value = getServerErrors(e);
  }
}
</script>

<template>
  <div class="hidden min-h-screen grid-cols-12 md:grid">
    <div
      id="introduction"
      class="relative col-span-12 grid place-items-center bg-tertiary text-white md:col-span-6"
    >
      <div class="px-5 lg:px-16">
        <router-link
          class="inline-flex items-center gap-x-1 text-4xl font-bold lg:text-5xl"
          :to="{ name: 'home' }"
        >
          <CompanyLogoSVG width="80" height="80" />
          Tradeawaay
        </router-link>

        <h2 class="mt-1 text-lg italic md:text-xl">
          "TRADING IS DECISION MAKING."
        </h2>

        <p class="mt-4 text-sm text-gray-300">
          Tradeaway is a web-based trading application powered by data analytics
          for traders to make informed decisions in day trading. Tradeaway
          provides full trading experience by connecting to your MetaTrader
          through Expert Advisor. It is designed for new beginners to
          experienced traders alike.
        </p>
      </div>

      <div class="absolute bottom-2 w-full">
        <p class="text-center text-sm">
          © {{ new Date().getFullYear() }} Tradeawaay. All rights reserved.
        </p>
      </div>
    </div>

    <div class="col-span-12 grid place-items-center bg-gray-100 md:col-span-6">
      <div
        class="w-[350px] rounded-md border bg-white p-5 text-sm shadow-lg lg:w-[400px]"
      >
        <h2 class="text-xl font-bold">Log In</h2>

        <p class="mt-1 text-gray-600">
          Discover a better way of trading with us.
        </p>

        <Alert
          variant="danger"
          class="mt-2 text-xs"
          v-if="serverErrors.length !== 0"
        >
          <div v-for="error in serverErrors">
            {{ error }}
          </div>
        </Alert>

        <form class="mt-3" novalidate @submit.prevent="handleLogin">
          <div>
            <InputLabel for="email">Email</InputLabel>

            <InputText
              id="email"
              type="email"
              placeholder="Email"
              :error="validationErrors?.email"
              v-model="userCredential.email"
            />
          </div>

          <div class="mt-4">
            <InputLabel for="password">Password</InputLabel>

            <InputText
              id="password"
              type="password"
              placeholder="Password"
              :error="validationErrors?.password"
              v-model="userCredential.password"
            />
          </div>

          <div class="mt-4">
            <PrimaryButton
              id="login-button"
              type="submit"
              class="flex w-full justify-center"
              :loading="btnLoading"
            >
              Log In
            </PrimaryButton>
          </div>
        </form>

        <!-- <div class="mt-5 text-center">
          <router-link
            class="hover:underline"
            :to="{ name: 'forgot-password' }"
          >
            Forgot password?
          </router-link>
        </div> -->

        <div class="mt-5 flex justify-center border-t pt-5">
          Don't have an account?

          <router-link
            class="ml-1 font-semibold underline"
            :to="{ name: 'register' }"
          >
            Sign up
          </router-link>
        </div>
      </div>
    </div>
  </div>

  <div class="flex min-h-screen flex-col md:hidden">
    <Appbar />

    <div class="grid grow place-items-center bg-accent px-5">
      <div class="container mx-auto">
        <div class="flex justify-center">
          <FontAwesomeIcon size="3x" icon="fa-solid fa-computer" />
        </div>

        <div class="mt-5 text-center text-lg font-bold">
          Oops! Login Not Available on Mobile Devices
        </div>

        <div class="mt-4 text-center">
          Our website is currently not supported on mobile devices. For the best
          experience, please access the site using a laptop or desktop computer.
          We apologize for any inconvenience and appreciate your understanding!
          <br />
          😊
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
#introduction {
  background-image: url("@/assets/shiny.svg");
  background-size: cover;
}
</style>
