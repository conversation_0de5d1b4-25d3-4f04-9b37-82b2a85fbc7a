<script setup lang="ts">
import { onMounted, ref } from "vue";

import { DateTime } from "luxon";
import { toast } from "vue3-toastify";

import ProfileMenu from "@/pages/trading/components/appbar/components/ProfileMenu.vue";
import SearchSymbolModal from "@/pages/trading/components/appbar/components/SearchSymbolModal.vue";

import { useAppbarStore } from "@/store/appbarStore";
import { useCandleStickStore } from "@/store/candleStickStore";
import { useChartStore } from "@/store/chartStore";
import { useTradeShapeStore } from "@/store/tradeShapeStore";

import Dropdown from "@/components/Dropdown.vue";
import NavItem from "@/components/NavItem.vue";

import { ChartInstanceError } from "@/helpers/errors";

import {
  candleStickIntervalList,
  loadCandleStickData
} from "@/utilities/candle-sticks.js";

import CameraOutlineSVG from "@/assets/svg/camera-outline.svg";

import { ICandleStickInterval } from "@/types/index";

import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const chartStore = useChartStore();
const appbarStore = useAppbarStore();
const tradeShapeStore = useTradeShapeStore();
const candleStickStore = useCandleStickStore();

const favoriteIntervals = ref<ICandleStickInterval[]>([]);
const downloadImageDropdown = ref(false);

onMounted(() => {
  initializeCandleStickIntervals();
});

function openSearchSymbolModal() {
  appbarStore.toggleModal("searchSymbolModal", true);
}

function initializeCandleStickIntervals() {
  const intervals = candleStickStore.chartSettings.favoriteIntervals;

  favoriteIntervals.value = candleStickIntervalList.map((interval) => {
    interval.toggle = intervals.includes(interval.id);
    return interval;
  });
}

async function changeCandleStickInterval(intervalId: string, idx: number) {
  if (!chartStore.chart) {
    throw new ChartInstanceError("Cannot change interval");
  }

  if (candleStickStore.interval === intervalId) {
    return;
  }

  try {
    // Check if bottom nav content area is open or not to remove trade shape.
    tradeShapeStore.hideNewOrderShape();
    tradeShapeStore.hideSelectedTradeShape();

    chartStore.chartLoader = true;

    const { candleStick } = await loadCandleStickData(
      candleStickStore.symbol,
      intervalId,
      candleStickStore.symbolBroker
    );

    // Box Plot
    if (intervalId === "PERIOD_D1" || intervalId === "PERIOD_W1") {
      chartStore.closeBoxPlot();
    }

    candleStickStore.storeChartSettings({
      interval: intervalId
    });

    candleStickStore.interval = intervalId;

    chartStore.chart.hub.mainOv.data = candleStick;

    chartStore.updateMarketClosingLines(candleStick);

    chartStore.chart.fullReset();

    chartStore.resetYZoom();

    tradeShapeStore.showOrderShape();
    tradeShapeStore.showSelectedTradeShape();

    if (favoriteIntervals.value[idx].toggle) {
      return;
    }

    addCandleStickIntervalToFavorites(idx);
  } catch (e) {
    toast.error("Cannot change interval");
    console.error("Cannot change interval:", e);
  } finally {
    chartStore.chartLoader = false;
  }
}

function addCandleStickIntervalToFavorites(idx: number) {
  const prevState = favoriteIntervals.value[idx].toggle;

  favoriteIntervals.value[idx].toggle = !prevState;

  const intervalIds: string[] = [];

  favoriteIntervals.value.forEach((interval) => {
    if (interval.toggle) {
      intervalIds.push(interval.id);
    }
  });

  candleStickStore.storeChartSettings({
    favoriteIntervals: intervalIds
  });
}

function dowloadChartImage() {
  const base64Image = chartStore.getChartImage();

  const link = document.createElement("a");

  link.href = base64Image;

  const { symbol, interval } = candleStickStore;

  const date = DateTime.now().toFormat("yyyy_MM_dd");

  link.download = `${symbol}_${interval}_${date}`;

  document.body.appendChild(link);

  link.click();

  document.body.removeChild(link);
}

async function copyChartImage() {
  try {
    const blob = await chartStore.getChartImageBlob();

    const clipboardItem = new ClipboardItem({
      [blob.type]: blob
    });

    await navigator.clipboard.write([clipboardItem]);

    toast.info("Image copied to clipboard.", {
      autoClose: 1000
    });
  } catch (_e) {
    toast.error("Failed to copy image to clipboard.", {
      autoClose: 1000
    });
  }
}
</script>

<template>
  <Teleport to="body">
    <SearchSymbolModal v-if="appbarStore.modals.searchSymbolModal" />
  </Teleport>

  <nav id="app-bar" class="flex justify-between border-b-4 py-1 pl-3.5 pr-1.5">
    <div class="flex gap-x-2">
      <h1 class="self-center font-semibold">TA</h1>

      <div>
        <NavItem
          class="flex items-center gap-x-1 font-semibold"
          :selected="appbarStore.modals.searchSymbolModal"
          @click="openSearchSymbolModal"
        >
          <FontAwesomeIcon icon="fa-solid fa-magnifying-glass" />

          <template v-if="candleStickStore.marketWatchOnlineStatus">
            {{ candleStickStore.marketWatchSymbol }}
          </template>

          <template v-else>
            {{ candleStickStore.symbol }}
          </template>
        </NavItem>
      </div>

      <div class="h-8 border-r"></div>

      <div class="flex">
        <template
          v-for="(interval, idx) in favoriteIntervals"
          :key="interval.id"
        >
          <NavItem
            :class="{
              'text-selected': interval.id === candleStickStore.interval
            }"
            v-if="interval.toggle"
            @click="changeCandleStickInterval(interval.id, idx)"
          >
            {{ interval.type }}
          </NavItem>
        </template>

        <Dropdown
          id="candlestick-interval-dropdown"
          toggle-id="candlestick-interval-toggle-dropdown"
          :offsetDistance="-40"
          :offsetSkidding="76"
        >
          <template #content>
            <div class="my-1 w-44">
              <div
                class="grid grid-cols-12 items-center hover:bg-accent"
                :class="{
                  'bg-selected text-white hover:bg-selected':
                    candleStickStore.interval === interval.id
                }"
                v-for="(interval, idx) in favoriteIntervals"
                :key="interval.id"
              >
                <div
                  class="col-span-9 px-3 pb-1.5 pt-2"
                  @click="changeCandleStickInterval(interval.id, idx)"
                >
                  {{ interval.name }}
                </div>

                <div class="col-span-3 flex justify-center">
                  <NavItem
                    class="!px-1 !py-0.5 hover:bg-gray-200"
                    @click="addCandleStickIntervalToFavorites(idx)"
                  >
                    <FontAwesomeIcon
                      icon="fa-solid fa-star"
                      class="text-orange-400"
                      v-if="interval.toggle"
                    />

                    <FontAwesomeIcon icon="fa-regular fa-star" v-else />
                  </NavItem>
                </div>
              </div>
            </div>
          </template>
        </Dropdown>
      </div>

      <div class="h-8 border-r"></div>
    </div>

    <div class="flex items-center gap-x-3">
      <Dropdown
        id="chart-image-download-dropdown"
        toggle-id="chart-image-download-toggle-dropdown"
        :class="{ 'bg-accent': downloadImageDropdown }"
        :icon="false"
        :offset-skidding="-41"
        @show="downloadImageDropdown = true"
        @hide="downloadImageDropdown = false"
      >
        <template #text>
          <CameraOutlineSVG />
        </template>

        <template #content="{ close }">
          <div class="my-1 w-56">
            <div
              class="flex items-center px-3 pb-1.5 pt-2 hover:bg-accent"
              @click="dowloadChartImage"
            >
              <div class="w-7">
                <FontAwesomeIcon icon="fa-solid fa-download" />
              </div>

              Download Image
            </div>

            <div
              class="flex items-center px-3 pb-1.5 pt-2 hover:bg-accent"
              @click="(close(), copyChartImage())"
            >
              <div class="w-7">
                <FontAwesomeIcon size="lg" icon="fa-regular fa-copy" />
              </div>
              Copy Image
            </div>
          </div>
        </template>
      </Dropdown>

      <ProfileMenu />
    </div>
  </nav>
</template>
