<script setup lang="ts">
import { ref } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { useChartStore } from "@/store/chartStore";

import Dropdown from "@/components/Dropdown.vue";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"trend-line"> =
  chartStore.selectedShape!;
const selectedLineWidth = ref(
  selectedShape.properties.lineProperties.line_width
);

const lineWidthDropdown = ref(false);

function handleLineWidth(lineWidth: number) {
  selectedLineWidth.value = lineWidth;

  selectedShape.setProperty("lineProperties", "line_width", lineWidth);

  chartStore.chart?.update();
}
</script>

<template>
  <Dropdown
    id="chart-modal-line-width-dropdown"
    toggle-id="chart-modal-line-width-toggle-dropdown"
    class="flex items-center gap-x-1 border"
    :class="{ 'border-info bg-accent': lineWidthDropdown }"
    :icon="false"
    @show="lineWidthDropdown = true"
    @hide="lineWidthDropdown = false"
    v-if="selectedLineWidth"
  >
    <template #text>
      <FontAwesomeIcon icon="fa-solid fa-minus" />
      {{ selectedLineWidth }}px
    </template>

    <template #content="{ close }">
      <div class="my-1 w-16">
        <div
          class="px-3 pb-1.5 pt-2 hover:bg-accent"
          :class="{
            'bg-selected text-white hover:bg-selected': i === selectedLineWidth
          }"
          v-for="i in 4"
          @click="(close(), handleLineWidth(i))"
        >
          {{ i }}px
        </div>
      </div>
    </template>
  </Dropdown>
</template>
