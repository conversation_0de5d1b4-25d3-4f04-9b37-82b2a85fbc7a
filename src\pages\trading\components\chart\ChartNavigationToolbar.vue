<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import { onKeyDown } from "@vueuse/core";

import { useChartFunctions } from "@/chart-frontend/chartHandlers";
import { useChartStore } from "@/store/chartStore";

import Tooltip from "@/components/Tooltip.vue";

import PlusSVG from "@/assets/svg/plus.svg";
import MinusSVG from "@/assets/svg/minus.svg";
import RefreshSVG from "@/assets/svg/refresh.svg";
import ResetZoomYAxisSVG from "@/assets/svg/reset_zoom_y_axis.svg";
import ChevronDoubleRightSVG from "@/assets/svg/chevron-double-right.svg";

const { handleZoomIn, handleZoomOut, handleChartMoveRight } =
  useChartFunctions();

const chartStore = useChartStore();

const showMoveRightButton = ref(false);
const toggleControlBar = ref(false);

let controlBarEl: HTMLElement | null;

const controlBarPosition = {
  top: 0,
  left: 0
};

onKeyDown(true, (e) => {
  if (e.altKey) {
    if (e.key === "+") {
      handleZoomIn();
    } else if (e.key === "-") {
      handleZoomOut();
    } else if (e.key.toLowerCase() === "r") {
      chartStore.handleZoomReset();
    } else if (e.key.toLowerCase() === "y") {
      chartStore.resetYZoom();
    } else if (e.code === "ArrowRight") {
      handleChartMoveRight();
    }
  }
});

onMounted(() => {
  controlBarEl = document.getElementById("chart-control-bar-actions");

  if (!controlBarEl) {
    return;
  }

  const { top, left } = controlBarEl.getBoundingClientRect();
  controlBarPosition.left = left;
  controlBarPosition.top = top;

  document.addEventListener("mousemove", handleControlBarVisibility);

  updateButtonVisibility();
});

function handleControlBarVisibility(e: MouseEvent) {
  if (!controlBarEl) {
    return;
  }

  const { x, y } = e;

  const { top, left, width, height } = controlBarEl.getBoundingClientRect();

  const perimeterTop = top - 100;
  const perimeterBottom = top + height + 50;
  const perimeterLeft = left - 150;
  const perimeterRight = left + width + 150;

  if (
    perimeterTop < y &&
    perimeterBottom > y &&
    perimeterLeft < x &&
    perimeterRight > x
  ) {
    toggleControlBar.value = true;
  } else {
    toggleControlBar.value = false;
  }
}

onUnmounted(() => {
  document.removeEventListener("mousemove", handleControlBarVisibility);
});

function updateButtonVisibility() {
  const chart = chartStore.chart;

  if (chart) {
    const mainPane = chart.data.panes[0];
    const mainOverlay = mainPane.overlays.find((o: any) => o.main);

    if (!mainOverlay || !mainOverlay.data || mainOverlay.data.length === 0) {
      console.error("Main overlay data is not available");
      return;
    }

    const dataLength = mainOverlay.data.length;
    const latestPoint = mainOverlay.data[dataLength - 1][0];
    const endViewPoint = chart.range[1];

    showMoveRightButton.value = latestPoint > endViewPoint;
  }
}
</script>

<template>
  <div id="chart-control-bar-section">
    <div
      id="chart-control-bar-actions"
      class="absolute bottom-24 left-1/2 -translate-x-1/2"
    >
      <div class="hidden gap-x-2" :class="{ '!flex': toggleControlBar }">
        <Tooltip
          id="chart-zoom-out-tooltip"
          trigger-id="chart-zoom-out-trigger-tooltip"
          class="rounded-md border bg-white p-0.5 shadow-md"
          @click="handleZoomOut"
        >
          <template #trigger>
            <MinusSVG />
          </template>

          <template #content>
            Zoom out

            <span class="ml-1 border border-warning px-1 pt-0.5 text-warning">
              Alt
            </span>

            <span
              class="border border-l-0 border-warning px-1 pt-0.5 text-warning"
            >
              -
            </span>
          </template>
        </Tooltip>

        <Tooltip
          id="chart-zoom-in-tooltip"
          trigger-id="chart-zoom-in-trigger-tooltip"
          class="rounded-md border bg-white p-0.5 shadow-md"
          @click="handleZoomIn"
        >
          <template #trigger>
            <PlusSVG />
          </template>

          <template #content>
            Zoom in

            <span class="ml-1 border border-warning px-1 pt-0.5 text-warning">
              Alt
            </span>

            <span
              class="border border-l-0 border-warning px-1 pt-0.5 text-warning"
            >
              +
            </span>
          </template>
        </Tooltip>

        <Tooltip
          id="chart-zoom-reset-tooltip"
          trigger-id="chart-zoom-reset-trigger-tooltip"
          class="rounded-md border bg-white p-0.5 shadow-md"
          @click="chartStore.handleZoomReset"
        >
          <template #trigger>
            <RefreshSVG />
          </template>

          <template #content>
            Reset Zoom

            <span class="ml-1 border border-warning px-1 pt-0.5 text-warning">
              Alt
            </span>

            <span
              class="border border-l-0 border-warning px-1 pt-0.5 text-warning"
            >
              R
            </span>
          </template>
        </Tooltip>

        <Tooltip
          id="chart-zoom-reset-yaxis-tooltip"
          trigger-id="chart-zoom-reset-yaxis-trigger-tooltip"
          class="rounded-md border bg-white p-0.5 shadow-md"
          @click="chartStore.resetYZoom"
        >
          <template #trigger>
            <ResetZoomYAxisSVG />
          </template>

          <template #content>
            Reset Zoom (Y-axis)

            <span class="ml-1 border border-warning px-1 pt-0.5 text-warning">
              Alt
            </span>

            <span
              class="border border-l-0 border-warning px-1 pt-0.5 text-warning"
            >
              Y
            </span>
          </template>
        </Tooltip>
      </div>
    </div>

    <Tooltip
      id="chart-scroll-latest-tooltip"
      trigger-id="chart-scroll-latest-trigger-tooltip"
      class="absolute bottom-24 right-20 rounded-md border bg-white p-0.5 shadow-md"
      @click="handleChartMoveRight"
    >
      <template #trigger>
        <ChevronDoubleRightSVG />
      </template>

      <template #content>
        Scroll right

        <span class="ml-1 border border-warning px-1 pt-0.5 text-warning">
          Alt
        </span>

        <span class="border border-l-0 border-warning px-1 pt-0.5 text-warning">
          →
        </span>
      </template>
    </Tooltip>
  </div>
</template>
