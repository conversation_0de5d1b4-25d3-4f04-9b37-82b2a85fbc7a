<script setup lang="ts">
import { useUserStore } from "@/store/userStore";

import ContactUs2 from "../components/ContactUs2.vue";
import Curriculum from "../components/Curriculum.vue";
import DetailedCurriculum from "../components/DetailedCurriculum.vue";
import FAQ2 from "../components/FAQ2.vue";
import InformativeFeatures from "../components/InformativeFeatures.vue";
import Introduction from "../components/Introduction.vue";
import Pricing2 from "../components/Pricing2.vue";
import PropTrading from "../components/PropTrading.vue";
import Testimonials from "../components/Testimonials.vue";
import TradingGoals from "../components/TradingGoals.vue";

const userStore = useUserStore();
</script>

<template>
  <Introduction />

  <TradingGoals />

  <PropTrading />

  <Curriculum />

  <DetailedCurriculum />

  <Pricing2 v-if="!userStore.user" />

  <InformativeFeatures />

  <Testimonials />

  <FAQ2 />

  <ContactUs2 />
</template>
