<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';

import { storeToRefs } from "pinia";

import { chatApi } from "@/api/chatApi";

import { chatSocket, initializeChatSocket } from "@/socketio/chat";

import { useUserStore } from "@/store/userStore";
import { useChartStore } from '@/store/chartStore';
import { Message, NewMessage, Room } from "@/types/chat";
import ConfirmDialog from '@/components/ConfirmDialog.vue';
import { useChatStore } from "@/features/stores/use-chat-store";
import MessageArea from "./components/MessageArea.vue";
import MessageInput from "./components/MessageInput.vue";
import RoomHeader from "./components/RoomHeader.vue";
import RoomsList from "./components/RoomsList.vue";
import TabNavigation from "./components/TabNavigation.vue";
import PrivateChat from "./components/PrivateChat.vue";
import { useChatResize } from "./composables/use-chat-resize";
import {watch} from "vue"
import {  axios, chatAxios } from "@/api";
import { useCandleStickStore } from "@/store/candleStickStore"; // Add this import
const userStore = useUserStore();
const chartStore = useChartStore();
const candleStickStore = useCandleStickStore(); // Add this
const searchResults = ref<Message[]>([])
const atTheRateActive=ref(false);
const userTag = ref("")
const ActiveUserForMessageSearch = ref('');
const parentMessage = ref<Message | null>(null);
const setActiveUserForMessageSearch = (userid: string) =>{
  ActiveUserForMessageSearch.value = userid;
}
const fileInChat = ref<File | null>(null);

// const setUserTag= ( e:Event) =>{
//   userTag.value = (e.target as HTMLInputElement).value
// }
const toggleAtTheRateActive = () => {
  atTheRateActive.value = !atTheRateActive.value;
  
  if (atTheRateActive.value) {
    // When @ button is activated, set mode to self to show mentions
    mode.value = "self";
    fetchMentionsForCurrentUser();
    userTag.value = ""; // Clear any previous search
  } else {
    // When @ button is deactivated, return to regular mode
    mode.value = "regular";
    searchResults.value = []; // Clear search results
  }
};

const tabs = ref([
  { id: "chatroom", name: "Chatroom" },
  { id: "private", name: "Private" }
]);

const search = ref("");
const mode = ref<"self" | "other" | "regular">("regular");

// Watch for mode changes
watch(mode, async (newMode) => {
  console.log("Mode changed to:", newMode);
  
  if (newMode === "self") {
    // Self mode - fetch messages where current user is mentioned
    await fetchMentionsForCurrentUser();
  } else if (newMode === "other" && ActiveUserForMessageSearch.value) {
    // Other mode - fetch messages from specific user
    await fetchUserMessages(ActiveUserForMessageSearch.value);
  } else if (newMode === "regular") {
    // Regular mode - show normal chat messages
    searchResults.value = []; // Clear search results to show normal messages
  }
});

// Fetch messages where current user is mentioned
const fetchMentionsForCurrentUser = async () => {
  if (!currentRoom.value || !userStore.user) return;
  
  clearTimeout(debounceTimeout);
  
  debounceTimeout = setTimeout(async () => {
    try {
      const { data } = await chatAxios.get(
        //@ts-expect-error fixme
        `/api/messages/searchByUserMentions?userId=${userStore.user._id}&groupId=${currentRoom.value._id}`
      );
      
      searchResults.value = data.data;
    } catch (error) {
      console.error('Error fetching mentions:', error);
    }
  }, debounceDelay);
};

// Set mode to "self" or "other"
const setMode = (newMode: "self" | "other" | "regular") => {
  mode.value = newMode;
};

// watch(ActiveUserForMessageSearch, async (newActiveUserForMessageSearch) => {
//   clearTimeout(debounceTimeout);
//   if (!currentRoom.value) return;
  
//   if (!newActiveUserForMessageSearch) {
//     searchResults.value = [];
//     return;
//   }
  
//   if (mode.value === 'self') {
//     await fetchMentionsForCurrentUser();
//   } else {
//     await fetchUserMessages(newActiveUserForMessageSearch);
//   }
// });

const chatStore = useChatStore();
const { activeTab, rooms, currentRoom, messages } = storeToRefs(chatStore);
const debounceDelay = 500;
let debounceTimeout: ReturnType<typeof setTimeout> | undefined = undefined;

watch(search, (newSearch) => {
  clearTimeout(debounceTimeout);
  if (!currentRoom.value) return;
  if (!newSearch) {
    searchResults.value = [];
    return;
  }

  debounceTimeout = setTimeout(async () => {
    try {
      if (newSearch.startsWith("#")) {
        console.log(newSearch.slice(1));
        const { data } = await chatAxios.get(
          `/api/messages/searchByTags?tags=${newSearch.slice(1)}&groupId=${currentRoom.value?._id}`
        );
        console.log(data);
        searchResults.value = data.data;
      } else {
        const { data } = await chatAxios.get(
          `/api/messages/searchByString?query=${newSearch}&groupId=${currentRoom.value?._id}`
        );
        console.log(data);
        searchResults.value = data.data;
      }
    } catch (error) {
      console.error("API error:", error);
    }
  }, debounceDelay);
});
const SearchMessageByUserResults= ref([]);
watch(ActiveUserForMessageSearch, async   (newActiveUserForMessageSearch) => {
  clearTimeout(debounceTimeout);
  if(!currentRoom.value) return;
  if (!newActiveUserForMessageSearch) {
    searchResults.value = [];
    return;
  }
  
  debounceTimeout = setTimeout(async () => {
    try {
      if(mode.value=='self'){
        const {data} = await chatAxios.get(
          `/api/messages/searchByUserMentions?userId=${newActiveUserForMessageSearch}&groupId=${currentRoom.value?._id}`
        );
        SearchMessageByUserResults.value = data.data;
      } else {
        const {data} = await chatAxios.get(
          `/api/messages/searchByUserId?userId=${newActiveUserForMessageSearch}&groupId=${currentRoom.value?._id}`
        );
        console.log(data);
        SearchMessageByUserResults.value = data.data;
      }
    } catch (error) {
      console.error('API error:', error);
    }
  }, debounceDelay);
});

const currentMessages = computed<Message[]>(() => {
  if (!currentRoom.value) {
    return [];
  }
  const currentRoomId = currentRoom.value._id;
  return messages.value[currentRoomId] || [];
});

const {
  containerRef,
  isRoomsListCollapsed,
  chatAreaStyle,
  roomsAreaStyle,
  startResize
} = useChatResize();
const changeTab = (tabId: string) => {
  activeTab.value = tabId;
  
  // Reset current room when switching tabs to avoid confusion
  if (tabId === 'chatroom' && rooms.value.length > 0) {
    chatStore.$patch({
      currentRoom: rooms.value[0]
    });
  }
};

const selectRoom = (room: Room) => {
  atTheRateActive.value=!atTheRateActive.value;
  clearFilters();
  currentRoom.value = room;
  fetchMainGroupMessages();
};

const handleRoomsCollapse = (collapsed: boolean) => {
  isRoomsListCollapsed.value = collapsed;
};

const getBrokerForCurrentTrade = () => {
  // First check if we have the current symbol's broker from candleStick store
  if (candleStickStore.symbolBroker) {
    return candleStickStore.symbolBroker;
  }
  
  // Fallback: check if we have broker info and can map the current symbol
  if (candleStickStore.brokerInfo && candleStickStore.symbol) {
    const symbol = candleStickStore.symbol;
    
    // Check if symbol exists in mapping
    if (symbol in candleStickStore.brokerInfo.mapping_symbols) {
      return candleStickStore.brokerInfo.mapping_symbols[symbol].broker_chart;
    } else {
      // Use default forex broker
      return candleStickStore.brokerInfo.forex_chart_broker;
    }
  }
  
  // Final fallback: search through symbolList
  if (candleStickStore.symbolList && candleStickStore.symbol) {
    const currentSymbol = candleStickStore.symbol;
    
    for (const watchlist of candleStickStore.symbolList) {
      if (watchlist.instruments.includes(currentSymbol)) {
        return watchlist.broker;
      }
    }
  }
  
  // Ultimate fallback
  return "L.F. Investment Limited";
};

const getCurrentSymbol = () => {
  return candleStickStore.symbol || "Unknown";
};
console.log("candlestickstore",candleStickStore.activeOrders);
const sendMessage = async (text: string) => { 
  if(fileInChat.value){
    const file = fileInChat.value;
    const formData = new FormData();
    formData.append("media", file);
    formData.append("groupId", currentRoom.value?._id || "");
    formData.append("content", text || "");
    formData.append("sender", userStore.user?._id || "");
    formData.append("normal_media", typeOfMedia.value === "normal" ? "true" : "false");
    
    if(typeOfMedia.value === "copyTrade") {
      formData.append("orderId", "66c86e6cc09424ce3091508f"); // You might want to make this dynamic too
      
      // Extract broker dynamically
      const currentBroker = getBrokerForCurrentTrade();
      const currentSymbol = getCurrentSymbol();
      console.log("Brooker",currentBroker);
      
      console.log(`Copy trade - Symbol: ${currentSymbol}, Broker: ${currentBroker}`);
      
      formData.append("broker", currentBroker);
      // formData.append("symbol", currentSymbol); // You might want to include symbol too
      formData.append("type", "ECN_Prime");
    }
    fileInChat.value = null; // Reset after sending
    const res = await chatAxios('/api/messages/media-message', {
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    console.log(res.data);
    fileInChat.value = null; // Reset file after sending
    // not ideal
    // fetchMainGroupMessages();
    // fetchRooms()
    messages.value[`${currentRoom?.value?._id}`].push({
      content: res.data.data.content,
      sender: {
        _id: userStore.user?._id || "",
        user_name: userStore.user?.user_name || "",
        // profile_picture: userStore.user?.profile_picture || ""
      },
      media: res.data.data.media,
      timestamp: res.data.data.timestamp,
      _id: res.data.data._id,
      parentId: res.data.data.parentId
    });
    
    return;
    //orderid,brokerid and type not requered for normal media
  }
  if (!text.trim() || !chatSocket || !currentRoom.value || !userStore.user) {
    return;
  }

  const body = {
    groupId: currentRoom.value._id,
    isDirect: false,
    sender: userStore.user._id,
    parentId: parentMessage.value!=null?parentMessage.value._id:null,
    content: text,
    sender_name: userStore.user.name,
    profile_picture_sender: userStore.user.profile_picture || "",
  };
    parentMessage.value=null;
  chatSocket.emit("sendMessage", body);
};

const updateLastMessage = (d: NewMessage) => {
console.log("New message received, pushing it to array:", d);
  if (d.groupId === currentRoom.value?._id) {
    const { sender, content, timestamp, _id,parentId } = d;
    currentMessages.value.push({ content, sender, timestamp, _id, parentId });
  }

  const roomIndex = rooms.value.findIndex((room) => room._id === d.groupId);

  if (roomIndex === -1) {
    return;
  }

  // Create a new array to trigger reactivity
  const updatedRooms = [...rooms.value];
  updatedRooms[roomIndex] = {
    ...updatedRooms[roomIndex],
    lastMessage: d
  };

  // Update the rooms array
  rooms.value = updatedRooms;
};

// Function to fetch rooms from API
const fetchRooms = async () => {
  try {
    const response = await chatApi.get("/api/groups");
    rooms.value = response.data.data || [];
    
    if (rooms.value.length > 0 && activeTab.value === 'chatroom') {
      // Force update the current room
      chatStore.$patch({
        currentRoom: rooms.value[0]
      });
      console.log("Rooms fetched, set current room to:", currentRoom.value);
    }

    rooms.value.forEach((room) => {
      if (!chatSocket || !userStore.user) {
        return;
      }

      chatSocket.emit("subscribe", {
        groupId: room._id,
        userId: userStore.user._id
      });
    });
  } catch (err) {
    console.error("Error fetching rooms:", err);
  }
};

const fetchMainGroupMessages = async () => {
  try {
    if (!currentRoom.value || !chatSocket || !userStore.user) return;

    const response = await chatApi.get(
      `/api/groups/${currentRoom.value._id}/messages?page=1&limit=50`
    );
    const groupMessages = response.data.data;
    messages.value[`${currentRoom.value._id}`] = groupMessages;
  } catch (error) {
    console.error("Error fetching messages", error);
  }
};

onMounted(async () => {
  await initializeChatSocket();
  await fetchRooms();
  await fetchMainGroupMessages();

  if (!chatSocket) {
    throw new Error("Chat socket not initialized");
  }
  chatSocket.removeAllListeners();

chatSocket.on("onDelete", (messagePayload: any) => {
    console.log(`Message deleted on delete event received: ${JSON.stringify(messagePayload)}`);
    handleMessageDeleted(messagePayload.message._id);
  });
  if (!currentRoom.value) {
    throw new Error("Current room not selected");
  }


  rooms.value.map((room) => {
    chatSocket?.on(`newMessage_${room._id}`, updateLastMessage);
  });

});
onUnmounted(() => {
  if (chatSocket && currentRoom.value) {
    chatSocket.removeAllListeners();
  }
});
watch(activeTab, (newTab, oldTab) => {
  if (newTab === 'chatroom' && oldTab !== 'chatroom') {
    initializeChatSocket();
    // Re-fetch rooms and messages when switching to chatroom tab
    fetchRooms();
    fetchMainGroupMessages();
    rooms.value.map((room) => {
    chatSocket?.on(`newMessage_${room._id}`, updateLastMessage);
  });
  }
});
const selectedUserId = ref<string | null>(null);
// const filteredMessages = computed(() => {
//   if (!selectedUserId.value) {
//     return currentMessages.value;
//   }
//   return currentMessages.value.filter(msg => msg.sender._id === selectedUserId.value);
// });

// Add handler for user selection
// const handleUserSelected = (userId: string) => {
//   selectedUserId.value = userId;
//   ActiveUserForMessageSearch.value = userId;
//   mode.value = "other"; // Switch to "other" mode when selecting a user
//   fetchUserMessages(userId);
// };
watch(activeTab,(newActiveTab)=>{
  console.log("newActiveTab",newActiveTab);
  if(newActiveTab=='chatroom'){
    //refresh when channging to chatroom
    fetchRooms();
    fetchMainGroupMessages();
  }
})
// Add function to fetch user messages
const fetchUserMessages = async (userId: string) => {
  if (!currentRoom.value) return;
  try {
    const { data } = await chatAxios.get(
      `/api/messages/searchByUserId?userId=${userId}&groupId=${currentRoom.value._id}`
    );
    console.log("Messages from user:", data);
    searchResults.value = data.data;
  } catch (error) {
    console.error('Error fetching user messages:', error);
  }
};

// Clear filters and return to normal view
const clearFilters = () => {  
  selectedUserId.value = null;
  ActiveUserForMessageSearch.value = '';
  mode.value = "regular"; 
  search.value = "";
  searchResults.value = [];
  atTheRateActive.value = false;
};

// Add a ref for user search results
const userSearchResults = ref<any[]>([]);

// Handle user selection from search results
const handleUserSelection = (user: any) => {
  // Set active user and switch to "other" mode
  ActiveUserForMessageSearch.value = user._id;
  mode.value = "other";
  atTheRateActive.value = false; // Close the search dropdown
  
  fetchUserMessages(user._id);
};

const setUserTag = async (e: Event) => {
  const value = (e.target as HTMLInputElement).value;
  userTag.value = value;
  
  if (value.trim()) {
    clearTimeout(debounceTimeout);
    
    debounceTimeout = setTimeout(async () => {
      try {
        // const { data } = await axios.get(`/user/search?user_name=${value}`);
        userSearchResults.value =  [];
      } catch (error) {
        console.error('Error searching users:', error);
        userSearchResults.value = [];
      }
    }, debounceDelay);
  } else {
    userSearchResults.value = [];
  }
};
const handleParentUpdated = (message: Message) => {
  parentMessage.value = message;
  console.log("Parent message updated:", parentMessage.value);
};
watch(parentMessage, (newParentMessage) => {
  console.log("Parent message changed in chat:", newParentMessage);
});
const isExpanded = ref(false);

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// Add this function to handle message deletion
const handleMessageDeleted = (messageId: string) => {
  
  // Remove the message from all relevant arrays
  if (currentRoom.value && currentRoom.value._id) {
    // Remove from current messages
    if (messages.value[currentRoom.value._id]) {
      messages.value[currentRoom.value._id] = messages.value[currentRoom.value._id].filter(
        msg => msg._id !== messageId
      );
    }
    
    // Remove from search results if present
    if (searchResults.value.length > 0) {
      searchResults.value = searchResults.value.filter(
        msg => msg._id !== messageId
      );
    }
  }
};

// Add state for confirmation dialog
const showDeleteConfirm = ref(false);
const messageToDelete = ref<string | null>(null);

// Handle message deletion request from child components
const handleDeleteRequest = (messageId: string) => {
  messageToDelete.value = messageId;

  showDeleteConfirm.value = true;
};

// Handle confirmation
const confirmDelete = async () => {
  if (!messageToDelete.value) return;
  
  try {
    console.log('Deleting message with ID:', messageToDelete.value);
    await chatAxios.delete(`/api/messages/${messageToDelete.value}`);
    // chatSocket?.emit("onDelete", messageToDelete.value);
    // console.log('Message deleted successfully n delete emitted');
    handleMessageDeleted(messageToDelete.value);
    
    // Reset state
    showDeleteConfirm.value = false;
    messageToDelete.value = null;
  } catch (error) {
    console.error('Error deleting message:', error);
    showDeleteConfirm.value = false;
    messageToDelete.value = null;
  }
};

// Handle cancellation
const cancelDelete = () => {
  showDeleteConfirm.value = false;
  messageToDelete.value = null;
};
///////copy trading////////////////////////////////////////////////
const typeOfMedia = ref<"normal" | "copyTrade">("normal");
const handleImageCopied = (file: File) => {
  fileInChat.value = file;
};
const toggleTypeOfMedia = () => {
  typeOfMedia.value = typeOfMedia.value === "normal" ? "copyTrade" : "normal";
};
</script>

<template>
  <div class="flex items-center justify-center h-full bg-gray-100">
    <div
      ref="containerRef"
      class="shadow-md rounded-md w-full h-full bg-white relative flex flex-col overflow-hidden"
    >
      <!-- Chat window (top section) -->
      <div
        class="flex flex-col grow bg-white border-b border-gray-200 overflow-hidden"
        :style="chatAreaStyle"
      >
        <!-- Tab navigation -->
        <TabNavigation
          :tabs="tabs"
          :activeTab="activeTab"
          @tab-change="changeTab"
        />

        <!-- Public Chat Content -->
        <div v-if="activeTab === 'chatroom'" class="flex flex-col flex-1 min-h-0">
          <!-- Room title and controls -->
          <RoomHeader 
            :atTheRateActive="atTheRateActive"
            :toggleAtTheRateActive="toggleAtTheRateActive" 
            v-if="currentRoom" 
            :room="currentRoom" 
            v-model:search="search"
            @toggle-expand="toggleExpand"
          />
          
          <!-- Mode indicator - only show when not in regular mode -->
          <div v-if="mode === 'self' || mode === 'other'" class="bg-blue-50 px-4 py-2 flex items-center justify-between border-b border-blue-100">
            <div class="flex items-center">
              <span class="text-blue-700 font-medium">
                {{ mode === 'self' ? 'Showing messages where you are mentioned' : 'Showing messages from selected user' }}
              </span>
            </div>
            <button @click="clearFilters" class="text-blue-500 hover:text-blue-700 text-sm font-medium">
              Clear filter
            </button>
          </div>
          
          <!-- Message area -->
          <MessageArea 
            v-model:search="search" 
            class="flex-1 min-h-0" 
            :parent-message="parentMessage"
            :messages="mode === 'self' ? searchResults : 
                      (mode === 'other' ? searchResults : 
                      (search ? searchResults : currentMessages))"
                
            @parent-updated="handleParentUpdated"
            @message-deleted="handleMessageDeleted"
            @delete-request="handleDeleteRequest"
          />

          <!-- Input area -->
           <!-- removed this from props -->
           <!-- :userSearchResults="userSearchResults" -->
          <MessageInput
          :type-of-media="typeOfMedia"
          @type-of-media-updated="toggleTypeOfMedia"
            :file-in-chat="fileInChat"
            :mode="mode"
            @image-copied="handleImageCopied"
            :set-mode="setMode"
            :set-active-user-for-message-search="setActiveUserForMessageSearch" 
            :ActiveUserForMessageSearch="ActiveUserForMessageSearch"
            :userTag="userTag"
            :setUserTag="setUserTag" 
            :atTheRateActive="atTheRateActive"
            @user-selected="handleUserSelection"
            :parent-message="parentMessage"
            @parent-updated="handleParentUpdated"
            @send-message="sendMessage"
          />
          <div v-if="!isExpanded" class="relative group cursor-row-resize" @mousedown="startResize">
        <div class="h-1 bg-gray-300 group-hover:bg-blue-400 relative"></div>

        <div
          class="absolute inset-x-0 -top-2 -bottom-2 cursor-row-resize"
        ></div>
      </div>

      <div
        v-if="!isExpanded"
        class="flex flex-col bg-gray-50 overflow-hidden"
        :style="roomsAreaStyle"
      >
        <div class="flex-1 min-h-0 overflow-hidden">
          <RoomsList
            v-if="currentRoom"
            :rooms="rooms"
            :currentRoom="currentRoom"
            @select-room="selectRoom"
            @collapse-change="handleRoomsCollapse"
          />
        </div>
      </div>
        </div>


        <!-- Private Chat Content -->
        <div v-else-if="activeTab === 'private'" class="flex flex-col flex-1 min-h-0">
          <PrivateChat />
        </div>
      </div>

      <!-- Resize handle -->
      <!-- Visual resize handle -->
      
    </div>
  </div>

  <!-- Confirmation Dialog at the root level -->
  <ConfirmDialog
    :isOpen="showDeleteConfirm"
    title="Delete Message"
    message="Are you sure you want to delete this message? This action cannot be undone."
    @confirm="confirmDelete"
    @cancel="cancelDelete"
  />
</template>

<style scoped>
/* Additional styles for resize functionality */
.resize-active {
  pointer-events: none;
}

/* Ensure proper overflow handling during resize */
.min-h-0 {
  min-height: 0;
}
</style>
