import { SymbolCalcMode } from "./enums";

export interface Symbol {
  _id: string;
  broker: string;
  display_name: string;
  instruments: string[];
}

export interface ICandleStickInterval {
  id: string;
  type: string;
  name: string;
  toggle?: boolean;
}

export interface IOHLC {
  open: number;
  high: number;
  low: number;
  close: number;
  timestamp: string;
  realVolume: number;
  tickVolume: number;
  meta: {
    symbol: string;
    broker?: string;
  };
}

export interface IIncompleteOHLC {
  open: number;
  high: number;
  low: number;
  close: number;
  realVolume: number;
  tickVolume: number;
  spread: number;
  symbol: string;
  period: string;
  time: string;
}

export interface Broker {
  _id: string;
  broker: string;
  type: string;
  display_name: string;
  forex_chart_broker: string;
  instruments: string[];
  mapping_symbols: {
    [key: string]: {
      broker_chart: string;
      symbol: string;
    };
  };
}

export interface BrokerInfo {
  broker: string;
  broker_type: string;
  display_name: string;
  forex_chart_broker: string;
  mapping_symbols: {
    [key: string]: {
      broker_chart: string;
      symbol: string;
    };
  };
}

export interface MarketWatchSymbol {
  symbol: string;
  bid: number;
  bid_color: string;
  ask: number;
  ask_color: string;
  daily_change: number;
  daily_change_color: string;
  spread: number;
  symbol_contract_size: string;
  symbol_bid_high: number;
  symbol_bid_low: number;
  symbol_session_open: number;
  symbol_margin_currency: string;
  symbol_profit_currency: string;
  symbol_tick_size: number;
  symbol_tick_value: number;
  symbol_volume_min: number;
  symbol_volume_max: number;
  symbol_volume_step: number;
  symbol_digit: number;
  symbol_trade_calc_mode: SymbolCalcMode;
  symbol_initial_margin_sell: number;
  symbol_initial_margin_buy: number;
}

export interface IUser {
  _id: string;
  name: string;
  user_name?:string;
  profile_picture: string | null;
  email: string;
  phone_number: string | null;
  gender: string | null;
  roles: string[];
  location: IUserLocation | null;
  payment: IUserPayment | null;
  is_suspended: boolean;
  sessions: IUserSession[];
  createdAt: Date;
  updatedAt: Date;
  username:string;
}

export interface IUserLocation {
  country: string;
  state: string;
}

export interface IUserPayment {
  _id: string;
  email: string;
  card_type: string;
  last_4: string;
  subscription_plan: string;
  subscription_status: string;
  trial_end: string;
  trial_start: string;
  current_period_start: string;
  current_period_end: string;
  payment_status: string;
  cancel_effective_date: string;
  billing_address?: BillingDetails;
}

export interface BillingDetails {
  city: string;
  state: string;
  country: string;
  postal_code: string;
  line1: string;
  line2: string;
}

export interface IUserSession {
  _id: string;
  login_type: string;
  ip_address: string;
  access_token: string;
  last_login_date: string;
}

export interface IEAAccount {
  balance: number;
  currency: string;
  leverage: number;
  equity: number;
  margin: number;
  margin_level: number;
  margin_free: number;
  mt5_id: number;
  account_name: string;
  trade_server: string;
  broker: string;
  profit: number;
}

export interface IActiveOrders {
  open_time: string;
  symbol: string;
  ticket: number;
  type: string;
  volume: number;
  sl: number;
  tp: number;
  tp1: number;
  tp1_status: string;
  vol1: number;
  vol1_status: string;
  open_price: number;
  current_price: number;
  comment: string | null;
  sl_trail: number;
  margin: number;
  exp_profit: number;
  exp_loss: number;
  swap: number;
  profit: number;
  symbol_contract_size: number;
}

export interface IPlacedOrders {
  open_time: string;
  symbol: string;
  ticket: number;
  type: string;
  volume: number;
  sl: number;
  tp: number;
  open_price: number;
  current_price: number;
  comment: string | null;
  type_time: string;
  expiration: string;
}

export interface IHistoryDeals {
  open_time: string;
  close_time: string | null;
  symbol: string;
  ticket: number;
  type: string;
  open_volume: number;
  close_volume: number;
  open_price: number;
  close_price: number;
  sl: number;
  tp: number;
  out_comment: string | null;
  commission: number;
  swap: number;
  profit: number;
}

export interface IChartSettings {
  symbol: string;
  symbol_broker: string;
  interval: string;
  timezone: string;
  favoriteIntervals: string[];
  volume: boolean;
  show_market_watch_detail: boolean;
  market_watch_settings: string[];
  marketCloseLine: boolean;
  londonMarket: boolean;
  asiaMarket: boolean;
  newYorkMarket: boolean;
  e_calendar_timezone: string;
  heatmap_show_pips: boolean;
  open_trades_headers: string[];
}

export interface IEconomicCalendar {
  time: string;
  currency: string;
  country: string;
  importance: string;
  description: string;
  actual_value: string;
  forecast_value: string;
  prev_value: string;
}

export interface AnalyticsSummary {
  today_return: number;
  week_td_return: number;
  month_td_return: number;
  year_td_return: number;
  total_return: number;
  trade_win_percentage: string;
  profit_factor: number;
  worst_day: {
    worst_day: string;
    profit: number;
  };
  best_day: {
    best_day: string;
    profit: number;
  };
  worst_week: {
    worst_week: string;
    profit: number;
  };
  best_week: {
    best_week: string;
    profit: number;
  };
  average_trade_length: string;
  average: {
    average_result: number;
    average_win: number;
    average_loss: number;
    risk_reward_ratio: number;
  };
  max_drawdown: string;
}

export interface ProfitAnalytics {
  date: string;
  balance: number;
  profitAmount: number;
  profitReturns: number;
}

export interface ProfitInstrumentAnalytics {
  instrument: string;
  profit: number;
  trades: number;
  date: string;
}

export interface EventNews {
  _id: string;
  title: string;
  category: string;
  description: string;
  createdAt: string;
}

export interface SubscriptionPlan {
  _id: string;
  name: string;
  service_name: string;
  description: string;
  features: string[];
  trial_days: string;
  charge: string;
  duration: string;
}

export interface BoxPlotData {
  highValues: {
    high_min: number;
    high_max: number;
    high_median: number;
    high_q1: number;
    high_q3: number;
  };
  lowValues: {
    low_min: number;
    low_max: number;
    low_median: number;
    low_q1: number;
    low_q3: number;
  };
}

export interface Timezone {
  id: string;
  name: string;
  offset: string;
  timezone: string;
}
