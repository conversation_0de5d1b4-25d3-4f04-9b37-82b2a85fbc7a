import { computed, ref } from "vue";

import { defineStore } from "pinia";

import { Message, Messages, Room } from "@/types/chat";

export const useChatStore = defineStore("chat", () => {
  const activeTab = ref("chatroom");
  const rooms = ref<Room[]>([]);
  const currentRoom = ref<Room | null>(null);

  const messages = ref<Messages>({});

  const currentMessages = computed<Message[]>(() => {
    if (!currentRoom.value) {
      return [];
    }
    const currentRoomId = currentRoom.value._id;
    return messages.value[currentRoomId] || [];
  });

  return { activeTab, rooms, currentRoom, messages, currentMessages };
});
