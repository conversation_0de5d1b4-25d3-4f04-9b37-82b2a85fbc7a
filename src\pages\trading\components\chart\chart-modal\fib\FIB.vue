<script setup lang="ts">
import { onMounted, ref } from "vue";
import { cloneDeep } from "lodash-es";
import {
  IFib,
  IFibLevel
} from "@/lib/night-vision/shapes/base/shape-properties.types";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";
import { useChartStore } from "@/store/chartStore";
import { useAppbarStore } from "@/store/appbarStore";

import LineLevelWidth from "./LineLevelWidth.vue";
import LineLevelType from "./LineLevelType.vue";

import Dropdown from "@/components/Dropdown.vue";
import Checkbox from "@/components/Checkbox.vue";
import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import ColorPicker from "@/components/ColorPicker.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

import PlusSVG from "@/assets/svg/plus.svg";

const chartStore = useChartStore();
const appbarStore = useAppbarStore();

const selectedShape: IBaseShapeOptions<"fib-level"> | undefined =
  chartStore.selectedShape;

const isLeftLineExtended = ref(false);
const isRightLineExtended = ref(false);
const fibLevels = ref<IFibLevel[]>([]);
const oneColorDropdown = ref(false);
const selectdOneColor = ref<string | null>(null);
const labelPositionDropdown = ref(false);
const selectedLabelPosition = ref("");

let previousSettings = {} as {
  level_properties: IFib;
  left_line_extend: boolean;
  right_line_extend: boolean;
  fib_levels: IFibLevel[];
};

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set fib properties");
  }

  const levelProperties = selectedShape.properties.levelProperties;

  isLeftLineExtended.value = levelProperties.extend_dir_1;
  isRightLineExtended.value = levelProperties.extend_dir_2;

  fibLevels.value = selectedShape.properties.fibLevels;

  selectdOneColor.value =
    selectedShape.properties.levelProperties.level_line_color;

  selectedLabelPosition.value = levelProperties.level_text_position;

  previousSettings = {
    level_properties: cloneDeep(levelProperties),
    left_line_extend: isLeftLineExtended.value,
    right_line_extend: isRightLineExtended.value,
    fib_levels: cloneDeep(fibLevels.value)
  };
});

function closeModal() {
  appbarStore.toggleModal("chartSettingsModal", false);
}

function handleLeftLineExtend() {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to extend fib left line");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to extend fib left line");
  }

  selectedShape.setProperty(
    "levelProperties",
    "extend_dir_1",
    isLeftLineExtended.value
  );

  chartStore.chart.update();
}

function handleRightLineExtend() {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to extend fib right line");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to extend fib right line");
  }

  selectedShape.setProperty(
    "levelProperties",
    "extend_dir_2",
    isRightLineExtended.value
  );

  chartStore.chart.update();
}

function handleFibLevel(idx: number) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Cannot set fib level");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Cannot set fib level");
  }

  const level = fibLevels.value[idx];

  selectedShape.setProperty("fibLevels", idx, level);

  chartStore.chart.update();
}

function handleLineLevelColor(color: string, idx: number) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Cannot update fib level color");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Cannot set fib fib level color");
  }

  const level = fibLevels.value[idx];

  level.level_line_color = color;

  selectdOneColor.value = null;

  selectedShape.setProperty("fibLevels", idx, level);

  selectedShape.properties.levelProperties.level_line_color = null;

  selectedShape.setProperty("levelProperties", "level_line_color", null);

  chartStore.chart.update();
}

function handleUseOneColor(color: string) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Cannot change fib one color");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Cannot change fib one color");
  }

  selectdOneColor.value = color;

  selectedShape.properties.levelProperties.level_line_color = color;

  selectedShape.setProperty("levelProperties", "level_line_color", color);

  fibLevels.value.forEach((level, idx) => {
    level.level_line_color = color;
    selectedShape.setProperty("fibLevels", idx, level);
  });

  chartStore.chart.update();
}

function handleLabelPosition(position: "left" | "right") {
  if (!selectedShape) {
    throw new ShapeInstanceError("Cannot change fib level position");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Cannot change fib level position");
  }

  selectedLabelPosition.value = position;

  selectedShape.setProperty("levelProperties", "level_text_position", position);

  chartStore.chart.update();
}

function restorePreviousSettings() {
  if (!selectedShape) {
    throw new ShapeInstanceError("Cannot reset fib previous settings");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Cannot reset fib previous settings");
  }

  for (const [key, value] of Object.entries(
    previousSettings.level_properties
  )) {
    selectedShape.setProperty("levelProperties", key as keyof IFib, value);
  }

  previousSettings.fib_levels.forEach((level, idx) => {
    selectedShape.setProperty("fibLevels", idx, level);
  });

  chartStore.chart.update();

  closeModal();
}
</script>

<template>
  <div class="scrollbar h-[350px] overflow-auto border-t p-3">
    <div class="grid grid-cols-12 items-center">
      <div class="col-span-4">Levels Line</div>

      <div class="col-span-8 flex gap-x-3">
        <LineLevelWidth />

        <LineLevelType />
      </div>
    </div>

    <div class="mt-4 flex items-center gap-x-3">
      <Checkbox
        id="extend_left_line"
        v-model="isLeftLineExtended"
        @change="handleLeftLineExtend"
      />

      <InputLabel for="extend_left_line" class="!mb-0 font-normal">
        Extend left line
      </InputLabel>
    </div>

    <div class="mt-3 flex items-center gap-x-3">
      <Checkbox
        id="extend_right_line"
        v-model="isRightLineExtended"
        @change="handleRightLineExtend"
      />

      <InputLabel for="extend_right_line" class="!mb-0 font-normal">
        Extend right line
      </InputLabel>
    </div>

    <div class="mt-4 grid grid-cols-2 gap-x-7 gap-y-2">
      <div
        class="flex items-center gap-x-2"
        v-for="(fibLevel, idx) in fibLevels"
        :key="idx"
      >
        <div>
          <Checkbox
            v-model="fibLevel.level_show"
            @change="handleFibLevel(idx)"
          />
        </div>

        <div>
          <InputText
            min="0"
            type="number"
            v-model="fibLevel.level_position"
            @input="handleFibLevel(idx)"
          />
        </div>

        <div>
          <Dropdown
            class="border !px-1.5 hover:border-gray-300 hover:bg-white"
            :id="`chart-modal-fib-level-${idx}-dropdown`"
            :toggle-id="`chart-modal-fib-level-${idx}-toggle-dropdown`"
            :icon="false"
            :offset-skidding="114"
          >
            <template #text>
              <div
                class="h-4 w-4"
                :style="{
                  backgroundColor: fibLevel.level_line_color
                }"
              ></div>
            </template>

            <template #content>
              <ColorPicker
                :color="fibLevel.level_line_color"
                @update-color="
                  (color) => {
                    handleLineLevelColor(color, idx);
                  }
                "
              />
            </template>
          </Dropdown>
        </div>
      </div>
    </div>

    <div class="mt-5 grid grid-cols-12 items-center">
      <div class="col-span-4">Use one color</div>

      <div class="col-span-8">
        <Dropdown
          id="chart-modal-fib-one-color-dropdown"
          toggle-id="chart-modal-fib-one-color-toggle-dropdown"
          class="flex h-9 w-9 items-center justify-center border !p-0"
          :class="{ 'border-info': oneColorDropdown }"
          :icon="false"
          :offset-skidding="112"
          @show="oneColorDropdown = true"
          @hide="oneColorDropdown = false"
        >
          <template #text>
            <div
              class="h-4 w-4"
              :style="{
                backgroundColor: selectdOneColor
              }"
              v-if="selectdOneColor"
            ></div>

            <PlusSVG v-else />
          </template>

          <template #content>
            <div>
              <ColorPicker
                :color="selectdOneColor ?? ''"
                @update-color="handleUseOneColor"
              />
            </div>
          </template>
        </Dropdown>
      </div>
    </div>

    <div class="mt-5 grid grid-cols-12 items-center">
      <div class="col-span-4">Labels</div>

      <div class="col-span-8">
        <Dropdown
          id="chart-modal-fib-levels-position-dropdown"
          toggle-id="chart-modal-fib-levels-position-toggle-dropdown"
          class="flex w-24 items-center justify-between border"
          :class="{ 'border-info': labelPositionDropdown }"
          @show="labelPositionDropdown = true"
          @hide="labelPositionDropdown = false"
        >
          <template #text>
            {{ selectedLabelPosition === "left" ? "Left" : "Right" }}
          </template>

          <template #content="{ close }">
            <div class="my-1 w-24">
              <div
                class="cursor-default px-3 pb-1.5 pt-2 hover:bg-accent"
                @click="(close(), handleLabelPosition('left'))"
              >
                Left
              </div>

              <div
                class="cursor-default px-3 pb-1.5 pt-2 hover:bg-accent"
                @click="(close(), handleLabelPosition('right'))"
              >
                Right
              </div>
            </div>
          </template>
        </Dropdown>
      </div>
    </div>
  </div>

  <div class="flex justify-end gap-x-3 border-t px-3 pb-1.5 pt-2">
    <PrimaryButton class="px-3" @click="closeModal">OK</PrimaryButton>

    <PrimaryButton
      class="border border-info bg-white !text-info hover:border-secondary hover:!text-white"
      @click="restorePreviousSettings"
    >
      Discard
    </PrimaryButton>
  </div>
</template>
