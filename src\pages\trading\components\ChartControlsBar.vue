<script setup lang="ts">
import { ref, onUnmounted } from "vue";
import { DateTime } from "luxon";

import { useCandleStickStore } from "@/store/candleStickStore";
import { timezones } from "@/utilities/timezones";

import ChartSettingsDropdown from "./chart/ChartSettingsDropdown.vue";

import Dropdown from "@/components/Dropdown.vue";

interface ITimezone {
  id: string;
  offset: string;
  name: string;
  timezone: string;
}

const candleStickStore = useCandleStickStore();

const timezoneList = ref<ITimezone[]>(timezones);
const selectedTimezone = ref<ITimezone>({
  id: "utc",
  name: "UTC",
  offset: "(UTC)",
  timezone: "UTC"
});
const time = ref("");

onUnmounted(() => {
  clearInterval(timeInterval);
});

initializeTime();

const timeInterval = setInterval(() => {
  time.value = DateTime.now()
    .setZone(selectedTimezone.value.timezone)
    .toFormat("HH:mm:ss");
}, 1000);

function initializeTime() {
  time.value = DateTime.now()
    .setZone(candleStickStore.timezone)
    .toFormat("HH:mm:ss");

  selectedTimezone.value = timezoneList.value.find(
    (time) => time.timezone === candleStickStore.timezone
  )!;
}

function handleTimezone(idx: number, timezone: string) {
  if (selectedTimezone.value.timezone === timezone) {
    return;
  }

  time.value = DateTime.now().setZone(timezone).toFormat("HH:mm:ss");

  selectedTimezone.value = timezoneList.value[idx];

  candleStickStore.timezone = timezone;

  candleStickStore.storeChartSettings({
    timezone
  });
}
</script>

<template>
  <div id="chart-controls-bar" class="flex justify-end border-t">
    <Dropdown
      id="timezone-dropdown"
      toggle-id="timezone-toggle-dropdown"
      class="rounded-none hover:bg-accent"
      :icon="false"
      placement="top"
      :offset-distance="-34"
      :offset-skidding="-14"
    >
      <template #text>{{ time }} {{ selectedTimezone?.offset }}</template>

      <template #content="{ close }">
        <div class="scrollbar my-1 h-[450px] w-52 cursor-default overflow-auto">
          <div
            class="px-3 pb-1.5 pt-2 hover:bg-accent"
            :class="{
              'bg-selected text-white hover:bg-selected':
                time.timezone === selectedTimezone?.timezone
            }"
            v-for="(time, idx) in timezoneList"
            :key="time.id"
            @click="(close(), handleTimezone(idx, time.timezone))"
          >
            {{ time.name }}
          </div>
        </div>
      </template>
    </Dropdown>

    <ChartSettingsDropdown />
  </div>
</template>
