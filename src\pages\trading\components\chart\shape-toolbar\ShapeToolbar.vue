<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { useDragElement } from "@/composables/useDragElement";
import { ShapeInstanceError, ShapeToolInstanceError } from "@/helpers/errors";
import { useChartStore } from "@/store/chartStore";
import { useCandleStickStore } from "@/store/candleStickStore";

import LineColor from "./components/LineColor.vue";
import LineWidth from "./components/LineWidth.vue";
import LineType from "./components/LineType.vue";
import TextFontColor from "./components/TextFontColor.vue";
import TextFontSize from "./components/TextFontSize.vue";

import Tooltip from "@/components/Tooltip.vue";

import TrashCanOutlineSVG from "@/assets/svg/trash-can-outline.svg";

const chartStore = useChartStore();
const candleStickStore = useCandleStickStore();

const isShapeLocked = ref(false);

const { top: topPos, left: leftPos } = useDragElement(
  "shape-toolbar",
  "shape-toolbar-header"
);

onMounted(() => {
  if (!chartStore.selectedShape) {
    throw new ShapeInstanceError();
  }

  isShapeLocked.value = chartStore.selectedShape.locked;
});

watch([topPos, leftPos], ([topPos, leftPos]) => {
  chartStore.setShapeToolbarPosition(topPos, leftPos);
});

function handleShapeLock() {
  if (!chartStore.shapeToolInstance) {
    throw new ShapeToolInstanceError("Unable to lock the shape");
  }

  if (!chartStore.selectedShape) {
    throw new ShapeInstanceError("Unable to lock the shape");
  }

  isShapeLocked.value = chartStore.shapeToolInstance.toggleShapeLock(
    chartStore.selectedShape.uuid
  );
}

function deleteShape() {
  if (!chartStore.shapeToolInstance) {
    throw new ShapeToolInstanceError("Unable to delete the shape");
  }

  if (!chartStore.selectedShape) {
    throw new ShapeInstanceError("Unable to delete the shape");
  }

  chartStore.shapeToolInstance.deleteShape(chartStore.selectedShape.uuid);

  const shapeName = candleStickStore.symbol + "_" + candleStickStore.interval;

  chartStore.shapeToolInstance.saveShapes(shapeName);

  chartStore.shapeToolInstance.saveShapes();
}
</script>

<template>
  <div
    id="shape-toolbar"
    class="absolute flex -translate-x-1/2 rounded-md border bg-white shadow-lg"
    :style="{
      left: chartStore.shapeToolbarPosition.left + 'px',
      top: chartStore.shapeToolbarPosition.top + 'px'
    }"
  >
    <div id="shape-toolbar-header" class="flex cursor-grab items-center px-3">
      <FontAwesomeIcon icon="fa-solid fa-grip-vertical" />
    </div>

    <div class="flex gap-x-1 p-0.5">
      <template v-if="chartStore.selectedShape?.type !== 'text'">
        <LineColor />

        <LineWidth />

        <LineType />
      </template>

      <template v-else>
        <TextFontColor />

        <TextFontSize />
      </template>

      <Tooltip
        id="shape-toolbar-shape-lock-tooltip"
        trigger-id="shape-toolbar-shape-lock-trigger-tooltip"
        class="px-2 hover:bg-accent"
        :class="{ 'bg-accent': isShapeLocked }"
        @click="handleShapeLock"
      >
        <template #trigger>
          <FontAwesomeIcon
            :icon="isShapeLocked ? 'fa-solid fa-lock' : 'fa-solid fa-unlock'"
          />
        </template>

        <template #content>
          <template v-if="isShapeLocked">Unlock</template>

          <template v-else>Lock</template>
        </template>
      </Tooltip>

      <Tooltip
        id="shape-toolbar-shape-delete-tooltip"
        trigger-id="shape-toolbar-shape-delete-trigger-tooltip"
        class="px-2 hover:bg-accent"
        @click="deleteShape"
      >
        <template #trigger>
          <TrashCanOutlineSVG />
        </template>

        <template #content>Remove</template>
      </Tooltip>
    </div>
  </div>
</template>
