import { Socket, io } from "socket.io-client";

export let chatSocket: Socket | null;

export function initializeChatSocket(): Promise<Socket> {
  return new Promise((resolve, reject) => {
    if (chatSocket) {
      resolve(chatSocket);
      return;
    }

    
console.log(import.meta.env.VITE_APP_SOCKET_CHAT_URL);

if(import.meta.env.VITE_APP_ENV=="Local"){
  chatSocket = io(import.meta.env.VITE_APP_SOCKET_CHAT_URL, {
    autoConnect: true, 
    // path: "/chat/socket.io", 
    transports: ["websocket"], 
  });
}else{
  chatSocket = io(import.meta.env.VITE_APP_SOCKET_CHAT_URL, {
    autoConnect: true, 
    path: "/chat/socket.io", 
    transports: ["websocket"], 
  });
}
// chatSocket = io(import.meta.env.VITE_APP_SOCKET_CHAT_URL);

    const socket = chatSocket;

    const timeout = setTimeout(() => {
      reject(new Error("Socket connection timeout"));
    }, 10000);

    socket.on("connect", () => {
      if (import.meta.env.DEV) {
        console.log("Chat Socket Connection Established");
      }
      clearTimeout(timeout);
      resolve(socket);
    });

    socket.on("connect_error", (err) => {
      if (import.meta.env.DEV) {
        console.log("Chat Socket Error: ", err);
      }
      clearTimeout(timeout);
      reject(err);
    });

    socket.on("disconnect", () => {
      if (import.meta.env.DEV) {
        console.log("Chat Socket Connection Disconnected");
      }
    });
  });
}
