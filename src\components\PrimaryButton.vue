<script setup lang="ts">
import Spinner from "@/components/Spinner.vue";

defineProps<{
  loading?: boolean;
  disabled?: boolean;
}>();
</script>

<template>
  <button
    type="button"
    class="rounded-md bg-primary px-2 pb-1.5 pt-2 text-sm font-medium text-white hover:bg-secondary"
    :class="{ 'opacity-80': loading, 'bg-blue-400': disabled }"
    :disabled="loading || disabled"
  >
    <Spinner class="!h-5 !w-5" v-if="loading" />

    <template v-else>
      <slot></slot>
    </template>
  </button>
</template>
