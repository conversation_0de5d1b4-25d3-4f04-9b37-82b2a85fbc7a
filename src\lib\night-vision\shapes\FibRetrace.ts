import { TCoreData } from "../types";
import { Line } from "./LineTool";
import { DistanceUtils } from "./utils/DistanceUtils";
import { DrawUtils } from "./utils/DrawUtils";
import { PreferenceUtils } from "./utils/PreferencesUtils";
import { IBaseShape } from "./shapes/BaseShape";
import { extendLine } from "./utils/fns";
import { DrawUtils2 } from "./utils/DrawUtils2";
type BasicPoint = { x: number; y: number };

function drawLineWithText(
  ctx: CanvasRenderingContext2D,
  p1: BasicPoint,
  p2: BasicPoint,
  styles: typeof DrawUtils.lineStyles & {
    textPosition: "left" | "right" | "center";
    font: string;
    fontSize: number;
  },
  text: string,
  linePoints: BasicPoint[],
  extend: {
    left: boolean;
    right: boolean;
  }
) {
  DrawUtils.drawLine(ctx, p1, p2, styles);
  const textM = ctx.measureText(text);
  let xPos = 0,
    yPos = p1.y + styles.fontSize / 3;
  switch (styles.textPosition) {
    case "left":
      xPos =
        (linePoints[0].x > linePoints[1].x
          ? linePoints[1].x
          : linePoints[0].x) -
        10 -
        textM.width;
      if (extend.left) {
        yPos -= 8;
      }
      break;
    default:
      xPos =
        (linePoints[0].x > linePoints[1].x
          ? linePoints[0].x
          : linePoints[1].x) + 10;
      if (extend.right) {
        yPos -= 8;
      }
  }

  ctx.save();
  ctx.fillStyle = styles.stroke;
  ctx.font = `${styles.fontSize}px ${styles.font}`;
  ctx.fillText(text, xPos, yPos, 200);
  ctx.restore();
}

/**
 *  FibRetrace class implements the functionality to calculate and draw Fibonacci retracement lines
 *  on a trading chart.
 *
 *  Fibonacci retracement is a technical analysis tool used to identify potential levels of support and resistance
 *  based on the Fibonacci sequence.
 *
 *  By utilizing predefined Fibonacci ratios, such as 0.236, 0.382, 0.618, 0.786, and 1.0, the FibRetrace class
 *  calculates price levels where a retracement in the asset's price movement is likely to occur.
 *
 *  These levels are then plotted on the trading chart, providing traders with valuable insights
 *  for making informed decisions regarding entry, exit, and risk management strategies.
 */

export class FibRetrace extends Line {
  draggingPoint: { x: number; y: number } | null = null;
  dragging: boolean = false;

  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (line: IBaseShape) => boolean
  ) {
    super($core, uuid, points, screenPoints, onSelect);
    this.type = "fib-level";
    this.properties = PreferenceUtils["fib-level"];
  }

  get levelScreenPoints() {
    const p = this.properties as (typeof PreferenceUtils)["fib-level"];
    const lp = p.levelProperties;
    const fl = p.fibLevels;

    const points = [this.points[0].screen, this.points[1].screen];
    let diffY = points[1].y - points[0].y;
    if (lp.reverse_order) {
      points.reverse();
      diffY *= -1;
    }

    const levels = fl
      .filter((l) => l.level_show)
      .map((level) => {
        const yPos = points[0].y + diffY * level.level_position;
        let start = {
          x: points[0].x,
          y: yPos
        };

        let end = {
          x: points[1].x,
          y: yPos
        };

        const w = this.$core.layout.width;
        const h = this.$core.layout.height;
        if (lp.extend_dir_1) start = extendLine(end, start, w, h);
        if (lp.extend_dir_2) end = extendLine(start, end, w, h);

        return { start: start, end: end, ...level };
      });
    return levels;
  }

  cursorOverLine(): boolean {
    const levels = this.levelScreenPoints;

    const cursor = this.$core.cursor;

    return (
      DistanceUtils.isCursorOnLine(
        this.screenPoints[0],
        this.screenPoints[1],
        cursor
      ) ||
      levels.some((level) =>
        DistanceUtils.isCursorOnLine(level.start, level.end, cursor)
      )
    );
  }
  drawDetails(ctx: CanvasRenderingContext2D): void {
    return;
  }

  draw(ctx: CanvasRenderingContext2D): void {
    const p = this.properties as (typeof PreferenceUtils)["fib-level"];
    const lp = p.levelProperties;

    const points = [this.points[0].screen, this.points[1].screen];
    let diffY = points[1].y - points[0].y;
    if (lp.reverse_order) {
      points.reverse();
      diffY *= -1;
    }

    const levels = this.levelScreenPoints;

    levels.forEach((level, i) => {
      const levelColor = lp.level_line_color ?? level.level_line_color;

      if (i !== 0) {
        const prevLevelY = levels[i - 1].start.y;
        const currLevelY = level.start.y;
        ctx.save();
        ctx.fillStyle = levelColor.substring(0, 7) + "05";
        ctx.fillRect(
          points[0].x,
          currLevelY,
          points[1].x - points[0].x,
          prevLevelY - currLevelY
        );
        ctx.fill();
        ctx.restore();
      }

      drawLineWithText(
        ctx,
        level.start,
        level.end,
        {
          stroke: levelColor,
          dash: DrawUtils2.getLineDash(lp.level_line_type),
          width: lp.level_line_width,
          textPosition: lp.level_text_position,
          font: p.levelTextProperties.font,
          fontSize: p.levelTextProperties.font_size
        },
        level.level_position.toString(),
        points,
        {
          left: lp.extend_dir_1,
          right: lp.extend_dir_2
        }
      );
    });

    super.draw(ctx);
  }

  toJSON: () => { [x: string]: any } = () => {
    return {
      uuid: this.uuid,
      time: this.points.map((p) => p.time),
      type: this.type,
      points: this.points.map((p) => ({ x: p.x, y: p.y })),
      properties: this.properties
    };
  };
}
