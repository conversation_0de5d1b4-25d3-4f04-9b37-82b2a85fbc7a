import { ref, watch } from "vue";
import { defineStore } from "pinia";

import {
  getFromLocalStorage,
  storeToLocalStorage
} from "@/helpers/localStorage";
import { getTimeZoneOffsetInHours } from "@/helpers/dateConversion";
import { useUserStore } from "@/store/userStore";
import {
  IOHLC,
  Symbol,
  Broker,
  BrokerInfo,
  IEAAccount,
  BoxPlotData,
  IActiveOrders,
  IPlacedOrders,
  IChartSettings,
  MarketWatchSymbol
} from "@/types";
import { useChartStore } from "./chartStore";

export const useCandleStickStore = defineStore("candlestick", () => {
  const userStore = useUserStore();
  const chartStore = useChartStore();

  // Default chart settings
  const chartSettings = ref<IChartSettings>({
    symbol: "EURUSD",
    symbol_broker: "Blueberry Markets Pty Ltd",
    interval: "PERIOD_M1",
    favoriteIntervals: [
      "PERIOD_M1",
      "PERIOD_M5",
      "PERIOD_M15",
      "PERIOD_H1",
      "PERIOD_D1"
    ],
    timezone: "UTC",
    volume: true,
    show_market_watch_detail: true,
    market_watch_settings: ["bid_ask", "daily_change", "pips", "daily_price"],
    marketCloseLine: true,
    asiaMarket: true,
    londonMarket: true,
    newYorkMarket: true,
    e_calendar_timezone: "UTC",
    heatmap_show_pips: false,
    open_trades_headers: []
  });
  const symbolList = ref<Symbol[]>([]);
  const symbol = ref("EURUSD");
  const symbolBroker = ref("Blueberry Markets Pty Ltd");
  const interval = ref("PERIOD_M1");
  const timezone = ref("UTC");
  const brokerList = ref<Broker[]>([]);

  // Box Plot
  const boxPlot = ref({
    boxPlotHigh: false,
    boxPlotLow: false,
    symbol: "",
    high_price: 0,
    low_price: 0
  });
  const boxPlotData = ref<BoxPlotData | null>(null);

  // OHLC
  const ohlc = ref<IOHLC[]>([]);

  // Market Watch
  const marketWatchOnlineStatus = ref(false);
  const marketWatchLoader = ref(false);
  const connectionTimer = ref(0);
  const brokerInfo = ref<BrokerInfo | null>(null);
  const eaAccount = ref<IEAAccount | null>(null);
  const activeOrders = ref<IActiveOrders[]>([]);
  const placedOrders = ref<IPlacedOrders[]>([]);
  const marketWatchSymbolList = ref<MarketWatchSymbol[]>([]);
  const marketWatchSymbol = ref("");
  const refMarginCurrency = ref(0);
  const bidPrice = ref(0);
  const bidPriceColor = ref("");
  const askPrice = ref(0);
  const askPriceColor = ref("");
  const dailyChange = ref(0);
  const dailyChangeColor = ref("");
  const sessionOpenPrice = ref(0);
  const bidHighPrice = ref(0);
  const bidLowPrice = ref(0);
  const spread = ref(0);
  const contractSize = ref("");
  const maxVolume = ref(0);
  const minVolume = ref(0);
  const volumeStep = ref(1);
  const digit = ref(0);
  const auto = ref(0.125);
  const updateTP1 = ref(false);
  const tp1 = ref(0);
  // set to 0 because label calculation uses tp1_vol to see if tp1 is checked
  const tp1_vol = ref(0);
  const updateSLTrial = ref(false);
  const slTrail = ref(20);

  intializeChartSettings();

  function intializeChartSettings() {
    const settings: IChartSettings = getFromLocalStorage(
      "chart_settings",
      true
    );

    // If new settings are added, we must add those settings to local storage as well.
    if (settings) {
      for (const [k, v] of Object.entries(chartSettings.value)) {
        // @ts-expect-error
        if (settings[k] === undefined) {
          // @ts-expect-error
          settings[k] = v;
        }
      }

      chartSettings.value = settings;

      symbol.value = settings.symbol;
      symbolBroker.value = settings.symbol_broker;
      interval.value = settings.interval;
      timezone.value = settings.timezone;
    }

    storeToLocalStorage("chart_settings", chartSettings.value, true);
  }

  function storeChartSettings(settings: Partial<IChartSettings>) {
    const newSettings = {
      ...chartSettings.value,
      ...settings
    };

    chartSettings.value = newSettings;

    storeToLocalStorage("chart_settings", newSettings, true);
  }

  watch(timezone, (newTimezone) => {
    if (chartStore.chart === null) {
      return;
    }

    const offset = getTimeZoneOffsetInHours(newTimezone);

    chartStore.chart.timezone = offset;

    chartStore.chart.update();
  });

  let marketWatchTimer: NodeJS.Timeout;

  watch(connectionTimer, (timer) => {
    if (marketWatchSymbolList.value.length !== 0 && timer > 10) {
      resetMarketWatch();
    }

    marketWatchOnlineStatus.value =
      marketWatchSymbolList.value.length !== 0 && timer < 10;
  });

  function createMarketWatchTimer() {
    marketWatchTimer = setInterval(() => {
      connectionTimer.value++;
    }, 1000);
  }

  function resetMarketWatchTimer() {
    connectionTimer.value = 0;
  }

  function clearMarketWatchTimer() {
    clearInterval(marketWatchTimer);
  }

  function resetMarketWatch() {
    userStore.eaAccessToken = null;

    marketWatchOnlineStatus.value = false;
    marketWatchLoader.value = false;
    connectionTimer.value = 0;
    brokerInfo.value = null;
    eaAccount.value = null;
    activeOrders.value = [];
    placedOrders.value = [];
    marketWatchSymbolList.value = [];

    document.title = import.meta.env.VITE_APP_TITLE;
  }

  function reset() {
    resetMarketWatch();
  }

  return {
    chartSettings,
    symbol,
    symbolBroker,
    symbolList,
    interval,
    timezone,
    brokerList,
    boxPlot,
    boxPlotData,
    ohlc,
    marketWatchOnlineStatus,
    marketWatchLoader,
    connectionTimer,
    brokerInfo,
    eaAccount,
    activeOrders,
    placedOrders,
    marketWatchSymbolList,
    marketWatchSymbol,
    refMarginCurrency,
    bidPrice,
    bidPriceColor,
    askPrice,
    askPriceColor,
    dailyChange,
    dailyChangeColor,
    sessionOpenPrice,
    bidHighPrice,
    bidLowPrice,
    spread,
    contractSize,
    maxVolume,
    minVolume,
    volumeStep,
    digit,
    auto,
    tp1,
    tp1_vol,
    updateTP1,
    slTrail,
    updateSLTrial,
    storeChartSettings,
    createMarketWatchTimer,
    resetMarketWatchTimer,
    clearMarketWatchTimer,
    resetMarketWatch,
    reset
  };
});
