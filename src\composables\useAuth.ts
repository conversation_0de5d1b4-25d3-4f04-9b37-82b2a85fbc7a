import { useUserStore } from "@/store/userStore";

import { axios } from "@/api";
import { IUserSession } from "@/types";

export async function useAuth() {
  const userStore = useUserStore();

  try {
    if (
      userStore.isUserLoggedIn &&
      localStorage.getItem("loggedOut") === "false"
    ) {
      return true;
    }

    const authResp = await axios.get("/auth/status");

    const authStatus: boolean = authResp.data.success;

    if (!authStatus) {
      return false;
    }

    const resp = await axios.get("/user");

    userStore.user = resp.data.data;

    const session = resp.data.data.sessions.find(
      (session: IUserSession) => session.login_type === "frontend"
    )!;

    userStore.userAccessToken = session.access_token;

    userStore.isUserLoggedIn = authStatus;

    localStorage.setItem("loggedOut", "false");

    return true;
  } catch (e) {
    console.error(e);
  }

  return false;
}
