<script setup lang="ts">
import { ref } from "vue";
import { number, object, string } from "yup";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { axios } from "@/api";
import { getClientValidationErrors } from "@/helpers/getErrors";
import { useCandleStickStore } from "@/store/candleStickStore";

import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import RadioButton from "@/components/RadioButton.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const candleStickStore = useCandleStickStore();

const riskTrade = ref({
  target: 10,
  riskPerTrade: 0.25,
  totalTrades: 50,
  breach: 10,
  equityBalance: "balance",
  riskRewardRatio: 3
});
const calculatedResult = ref({
  required_wins: 0,
  minimum_total_trades: 0,
  consecutive_losses_before_breach: 0
});
const btnLoading = ref(false);
const validationErrors = ref<typeof riskTrade.value | null>(null);
const serverErrors = ref<string[]>([]);

const validationSchema = object({
  target: number()
    .min(0.01, "Target must be greater than 0.01%.")
    .max(100, "Target must be less than 100%.")
    .typeError("Target must be a number.")
    .required("Target is required."),
  riskPerTrade: number()
    .min(0.01, "Risk Per Trade must be greater than 0.01%.")
    .max(100, "Risk Per Trade must be less than 100%.")
    .typeError("Risk Per Trade must be a number.")
    .required("Risk Per Trade is required."),
  totalTrades: number()
    .min(1)
    .typeError("Total Trades must be a number.")
    .required("Total Trades is required."),
  breach: number()
    .min(0.01, "Breach must be greater than 0.01%.")
    .max(100, "Breach must be less than 100%.")
    .typeError("Breach must be a number.")
    .required("Breach is required."),
  equityBalance: string().required("This field is required."),
  riskRewardRatio: number()
    .min(0.01, "Risk Reward Ratio must be greater than 0.01.")
    .typeError("Risk Reward Ratio must be a number.")
    .required("Risk Reward Ratio is required.")
});

async function handleCalculation() {
  try {
    validationErrors.value = null;
    serverErrors.value = [];

    btnLoading.value = true;

    const validated = await validationSchema.validate(riskTrade.value, {
      abortEarly: false
    });

    let equityBalance = candleStickStore.eaAccount?.balance;

    if (validated.equityBalance === "equity") {
      equityBalance = candleStickStore.eaAccount?.equity;
    }

    const resp = await axios.post("analytics/calculate_trades", {
      mt5Id: candleStickStore.eaAccount?.mt5_id,
      targetPercent: validated.target,
      riskPerTradePercent: validated.riskPerTrade,
      totalTrades: validated.totalTrades,
      breachPercent: validated.breach,
      equity_balance: equityBalance,
      riskRewardRatio: validated.riskRewardRatio
    });

    calculatedResult.value = resp.data.data;
  } catch (e) {
    validationErrors.value = getClientValidationErrors(e);
  } finally {
    btnLoading.value = false;
  }
}
</script>

<template>
  <div
    class="mt-3 rounded-md border"
    data-accordion="collapse"
    id="analytics-risk-trade-accordion"
  >
    <div id="analytics-risk-trade-accordion-header">
      <button
        type="button"
        aria-expanded="true"
        class="flex w-full cursor-pointer items-center justify-between bg-accent p-3 font-medium hover:text-primary"
        aria-controls="analytics-risk-trade-accordion-header"
        data-accordion-target="#analytics-risk-trade-accordion-body"
      >
        <div class="text-base font-semibold">Risk Trade</div>

        <FontAwesomeIcon icon="fa-solid fa-chevron-down" />
      </button>
    </div>

    <div
      class="mt-3 px-3"
      id="analytics-risk-trade-accordion-body"
      aria-labelledby="analytics-risk-trade-accordion-header"
    >
      <div class="divide-y">
        <div class="flex items-center justify-between pb-2">
          <div>Required wins</div>

          <div class="font-medium">{{ calculatedResult.required_wins }}</div>
        </div>

        <div class="flex items-center justify-between py-2">
          <div>Win %</div>

          <div class="font-medium">
            <template
              v-if="riskTrade.totalTrades <= calculatedResult.required_wins"
            >
              100%
            </template>

            <template v-else>
              {{
                Math.round(
                  (calculatedResult.required_wins / riskTrade.totalTrades) * 100
                )
              }}%
            </template>
          </div>
        </div>

        <div class="flex items-center justify-between py-2">
          <div>Minimum trade</div>

          <div class="font-medium">
            {{ calculatedResult.minimum_total_trades }}
          </div>
        </div>

        <div class="flex items-center justify-between py-2">
          <div>Consec Losses Before Breach</div>

          <div class="font-medium">
            {{ calculatedResult.consecutive_losses_before_breach }}
          </div>
        </div>
      </div>

      <form class="mt-2" @submit.prevent="handleCalculation">
        <div class="grid grid-cols-2 gap-x-3">
          <div>
            <InputLabel for="target">Target</InputLabel>

            <div
              class="flex items-center gap-x-2 rounded-lg border border-gray-300 bg-gray-50 pr-3 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500"
              :class="{
                '!border-danger focus-within:ring-red-500':
                  validationErrors?.target
              }"
            >
              <InputText
                id="target"
                placeholder="Target"
                class="border-0 focus:ring-0"
                v-model="riskTrade.target"
              />

              <span>%</span>
            </div>
          </div>

          <div>
            <InputLabel for="risk_trade">Risk Per Trade</InputLabel>

            <div
              class="flex items-center gap-x-2 rounded-lg border border-gray-300 bg-gray-50 pr-3 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500"
              :class="{
                '!border-danger focus-within:ring-red-500':
                  validationErrors?.riskPerTrade
              }"
            >
              <InputText
                id="risk_trade"
                placeholder="Risk Trade"
                class="border-0 focus:ring-0"
                v-model="riskTrade.riskPerTrade"
              />

              <span>%</span>
            </div>
          </div>
        </div>

        <div
          class="mt-1 px-2 text-xs text-danger"
          v-if="validationErrors?.target"
        >
          {{ validationErrors.target }}
        </div>

        <div
          class="mt-1 px-2 text-xs text-danger"
          v-if="validationErrors?.riskPerTrade"
        >
          {{ validationErrors.riskPerTrade }}
        </div>

        <div class="mt-3 grid grid-cols-2 gap-x-3">
          <div>
            <InputLabel for="total_trades">Total Trades</InputLabel>

            <InputText
              id="total_trades"
              placeholder="Total Trades"
              :class="{
                '!border-danger focus:!ring-red-500':
                  validationErrors?.totalTrades
              }"
              v-model="riskTrade.totalTrades"
            />
          </div>

          <div>
            <InputLabel for="breach">Breach</InputLabel>

            <div
              class="flex items-center gap-x-2 rounded-lg border border-gray-300 bg-gray-50 pr-3 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500"
              :class="{
                '!border-danger focus-within:ring-red-500':
                  validationErrors?.breach
              }"
            >
              <InputText
                id="breach"
                placeholder="Breach"
                class="border-0 focus:ring-0"
                v-model="riskTrade.breach"
              />

              <span>%</span>
            </div>
          </div>
        </div>

        <div
          class="mt-1 px-2 text-xs text-danger"
          v-if="validationErrors?.totalTrades"
        >
          {{ validationErrors.totalTrades }}
        </div>

        <div
          class="mt-1 px-2 text-xs text-danger"
          v-if="validationErrors?.breach"
        >
          {{ validationErrors.breach }}
        </div>

        <div class="mt-3 flex gap-x-7">
          <div class="flex items-center gap-x-2">
            <RadioButton
              id="balance"
              value="balance"
              v-model="riskTrade.equityBalance"
            />

            <InputLabel for="balance" class="!mb-0">Balance</InputLabel>
          </div>

          <div class="flex items-center gap-x-2">
            <RadioButton
              id="equity"
              value="equity"
              v-model="riskTrade.equityBalance"
            />

            <InputLabel for="equity" class="!mb-0">Equity Balance</InputLabel>
          </div>
        </div>

        <div
          class="mt-1 px-2 text-xs text-danger"
          v-if="validationErrors?.equityBalance"
        >
          {{ validationErrors.equityBalance }}
        </div>

        <div class="mt-3">
          <InputLabel for="risk_reward_ratio">
            Risk Reward Ratio (1:x)
          </InputLabel>

          <InputText
            id="risk_reward_ratio"
            placeholder="Risk Reward Ratio"
            :class="{
              '!border-danger focus:!ring-red-500':
                validationErrors?.riskRewardRatio
            }"
            v-model="riskTrade.riskRewardRatio"
          />
        </div>

        <div
          class="mt-1 px-2 text-xs text-danger"
          v-if="validationErrors?.riskRewardRatio"
        >
          {{ validationErrors.riskRewardRatio }}
        </div>

        <div class="my-3">
          <PrimaryButton
            type="submit"
            class="flex w-full justify-center"
            :loading="btnLoading"
          >
            Calculate
          </PrimaryButton>
        </div>
      </form>
    </div>
  </div>
</template>
