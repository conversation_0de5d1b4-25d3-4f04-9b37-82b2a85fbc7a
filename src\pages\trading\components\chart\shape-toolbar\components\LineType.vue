<script setup lang="ts">
import { onMounted, ref } from "vue";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";
import { ILine } from "@/lib/night-vision/shapes/base/shape-properties.types";

import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";
import { useChartStore } from "@/store/chartStore";

import DropdownTooltip from "@/components/DropdownTooltip.vue";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"trend-line"> | undefined =
  chartStore.selectedShape;

const lineTypeDropdown = ref(false);
const lineTypes = ref([
  {
    type: "solid",
    name: "Normal"
  },
  {
    type: "dashed",
    name: "Dashed"
  },
  {
    type: "dotted",
    name: "Dotted"
  }
]);
const selectedLineType = ref("");

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set shape line type");
  }

  selectedLineType.value = selectedShape.properties.lineProperties.line_type;
});

function handleLineType(lineType: ILine["line_type"]) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to change shape line type");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to change shape line type");
  }

  selectedLineType.value = lineType;

  selectedShape.setProperty("lineProperties", "line_type", lineType);

  chartStore.chart?.update();
}
</script>

<template>
  <DropdownTooltip
    dropdown-id="shape-toolbar-line-type-dropdown"
    dropdown-toggle-id="shape-toolbar-line-type-toggle-dropdown"
    tooltip-id="shape-toolbar-line-type-tooltip"
    tooltip-trigger-id="shape-toolbar-line-type-trigger-tooltip"
    class="flex h-[40px] items-center rounded px-2 text-sm hover:bg-accent"
    :class="{ 'bg-accent': lineTypeDropdown }"
    :icon="false"
    :dropdown-offset-distance="2"
    :dropdown-offset-skidding="64"
    @show="lineTypeDropdown = true"
    @hide="lineTypeDropdown = false"
  >
    <template #text>
      <div
        class="w-5 border-b-2 border-black"
        :style="{
          borderStyle: selectedLineType
        }"
      ></div>
    </template>

    <template #dropdown-content="{ close }">
      <div class="my-1 w-40 cursor-default">
        <div
          class="flex items-center gap-x-3 px-3 pb-1.5 pt-2 hover:bg-accent"
          :class="{
            'bg-selected text-white hover:bg-selected':
              selectedLineType === type
          }"
          v-for="{ type, name } in lineTypes"
          :key="type"
          @click="(close(), handleLineType(type as ILine['line_type']))"
        >
          <div
            class="w-5 border-b-2 border-black"
            :class="{
              '!border-white': selectedLineType === type
            }"
            :style="{ borderStyle: type }"
          ></div>
          {{ name }} Line
        </div>
      </div>
    </template>

    <template #tooltip-content>Style</template>
  </DropdownTooltip>
</template>
