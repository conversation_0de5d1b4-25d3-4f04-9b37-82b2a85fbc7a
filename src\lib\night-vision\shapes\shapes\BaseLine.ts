import { AvailableShapes } from "../../groups/ShapeTool";
import { TCoreData } from "../../types";
import { BasicPoint } from "../base/Types";
import { BaseShape, IBaseShape, IBaseShapeOptions } from "./BaseShape";
import { DrawUtils2 } from "../utils/DrawUtils2";
import { PreferenceUtils } from "../utils/PreferencesUtils";
import { extendLine, formatCursorX } from "../utils/fns";
import { Point } from "./Point";
import { BaseShapeInterface } from "../base/shape.types";

export interface IBaseLine
  extends BaseShapeInterface<
    Exclude<AvailableShapes, "text" | "fib-level" | "no-shape">
  > {
  readonly endPoints: BasicPoint[];
  readonly screenPoints: BasicPoint[];
  readonly midPoint: BasicPoint;
  toJSON: () => Record<string, any>;
}

export interface INightVisionDraw {
  draw(ctx: CanvasRenderingContext2D): void;
  drawSidebar(
    ctx: CanvasRenderingContext2D,
    _: any,
    scale: { prec: number }
  ): void;
  drawBotbar(ctx: CanvasRenderingContext2D): void;
}

export class BaseLine
  extends BaseShape<"trend-line">
  implements IBaseLine, INightVisionDraw
{
  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (line: IBaseShape) => boolean,
    properties?: IBaseLine["properties"]
  ) {
    super($core, uuid, points, screenPoints, onSelect);
    this.properties = properties ?? PreferenceUtils["trend-line"];

    window.addEventListener("mouseup", this.handleGlobalMouseUp.bind(this));
  }

  get isValid(): boolean {
    const layout = this.$core.layout;
    const yRange = layout.$hi - layout.$lo;
    return (
      this.points[0].x - this.points[1].x !== 0 ||
      Math.abs(this.points[0].y - this.points[1].y) > yRange / 100
    );
  }

  get endPoints() {
    return [this.points[0].screen, this.points[1].screen];
  }

  get screenPoints() {
    // Retrieve the extend direction flags from the properties
    const { extend_dir_1, extend_dir_2 } = this.properties.lineProperties;
    const { height, width } = this.$core.layout;

    let start = this.endPoints[0];
    let end = this.endPoints[1];

    if (extend_dir_1) start = extendLine(end, start, width, height);
    if (extend_dir_2) end = extendLine(start, end, width, height);

    // Return the modified or original screen points
    return [start, end];
  }

  get midPoint() {
    const sp = this.endPoints;
    return {
      x: (sp[0].x + sp[1].x) / 2,
      y: (sp[0].y + sp[1].y) / 2
    };
  }

  private handleGlobalMouseUp() {
    if (this.dragging) {
      this.dragging = false;
      this.draggingPoint = null;
      this.$core.hub.events.emit("scroll-lock", false);
      this.$core.hub.events.emit("update-layout");
    }
  }

  draw(ctx: CanvasRenderingContext2D): void {
    this.drawLine(ctx);
    this.drawEndPoints(ctx);
    this.drawMidPoint(ctx);

    if (this._showDetails) {
      this.drawDetails(ctx);
    }
  }

  // Draw Utils
  drawLine(ctx: CanvasRenderingContext2D) {
    const points = this.screenPoints;
    const lp = this.properties.lineProperties;

    ctx.save();
    ctx.strokeStyle = lp.line_color;
    ctx.setLineDash(DrawUtils2.getLineDash(lp.line_type));
    ctx.lineWidth = lp.line_width;
    DrawUtils2.drawLine(ctx, points[0], points[1]);
    ctx.restore();
  }

  drawEndPoints(ctx: CanvasRenderingContext2D) {
    const points = this.endPoints;

    const lp = this.properties.lineProperties;
    const pd = this.properties.pointDisplay;
    const pp = this.properties.pointProperties;

    if (pp.point_position === "midpoint") return;

    const draw = (width: number) => {
      if (pp.point_shape === "circle") {
        DrawUtils2.drawCirclePoint(ctx, points[0], width);
        DrawUtils2.drawCirclePoint(ctx, points[1], width);
      }
      if (pp.point_shape === "square") {
        DrawUtils2.drawSquarePoint(ctx, points[0], width);
        DrawUtils2.drawSquarePoint(ctx, points[1], width);
      }
      if (pp.point_shape === "arrow") {
        DrawUtils2.drawArrow(ctx, points[0], points[1], width, this.type);
      }
    };

    ctx.save();
    ctx.strokeStyle = lp.line_color;
    ctx.lineWidth = lp.line_width;
    if (this.selected && pd.show_point_selected) {
      const width = pp.point_width_selected;
      draw(width);
    } else if (this.hovered && pd.show_point_hover) {
      const width = pp.point_width_hover;
      draw(width);
    } else if (pd.show_point_default) {
      const width = pp.point_width_default;
      draw(width);
    }
    ctx.restore();
  }

  drawMidPoint(ctx: CanvasRenderingContext2D) {
    const point = this.midPoint;

    const lp = this.properties.lineProperties;
    const pd = this.properties.pointDisplay;
    const pp = this.properties.pointProperties;

    if (pp.point_position !== "midpoint") return;

    let width = 0;
    if (this.selected && pd.show_point_selected) {
      width = pp.point_width_selected;
    } else if (this.hovered && pd.show_point_hover) {
      width = pp.point_width_hover;
    } else if (pd.show_point_default) {
      width = pp.point_width_default;
    }

    if (width)
      Point.drawPoint(
        ctx,
        point,
        pp.point_shape,
        width,
        lp.line_color,
        lp.line_width
      );
  }

  drawSidebar(
    ctx: CanvasRenderingContext2D,
    _: any,
    scale: { prec: number }
  ): void {
    const lp = this.properties.lineProperties;
    const sd = this.properties.sidebarDisplay;

    const drawInBetween = () => {
      if (this.points.length === 2) {
        ctx.save();
        ctx.fillStyle =
          lp.line_color.length > 7
            ? lp.line_color.substring(0, 7) + "11"
            : lp.line_color + "11";
        ctx.fillRect(
          0,
          this.points[0].screenY,
          500,
          this.points[1].screenY - this.points[0].screenY
        );
        ctx.restore();
      }
    };

    const draw = () => {
      drawInBetween();
      ctx.fillStyle = lp.line_color;
      this.points.forEach((point) => {
        const label = point.y.toFixed(scale.prec);
        ctx.save();
        DrawUtils2.drawSidebar(ctx, label, point.screenY);
        ctx.restore();
      });
    };

    if (sd.show_sidebar_default) draw();
    else if (sd.show_sidebar_hover && this.hovered) draw();
    else if (sd.show_sidebar_selected && this.selected) draw();
  }

  drawBotbar(ctx: CanvasRenderingContext2D): void {
    const lp = this.properties.lineProperties;
    const sd = this.properties.sidebarDisplay;

    const drawInBetween = () => {
      if (this.points.length === 2) {
        ctx.save();
        ctx.fillStyle =
          lp.line_color.length > 7
            ? lp.line_color.substring(0, 7) + "11"
            : lp.line_color + "11";
        ctx.fillRect(
          this.points[0].screenX,
          0,
          this.points[1].screenX - this.points[0].screenX,
          500
        );
        ctx.restore();
      }
    };

    const draw = () => {
      drawInBetween();
      ctx.save();
      ctx.fillStyle = lp.line_color;
      this.points.forEach((point) => {
        const label = formatCursorX(
          point.time,
          this.$core.props.timeFrame,
          this.$core.props.timezone
        );
        DrawUtils2.drawBotbar(ctx, label, point.screenX);
      });
      ctx.restore();
    };

    if (sd.show_sidebar_default) draw();
    else if (sd.show_sidebar_hover && this.hovered) draw();
    else if (sd.show_sidebar_selected && this.selected) draw();
  }

  toJSON: () => { [x: string]: any } = () => {
    return {
      uuid: this.uuid,
      type: this.type,
      time: this.points.map((p) => p.time),
      points: this.points.map((p) => ({ x: p.x, y: p.y })),
      properties: this.properties,
      showDetails: this._showDetails
    };
  };

  getCoordinates(): { [x: string]: any } {
    return {
      endPoint1: { row: this.points[0].x, price: this.points[0].y },
      endPoint2: { row: this.points[1].x, price: this.points[1].y }
    };
  }

  setCoordinatesVal(name: string, value: any): boolean {
    if (value.row && value.price)
      switch (name) {
        case "endPoint1":
          this.points[0].x = Number(value.row);
          this.points[0].y = Number(value.price);
          return true;

        case "endPoint2": {
          this.points[1].x = Number(value.row);
          this.points[1].y = Number(value.price);
          return true;
        }
      }
    return false;
  }

  setCoordinates(name: string, value: any): boolean {
    const returnVal = this.setCoordinatesVal(name, value);
    if (returnVal) this.$core.hub.events.emit("update-layout");
    return returnVal;
  }

  destroy() {
    window.removeEventListener("mouseup", this.handleGlobalMouseUp.bind(this));
  }

  getOptions: () => IBaseShapeOptions<any> = () => {
    return {
      uuid: this.uuid,
      type: this.type as "trend-line",
      properties: this.properties as any,
      getCoordinates: this.getCoordinates.bind(this),
      setCoordinates: this.setCoordinates.bind(this),
      setProperty: this.setProperty.bind(this) as any,
      toggleDetails: this.toggleDetails,
      locked: this.locked,
      showLabelDetails: this.showLabelDetails,
      getLabelProperties: this.getLabelProperties.bind(this),
      deleteShape: () =>
        console.log(
          "Please use delete shape from the shapeTool for now. This is not implemented yet"
        ),
      lockShape: () =>
        console.log(
          "Please use lock shape from the shapeTool for now. This is not implemented yet"
        )
    } as unknown as IBaseShapeOptions<any>;
  };
}
