import { useChartStore } from "@/store/chartStore";
import { useCandleStickStore } from "@/store/candleStickStore";
import { animateChartRange } from "./utils";

type CandleStickInterval =
  | "PERIOD_M1"
  | "PERIOD_M5"
  | "PERIOD_M15"
  | "PERIOD_M30"
  | "PERIOD_H1"
  | "PERIOD_H4"
  | "PERIOD_D1"
  | "PERIOD_W1";

export function useChartFunctions() {
  const chartStore = useChartStore();
  const candleStickStore = useCandleStickStore();
  const interval = candleStickStore.interval as CandleStickInterval;

  const intervalGapMap: { [key in CandleStickInterval]: number } = {
    // Leave 5 units of bars to the right
    PERIOD_M1: 5 * 60000,
    PERIOD_M5: 5 * 5 * 60000,
    PERIOD_M15: 5 * 15 * 60000,
    PERIOD_M30: 5 * 30 * 60000,
    PERIOD_H1: 5 * 60 * 60000,
    PERIOD_H4: 5 * 4 * 60 * 60000,
    PERIOD_D1: 5 * 24 * 60 * 60000,
    PERIOD_W1: 5 * 7 * 24 * 60 * 60000
  };

  const handleZoomIn = () => {
    if (!chartStore.chart) {
      throw new Error("Cannot find chart");
    }

    const chart = chartStore.chart;

    const currentRange = chart.range;
    const currentDuration = currentRange[1] - currentRange[0];
    const newDuration = Math.max(10, currentDuration * 0.8);

    const newRange: [number, number] = [
      currentRange[1] - newDuration,
      currentRange[1]
    ];

    animateChartRange(chart, newRange);
  };

  const handleZoomOut = () => {
    if (!chartStore.chart) {
      throw new Error("Cannot find chart");
    }

    const chart = chartStore.chart;

    const currentRange = chart.range;
    const currentDuration = currentRange[1] - currentRange[0];
    let newDuration = currentDuration / 0.8;

    const MAX_ZOOM = chart.config["MAX_ZOOM"];

    if (newDuration === MAX_ZOOM) {
      return;
    } else if (newDuration > MAX_ZOOM) {
      newDuration = MAX_ZOOM;
    }

    const newRange: [number, number] = [
      currentRange[1] - newDuration,
      currentRange[1]
    ];

    animateChartRange(chart, newRange);
  };

  const handleChartMoveRight = () => {
    if (!chartStore.chart) {
      throw new Error("Cannot find chart");
    }

    const chart = chartStore.chart;
    const currentRange = chart.range;
    const duration = currentRange[1] - currentRange[0];
    const mainPane = chart.data.panes[0];
    const mainOverlay = mainPane.overlays.find((o: any) => o.main);

    if (!mainOverlay || !mainOverlay.data || mainOverlay.data.length === 0) {
      console.error("Main overlay data is not available");
      return null;
    }

    const dataLength = mainOverlay.data.length;
    const endPoint = chart.hub.indexBased
      ? dataLength + 3
      : mainOverlay.data[dataLength - 1][0] - 1 + intervalGapMap[interval];
    const startPoint = endPoint - duration;

    const newRange: [number, number] = [startPoint, endPoint];

    animateChartRange(chart, newRange);
  };

  return {
    handleZoomIn,
    handleZoomOut,
    handleChartMoveRight
  };
}
