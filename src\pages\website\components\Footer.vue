<script setup lang="ts">
import { useRouter } from "vue-router";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const router = useRouter();

function handleNavigation(routeName: string, hash: string) {
  router.push({ name: routeName, hash: hash });
}
</script>

<template>
  <footer id="footer" class="bg-tertiary pb-4 pt-6 text-white">
    <div class="container mx-auto grid grid-cols-12 items-center px-5 text-sm">
      <div
        class="order-2 col-span-12 py-5 text-center lg:order-1 lg:col-span-2 lg:py-0 lg:text-left"
      >
        © {{ new Date().getFullYear() }} Tradeawaay
      </div>

      <div class="order-1 col-span-12 lg:order-2 lg:col-span-8">
        <div class="flex flex-wrap justify-center gap-3">
          <router-link
            :to="{ name: 'home', hash: '#home' }"
            class="cursor-pointer underline-offset-8 hover:underline"
            @click="handleNavigation('home', '#home')"
          >
            Home
          </router-link>

          <span>|</span>

          <router-link
            :to="{ name: 'home', hash: '#features' }"
            class="cursor-pointer underline-offset-8 hover:underline"
            @click="handleNavigation('home', '#features')"
          >
            Features
          </router-link>

          <span>|</span>

          <router-link
            :to="{ name: 'home', hash: '#pricing' }"
            class="cursor-pointer underline-offset-8 hover:underline"
            @click="handleNavigation('home', '#pricing')"
          >
            Pricing
          </router-link>

          <span>|</span>

          <router-link
            :to="{ name: 'home', hash: '#faq' }"
            class="cursor-pointer underline-offset-8 hover:underline"
            @click="handleNavigation('home', '#faq')"
          >
            FAQ
          </router-link>

          <span>|</span>

          <router-link
            :to="{ name: 'home', hash: '#contact-us' }"
            class="cursor-pointer underline-offset-8 hover:underline"
            @click="handleNavigation('home', '#contact-us')"
          >
            Contact Us
          </router-link>

          <span>|</span>

          <router-link
            :to="{ name: 'community-videos' }"
            class="cursor-pointer underline-offset-8 hover:underline"
            @click="handleNavigation('h', '#faq')"
          >
            Education
          </router-link>
        </div>

        <div class="mt-3 flex justify-center gap-x-3">
          <router-link
            :to="{ name: 'privacy-policy' }"
            class="cursor-pointer underline-offset-8 hover:underline"
          >
            Privacy Policy
          </router-link>

          <span>|</span>

          <router-link
            :to="{ name: 'terms-of-service' }"
            class="cursor-pointer underline-offset-8 hover:underline"
          >
            Terms of Service
          </router-link>
        </div>
      </div>

      <div
        class="order-3 col-span-12 flex items-center justify-center gap-x-3 lg:order-3 lg:col-span-2 lg:justify-end"
      >
        <h6>Follow Us:</h6>

        <ul class="flex gap-x-3">
          <li>
            <a
              target="_blank"
              class="block py-1"
              href="https://www.facebook.com/cittaalgolabs"
            >
              <FontAwesomeIcon icon="fa-brands fa-facebook" size="lg" />
            </a>
          </li>

          <li>
            <a
              target="_blank"
              class="block py-1"
              href="https://www.youtube.com/@tradeawaay"
            >
              <FontAwesomeIcon icon="fa-brands fa-youtube" size="lg" />
            </a>
          </li>
        </ul>
      </div>
    </div>
  </footer>
</template>
