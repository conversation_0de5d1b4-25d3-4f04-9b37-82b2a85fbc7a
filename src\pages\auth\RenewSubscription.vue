<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { object, string } from "yup";
import { toast } from "vue3-toastify";
import {
  Stripe,
  loadStripe,
  StripeError as IStripeError,
  StripeElements,
  StripeCardCvcElement,
  StripeAddressElement,
  StripeCardExpiryElement,
  StripeCardNumberElement
} from "@stripe/stripe-js";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { axios } from "@/api";
import { StripeError } from "@/helpers/errors";
import {
  getClientValidationErrors,
  getServerErrors
} from "@/helpers/getErrors";
import { SubscriptionPlan } from "@/types";
import { STRIPE_APPEARANCE, STRIPE_CLASSES } from "@/utilities/stripe";

import Alert from "@/components/Alert.vue";
import Select from "@/components/Select.vue";
import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const router = useRouter();

const user = ref({
  email: "",
  subscription_plan: ""
});
const subscriptionPlans = ref<SubscriptionPlan[]>([]);
const isStripePaymentLoaded = ref(false);
const btnLoading = ref(false);
const stripeErrors = ref<IStripeError | null>(null);
const validationErrors = ref<typeof user.value | null>(null);
const serverErrors = ref<string[]>([]);

let stripe: Stripe | null;
let stripeElement: StripeElements;
let stripeCardNumberEl: StripeCardNumberElement;
let stripeCardExpiryDateEl: StripeCardExpiryElement;
let stripeCardCVCEl: StripeCardCvcElement;
let stripeAddressEl: StripeAddressElement;

const validationSchema = object({
  email: string()
    .email("Email addrss in invalid.")
    .required("Email is required."),
  subscription_plan: string().required("Subscription plan is required.")
});

onMounted(() => {
  getSubscriptionPlans();

  intializeStripePayment();
});

async function getSubscriptionPlans() {
  try {
    const resp = await axios.get("/payments/subscription-plan", {
      params: {
        trial: false
      }
    });

    subscriptionPlans.value = resp.data.data;
  } catch (e) {
    console.error(e);
  }
}

async function intializeStripePayment() {
  try {
    stripe = await loadStripe(import.meta.env.VITE_APP_STRIPE_PUBLISHABLE_KEY);

    if (!stripe) {
      throw new StripeError("Cannot load Stripe payment form.");
    }

    stripeElement = stripe.elements({
      appearance: STRIPE_APPEARANCE
    });

    stripeCardNumberEl = stripeElement.create("cardNumber", {
      showIcon: true,
      iconStyle: "solid",
      style: {
        base: {
          iconColor: "#233876"
        }
      },
      classes: STRIPE_CLASSES,
      placeholder: "Credit Card Number"
    });

    stripeCardExpiryDateEl = stripeElement.create("cardExpiry", {
      classes: STRIPE_CLASSES
    });

    stripeCardCVCEl = stripeElement.create("cardCvc", {
      classes: STRIPE_CLASSES
    });

    stripeAddressEl = stripeElement.create("address", {
      mode: "billing"
    });

    stripeCardNumberEl.mount("#payment-card-number");
    stripeCardExpiryDateEl.mount("#payment-card-expiry-date");
    stripeCardCVCEl.mount("#payment-card-cvc");
    stripeAddressEl.mount("#payment-card-address");

    isStripePaymentLoaded.value = true;
  } catch (e) {
    isStripePaymentLoaded.value = false;
  }
}

async function handleRegister() {
  try {
    validationErrors.value = null;
    serverErrors.value = [];

    btnLoading.value = true;

    const validatedUser = await validationSchema.validate(user.value, {
      abortEarly: false
    });

    if (!stripe) {
      throw new StripeError("Cannot load Stripe payment form.");
    }

    const billingDetails = await stripeAddressEl.getValue();

    const { paymentMethod, error } = await stripe.createPaymentMethod({
      type: "card",
      card: stripeCardNumberEl,
      billing_details: billingDetails.value
    });

    if (error) {
      stripeErrors.value = error;
      throw new StripeError("Stripe error occurred.");
    }

    const data = {
      email: validatedUser.email,
      subscription_plan: validatedUser.subscription_plan,
      payment_method: "stripe",
      card_nonce: paymentMethod.id,
      billing_details: billingDetails.value
    };

    const resp = await axios.post("payments/stripe/re-subscription", data);

    await router.push({ name: "login" });

    toast.info(resp.data.message);
  } catch (e) {
    btnLoading.value = false;

    validationErrors.value = getClientValidationErrors(e);
    serverErrors.value = getServerErrors(e);
  }
}
</script>

<template>
  <div class="grid min-h-screen grid-cols-2">
    <div id="introduction" class="fixed grid min-h-screen w-1/2 items-center">
      <div class="px-16 text-center text-white">
        <div class="flex justify-center">
          <FontAwesomeIcon size="4x" icon="fa-regular fa-face-frown-open" />
        </div>

        <h1 class="mt-5 text-3xl font-bold">
          Your subscription plan has expired.
        </h1>

        <h2 class="mt-3 text-gray-300">
          Please, renew your subscription plan to continue using Tradeaway.
        </h2>

        <p class="absolute bottom-2 left-1/2 -translate-x-1/2 text-sm">
          © Cita Algo Labs LLC {{ new Date().getFullYear() }}
        </p>
      </div>
    </div>

    <div></div>

    <div class="flex items-center justify-center bg-accent py-20">
      <div class="w-3/4 rounded-md border bg-white p-5 text-sm 2xl:w-3/5">
        <h2 class="text-xl font-bold">Your subscription plan has expired.</h2>

        <p class="mt-1 text-gray-600">
          Renew your subscription plan to continue using Tradeaway.
        </p>

        <Alert
          variant="danger"
          class="mt-2 text-xs"
          v-if="serverErrors.length !== 0"
        >
          <div v-for="error in serverErrors">
            {{ error }}
          </div>
        </Alert>

        <form novalidate class="mt-3" @submit.prevent="handleRegister">
          <div>
            <InputLabel for="email">
              Email
              <span class="text-danger">*</span>
            </InputLabel>

            <InputText
              id="email"
              type="email"
              placeholder="Email"
              :error="validationErrors?.email"
              v-model="user.email"
            />
          </div>

          <div class="mt-5">
            <InputLabel for="subscription_plan">
              Subscription Plan
              <span class="text-danger">*</span>
            </InputLabel>

            <Select
              id="subscription_plan"
              placeholder="Subscription Plan"
              :error="validationErrors?.subscription_plan"
              v-model="user.subscription_plan"
            >
              <option
                :value="subscription.name"
                v-for="subscription in subscriptionPlans"
                :key="subscription._id"
              >
                {{ subscription.service_name }} (${{ subscription.charge }}/{{
                  subscription.duration
                }})
              </option>
            </Select>
          </div>

          <div class="mt-5 flex items-center justify-between pr-2">
            <h2 class="text-base">
              <span class="font-medium">Payment Information</span>
              <span class="text-danger"> *</span>
            </h2>

            <img
              src="https://cdn.worldvectorlogo.com/logos/stripe-4.svg"
              width="48"
            />
          </div>

          <Alert
            variant="danger"
            class="mt-1 text-xs"
            v-if="!isStripePaymentLoaded"
          >
            Unable to load payment form. Please refesh the page and try again.
          </Alert>

          <div class="mt-2 grid grid-cols-2 gap-x-3 gap-y-2">
            <div
              id="payment-card-number"
              class="col-span-2 rounded-lg border border-gray-300 bg-accent px-2 pb-3 pt-3"
              :class="{ '!border-danger': stripeErrors }"
            ></div>

            <div
              id="payment-card-expiry-date"
              class="rounded-lg border border-gray-300 bg-accent px-2 pb-3 pt-3"
              :class="{ '!border-danger': stripeErrors }"
            ></div>

            <div
              id="payment-card-cvc"
              class="rounded-lg border border-gray-300 bg-accent px-2 pb-3 pt-3"
              :class="{ '!border-danger': stripeErrors }"
            ></div>

            <div class="px-1 text-xs text-danger" v-if="stripeErrors">
              {{ stripeErrors.message }}
            </div>

            <div id="payment-card-address" class="col-span-2 mt-2"></div>
          </div>

          <div class="mt-3 text-xs text-gray-600">
            Note: <br />
            Subscription will start immediately after you renew it.* <br />
            Cancel your subscription anytime you want.*
          </div>

          <div class="mt-3">
            <PrimaryButton
              type="submit"
              class="flex w-full justify-center"
              :loading="btnLoading"
            >
              Renew Subscription
            </PrimaryButton>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style scoped>
#introduction {
  background-image: url("@/assets/shiny.svg");
  background-size: cover;
}
</style>
