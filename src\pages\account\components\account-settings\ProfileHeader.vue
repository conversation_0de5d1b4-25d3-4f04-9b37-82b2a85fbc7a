<script setup lang="ts">
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import NavItem from "@/components/NavItem.vue";

defineProps<{
  text: string;
  routeName: string;
}>();
</script>

<template>
  <div class="mb-2 flex items-center gap-x-2 pl-3 pr-5 text-lg font-bold">
    <NavItem
      class="!rounded-full px-3 pb-2 pt-2.5"
      @click="$router.push({ name: routeName })"
    >
      <FontAwesomeIcon size="xl" icon="fa-solid fa-arrow-left" />
    </NavItem>

    <span class="mb-0.5">{{ text }}</span>
  </div>
</template>
