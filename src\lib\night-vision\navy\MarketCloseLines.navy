// Navy ~ 0.2-lite

[OVERLAY name=MarketCloseLines, ctx=Canvas, version=1.0.0]

// Props for customizing the appearance
prop('visible', { type: 'boolean', def: true })
prop('lineColor', { type: 'color', def: '#999999' })
prop('lineWidth', { type: 'number', def: 1 })
prop('lineStyle', { type: 'string', def: 'dashed' })
prop('closeHour', { type: 'number', def: 18 }) 
prop('closeMinute', { type: 'number', def: 0 })
prop('labelMinSpace', { type: 'number', def: 20 })

// NY settings
prop('nyVisible', { type: 'boolean', def: true }) 
prop('nyColor', { type: 'color', def: '#033f6e' })
prop('nyHour', { type: 'number', def: 18 })
prop('nyMinute', { type: 'number', def: 0 })

// Asia settings
prop('asiaVisible', { type: 'boolean', def: true })
prop('asiaColor', { type: 'color', def: '#732200' })
prop('asiaHour', { type: 'number', def: 9 })
prop('asiaMinute', { type: 'number', def: 0 })

// London settings
prop('londonVisible', { type: 'boolean', def: true })
prop('londonColor', { type: 'color', def: '#296600' })
prop('londonHour', { type: 'number', def: 8 })
prop('londonMinute', { type: 'number', def: 0 })

// Box plot
prop('boxPlotHigh', { type: 'boolean', def: false })
prop('boxPlotLow', { type: 'boolean', def: false })
prop('highMin', { type: 'number', def: 0 })
prop('highMax', { type: 'number', def: 0 })
prop('highMedian', { type: 'number', def: 0 })
prop('highQ1', { type: 'number', def: 0 })
prop('highQ3', { type: 'number', def: 0 })
prop('lowMin', { type: 'number', def: 0 })
prop('lowMax', { type: 'number', def: 0 })
prop('lowMedian', { type: 'number', def: 0 })
prop('lowQ1', { type: 'number', def: 0 })
prop('lowQ3', { type: 'number', def: 0 })

init() {
    this.recentLines = []
    this.latestClosingIndex = 0
    this.futureClosingIndex = 0
}

canShowLabel(x) {
    const range = $core.range
    const zoomLevel = range[1] - range[0]

    if (
        $core.props.timeFrame >= 14400000 && 
        zoomLevel > 110
    ) {
        return false
    } else if ($core.props.timeFrame >= 3600000 &&
        zoomLevel > 550
    ) {
        return false
    } else if ($core.props.timeFrame >= 900000 &&
        zoomLevel > 2000
    ) {
        return false
    } else if ($core.props.timeFrame >= 300000 &&
        zoomLevel > 6600
    ) {
        return false
    }

    if (this.recentLines.length === 0) {
        this.recentLines.push(x)
        return true
    }

    // Check distance from all recent lines
    for (let prevX of this.recentLines) {
        if (Math.abs(x - prevX) < $props.labelMinSpace) {
            return false
        }
    }

    // Keep only recent lines for comparison
    // Remove lines that are far to the left
    this.recentLines = this.recentLines.filter(prevX => 
        Math.abs(x - prevX) < $props.labelMinSpace * 2
    )
    
    this.recentLines.push(x)
    return true
}

shouldDrawLine(timestamp, prevTimestamp, closeHour, closeMinute, log) {
    let draw = false;

    const date = new Date(timestamp)

    if (log) {
        console.log("date.getUTCHours()", date.getUTCHours())
        console.log("closeHour", closeHour)
        console.log("timestamp", timestamp)
    }

    if (date.getUTCHours() === closeHour && 
        date.getUTCMinutes() === closeMinute) {
        draw = true
    }

    const closingHours = closeHour + closeMinute / 60

    // Given data is not the first data
    if (prevTimestamp !== 0) {
        const prevDate = new Date(prevTimestamp)
        let prevTimeInHours = 
            prevDate.getUTCHours() + prevDate.getUTCMinutes() / 60

        let currentTimeInHours = 
            date.getUTCHours() + date.getUTCMinutes() / 60

        const marketCloseHour = closeHour === 0 ? 24 : 0

        // Handle the case when crossing midnight
        if (prevTimeInHours > currentTimeInHours) {
            currentTimeInHours += 24
        }
        
        // Handle case when target hour is 0 (midnight)
        if (closingHours === 0 && prevTimeInHours > 20) { 
            prevTimeInHours -= 24
        }
        
        // Check if we crossed the target hour
        if (prevTimeInHours < closingHours && 
            currentTimeInHours >= closingHours) {
            draw = true
        }
    }
    
    return draw
}

drawMarketLine(ctx, x, color, label, layoutHeight) {
    ctx.strokeStyle = color
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, layoutHeight - 20)
    ctx.stroke()
    ctx.closePath()

    if (canShowLabel(x)) {
        ctx.fillStyle = color
        ctx.font = 'bold 14px Arial'
        ctx.textAlign = 'center'
        ctx.fillText(label, x, layoutHeight - 5)
    }
}

shouldDrawBoxPlot(data, i) {
    const closingTimestamp = data[i][0]
    const latestTimestamp = data[data.length - 1][0]

    return ((latestTimestamp - closingTimestamp) / 1000 / 60 / 60) < 24
}

drawBoxPLotFill(ctx, q1, q3, type) {
    const color = 
        type.toLowerCase().includes("high") ? 
            "#90EE90" : 
            "#FFB6C1"

    ctx.fillStyle = color + "30"

    const width = this.futureClosingIndex - this.latestClosingIndex

    const y1 = $core.layout.value2y(q1)
    const y3 = $core.layout.value2y(q3)

    const height = y3 - y1

    ctx.arc(this.latestClosingIndex, y1, 5, 0, 2 * Math.PI);
    ctx.fillRect(this.latestClosingIndex, y1, width, height)
}

drawBoxPlotLine(ctx, price, label, type) {
    const color = 
        type.toLowerCase().includes("high") ? 
            "#06402B" : 
            "#950606"
    const { width } = $core.layout
    ctx.beginPath()
    const y = $core.layout.value2y(price)
    ctx.moveTo(this.latestClosingIndex, y)
    ctx.lineTo(this.futureClosingIndex, y)
    ctx.strokeStyle = color
    ctx.lineWidth = "1px"
    ctx.setLineDash([])
    ctx.stroke()
    
    const labelWithValue = label + ": " + price
    const { width: textWidth } = ctx.measureText(labelWithValue)
    let labelX = Math.max(
                        Math.min(
                            width - 100, 
                            this.futureClosingIndex - 100
                        ),
                        this.latestClosingIndex + 100
                    )
    let labelY = y - 5

    const padding = 4
    const bgWidth = textWidth + (padding * 2)
    const bgHeight = 20

    let bgX = labelX - (bgWidth / 2)
    let bgY = y

    if ((this.futureClosingIndex + 135) < width) {
        bgX = this.futureClosingIndex
        bgY = y + (bgHeight / 2)
        labelX = this.futureClosingIndex + (bgWidth / 2)
        labelY = labelY + (bgHeight / 2)
    }

    ctx.fillStyle = color
    ctx.fillRect(
        bgX, 
        bgY,
        bgWidth,
        -bgHeight
    )

    ctx.fillStyle = "white"
    ctx.font = 'bold 14px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(labelWithValue, labelX, labelY)
}

findLatestClosingLine() {
    const data = $core.data

    for (var i = data.length - 1; i >= 0; i--) {
        if (!data[i] || !data[i - 1]) {
            continue
        }

        const timestamp = data[i][0]
        const prevTimestamp = data[i - 1][0]

        if (
            shouldDrawLine(
                timestamp, 
                prevTimestamp, 
                $props.closeHour, 
                $props.closeMinute
            )
        ) {
            this.latestClosingIndex = 
                ($core.layout.time2x(i) + $core.layout.time2x(i - 1)) / 2
            break;
        }
    }
}

draw(ctx) {
    if (
        (
            !$props.visible &&
            !$props.nyVisible &&
            !$props.londonVisible &&
            !$props.asiaVisible &&
            !$props.boxPlotHigh && 
            !$props.boxPlotLow
        ) 
        || $core.props.timeFrame > 14400000
    ) {
        return
    }

    const layout = $core.layout
    const data = $core.data
    const view = $core.view
    
    // Save the current context state
    ctx.save()
    
    // Set line style
    ctx.strokeStyle = $props.lineColor
    ctx.lineWidth = $props.lineWidth
    
    // Set dotted line style
    if ($props.lineStyle === 'dotted') {
        ctx.setLineDash([2, 4])
    } else if ($props.lineStyle === 'dashed') {
        ctx.setLineDash([4, 10])
    }

    // Get candle width from layout
    const candleW = layout.pxStep * $core.props.config.CANDLEW

    // Iterate through visible data points
    for (var i = view.i1, n = view.i2; i <= n; i++) {
        let p = data[i]
        let prevData

        if (i !== 0) {
            prevData = data[i - 1]
        }

        if (!p) continue
        
        const timestamp = p[0]

        // prevTimestamp is 0 for the first data
        let prevTimestamp = 0

        if (prevData) {
            prevTimestamp = prevData[0]
        }
        
        // Check if this timestamp represents market closing time
        // Draw NY line
        if ($props.nyVisible && 
            shouldDrawLine(
                timestamp, 
                prevTimestamp, 
                $props.nyHour, 
                $props.nyMinute
            )
        ) {
            let x = (layout.time2x(i) + layout.time2x(i - 1)) / 2
            drawMarketLine(ctx, x, $props.nyColor, 'NY', layout.height)
        }

        // Draw Asia line  
        if ($props.asiaVisible &&
            shouldDrawLine(
                timestamp, 
                prevTimestamp, 
                $props.asiaHour, 
                $props.asiaMinute
            )
        ) {
            let x = (layout.time2x(i) + layout.time2x(i - 1)) / 2
            drawMarketLine(ctx, x, $props.asiaColor, 'A', layout.height)
        }

        // Draw London line
        if ($props.londonVisible &&
            shouldDrawLine(
                timestamp, 
                prevTimestamp, 
                $props.londonHour, 
                $props.londonMinute
            )
        ) {
            let x = (layout.time2x(i) + layout.time2x(i - 1)) / 2
            drawMarketLine(ctx, x, $props.londonColor, 'L', layout.height)
        }

        // Draw closing line
        if (
            shouldDrawLine(
                timestamp, 
                prevTimestamp, 
                $props.closeHour, 
                $props.closeMinute
            )
        ) {
            let x = (layout.time2x(i) + layout.time2x(i - 1)) / 2

            if ($props.visible) {
                drawMarketLine(ctx, x, $props.lineColor, 'C', layout.height)
            }
            
            if (shouldDrawBoxPlot(data, i)) {
                this.latestClosingIndex = x
            }
        }
    }

    if (
        $props.visible || 
        $props.nyVisible || 
        $props.asiaVisible || 
        $props.londonVisible ||
        $props.boxPlotHigh ||
        $props.boxPlotLow
    ) {
        let index = data.length
        let timeFrame = $core.props.timeFrame
        let currentTimestamp, prevTimestamp

        let futureAsia, futureNy, futureLondon, futureClosing

        for (count = 1; count < 2000; count++) {
            currentTimestamp = data[data.length - 1][0] + timeFrame * count
            prevTimestamp = currentTimestamp - timeFrame

            if (
                shouldDrawLine(
                    currentTimestamp, 
                    prevTimestamp, 
                    $props.closeHour, 
                    $props.closeMinute
                ) &&
                !futureClosing
            ) {
                lineIndex = index + count - 1
                let x = (layout.time2x(lineIndex) + layout.time2x(lineIndex - 1)) / 2

                if ($props.visible) {
                    drawMarketLine(ctx, x, $props.lineColor, 'C', layout.height)
                }
                futureClosing = true

                this.futureClosingIndex = x
            }

            // Draw Asia line  
            if ($props.asiaVisible &&
                shouldDrawLine(
                    currentTimestamp, 
                    prevTimestamp, 
                    $props.asiaHour, 
                    $props.asiaMinute
                ) &&
                !futureAsia
            ) {
                lineIndex = index + count - 1
                let x = (layout.time2x(lineIndex) + layout.time2x(lineIndex - 1)) / 2
                drawMarketLine(ctx, x, $props.asiaColor, 'A', layout.height)
                futureAsia = true
            }

            if ($props.nyVisible && 
                shouldDrawLine(
                    currentTimestamp, 
                    prevTimestamp, 
                    $props.nyHour, 
                    $props.nyMinute
                ) &&
                !futureNy
            ) {
                lineIndex = index + count - 1
                let x = (layout.time2x(lineIndex) + layout.time2x(lineIndex - 1)) / 2
                drawMarketLine(ctx, x, $props.nyColor, 'NY', layout.height)
                futureNy = true
            }

            if ($props.londonVisible &&
                shouldDrawLine(
                    currentTimestamp, 
                    prevTimestamp, 
                    $props.londonHour, 
                    $props.londonMinute
                ) &&
                !futureLondon
            ) {
                lineIndex = index + count - 1
                let x = (layout.time2x(lineIndex) + layout.time2x(lineIndex - 1)) / 2
                drawMarketLine(ctx, x, $props.londonColor, 'L', layout.height)
                futureLondon = true
            }
        }
    }

    if ($props.boxPlotHigh || $props.boxPlotLow){ 
        findLatestClosingLine()

        if (this.latestClosingIndex) {
            if ($props.boxPlotHigh) {
                drawBoxPlotLine(ctx, $props.highMax, "Max", "high")
                drawBoxPlotLine(ctx, $props.highMin, "Min", "high")
                drawBoxPlotLine(ctx, $props.highMedian, "Median", "high")
                drawBoxPlotLine(ctx, $props.highQ1, "Q1", "high")
                drawBoxPlotLine(ctx, $props.highQ3, "Q3", "high")
                drawBoxPLotFill(ctx, $props.highQ1, $props.highQ3, "high")
            }

            if ($props.boxPlotLow) {
                drawBoxPlotLine(ctx, $props.lowMin, "Min", "low")
                drawBoxPlotLine(ctx, $props.lowMax, "Max", "low")
                drawBoxPlotLine(ctx, $props.lowMedian, "Median", "low")
                drawBoxPlotLine(ctx, $props.lowQ1, "Q1", "low")
                drawBoxPlotLine(ctx, $props.lowQ3, "Q3", "low")
                drawBoxPLotFill(ctx, $props.lowQ1, $props.lowQ3, "low")
            }
        }
    }
    
    // Restore the context state
    ctx.restore()
}

// No need for legend
legend() => null

// Hide the overlay from value tracker
valueTracker() => ({
    show: false
})

// Define y-range to be the same as the main overlay
yRange(hi, lo) => [hi, lo]