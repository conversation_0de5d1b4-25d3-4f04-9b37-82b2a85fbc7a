import { AvailableShapes } from "../../groups/ShapeTool";
import { TCoreData } from "../../types";
import { BasicPoint } from "../base/Types";
import { BaseShapeInterface } from "../base/shape.types";
import { PreferenceUtils } from "../utils/PreferencesUtils";
import { IPoint, Point } from "./Point";

type ShapePreferences = typeof PreferenceUtils;

// Helper type to extract property keys for a given shape type
type PropertyKeys<X extends AvailableShapes> = keyof ShapePreferences[X];

// Helper type to extract sub-property keys for a given shape type and property
type SubPropertyKeys<
  X extends AvailableShapes,
  Y extends PropertyKeys<X>
> = keyof ShapePreferences[X][Y];

// TProperties type with inferred keys
type TProperties<X extends AvailableShapes> = {
  type: X;
  properties: ShapePreferences[X];
  setProperty: <Y extends PropertyKeys<X>, Z extends SubPropertyKeys<X, Y>>(
    baseKey: Y,
    subKey: Z,
    value: ShapePreferences[X][Y][Z]
  ) => boolean;
};

// Define IBaseShapeOptions with inferred types
export type IBaseShapeOptions<X extends AvailableShapes> = {
  [x: string]: any; // Additional properties that are not part of TProperties
  uuid: string;
  toggleDetails: (b?: boolean) => boolean;
  getLabelProperties: () => {
    [x: string]: boolean;
  };

  name: string;
  dragging: boolean;
  hovered: boolean;
  deleteShape: () => void;
  locked: boolean;
  getCoordinates: () => { [x: string]: { row: number; price: number } };
  setCoordinates: (...d: any[]) => void; // Define the type appropriately
} & TProperties<X>;

export interface IBaseShape {
  uuid: string;
  points: IPoint[];
  selected: boolean;
  hovered: boolean;
  dragging: boolean;
  isEditing?: boolean;
  locked: boolean;
  detailsInfo: { [x: string]: boolean };
  name: string;
  type: AvailableShapes;
  setName: (name: string) => void;
  getLabelProperties: () => { [x: string]: boolean };
  setLabelProperty: (name: string, value: boolean) => boolean;
  showLabelDetails: boolean;
  readonly isValid: boolean;
  toggleDetails: (bool?: boolean) => boolean;
  onSelect(shape: BaseShapeInterface<AvailableShapes>): boolean;
  toJSON?: () => Record<string, any>;
  getOptions: () => IBaseShapeOptions<any>;
  destroy?: () => void;
}

export class BaseShape<T extends AvailableShapes = "no-shape">
  implements IBaseShape
{
  uuid: string = "";
  onSelect(_shape: BaseShapeInterface<AvailableShapes>) {
    return false;
  }
  points: IPoint[] = [];
  selected: boolean = false;
  hovered: boolean = false;
  $core: TCoreData;
  dragging: boolean = false;
  protected draggingPoint: { x: number; y: number } | null = null;
  locked: boolean = false;
  detailsInfo: { [x: string]: boolean } = {};
  private _type: AvailableShapes = "no-shape";
  private _name = "";
  protected set type(data: AvailableShapes) {
    this._type = data;
    this._name = data;
  }
  get name() {
    return this._name;
  }
  get type() {
    return this._type;
  }
  setName: (name: string) => void = (name) => {
    this._name = name;
  };

  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (s: IBaseShape) => void,
    showDetails: boolean = true
  ) {
    this.$core = $core;
    this.uuid = uuid;
    this._showDetails = showDetails;
    points.forEach((p, i) =>
      this.points.push(new Point($core, `${this.uuid}-${i}`, p, screenPoints))
    );

    // @ts-expect-error
    if (onSelect) this.onSelect = onSelect;

    this.$core.hub.events.on(
      "base-shape:double-click",
      this.stopDragging.bind(this)
    );
  }

  stopDragging() {
    setTimeout(() => {
      this.dragging = false;
      this.draggingPoint = null;
    }, 0);
  }

  isEditing?: boolean | undefined;
  toJSON?: (() => Record<string, any>) | undefined;

  getOptions: () => IBaseShapeOptions<any> = () => ({
    uuid: this.uuid,
    deleteShape: () => console.error("Not implemented"),
    dragging: this.dragging,
    getCoordinates: this.getCoordinates.bind(this),
    getLabelProperties: this.getLabelProperties.bind(this),
    hovered: this.hovered,
    locked: this.locked,
    toggleDetails: this.toggleDetails.bind(this),
    properties: this.properties,
    setProperty: this.setProperty.bind(this),
    setCoordinates: () => false,
    type: this.type,
    name: this.name,
    setName: this.setName.bind(this)
  });

  getLabelProperties(): { [x: string]: boolean } {
    return JSON.parse(JSON.stringify(this.detailsInfo));
  }

  setLabelProperty(name: string, value: boolean): boolean {
    if (name in this.detailsInfo) this.detailsInfo[name] = value;
    return value;
  }

  drawDetails(_: CanvasRenderingContext2D): void {
    return;
  }

  protected _showDetails: boolean = false;

  toggleDetails(bool?: boolean) {
    if (bool === undefined) this._showDetails = !this._showDetails;
    else this._showDetails = bool;
    return this._showDetails;
  }

  get showLabelDetails(): boolean {
    return this._showDetails;
  }

  get isValid() {
    return false;
  }

  moveShape(difference: BasicPoint): void {
    this.points.forEach((p) => {
      const screen = p.screen;
      // I don't know why the screen coordinates always come less than real values
      const x = screen.x + difference.x + 1;
      const y = screen.y + difference.y + 1;
      p.updatePosition({ x, y });
    });
  }

  getCoordinates(): { [x: string]: any } {
    return this.points.map((p) => {
      return { row: p.x, price: p.y };
    });
  }

  setCoordinatesVal(name: string | number, value: any): boolean {
    if (value.row && value.price) {
      this.points[name as number].x = Number(value.row);
      this.points[name as number].y = Number(value.price);
      return true;
    }
    return false;
  }

  // @ts-expect-error
  _properties: IBaseShapeOptions<T>["properties"] = {};
  get properties(): IBaseShapeOptions<T>["properties"] {
    return this._properties;
  }
  set properties(value: IBaseShapeOptions<T>["properties"]) {
    this._properties = JSON.parse(JSON.stringify(value));
  }

  private setPropertyItem: IBaseShapeOptions<T>["setProperty"] = (
    baseKey,
    subKey,
    value
  ) => {
    if (typeof this._properties[baseKey][subKey] !== "undefined") {
      this._properties[baseKey][subKey] = value;
      return true;
    }
    return false;
  };

  setProperty(baseKey: any, subKey: any, value: any) {
    const changed = this.setPropertyItem(baseKey, subKey, value);
    this.$core.hub.events.emit("shape-property-changed");
    this.$core.hub.events.emitSpec("shapetool", "default-property-updated", {
      type: this.type,
      properties: this.properties
    });
    return changed;
  }

  setProperties(p: IBaseShapeOptions<T>["properties"]) {
    this.properties = p;
  }
}
