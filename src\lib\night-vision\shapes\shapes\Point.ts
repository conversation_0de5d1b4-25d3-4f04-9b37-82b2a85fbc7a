import { TCoreData } from "../../types";
import { BasicPoint } from "../base/Types";
import { DistanceUtils } from "../utils/DistanceUtils";
import { DrawUtils } from "../utils/DrawUtils";
import { DrawUtils2 } from "../utils/DrawUtils2";

export interface IPoint {
  $core: TCoreData;
  uuid: string;
  draw(ctx: CanvasRenderingContext2D): void;
  x: number;
  y: number;
  readonly time: number;
  readonly screen: {
    x: number;
    y: number;
  };
  updatePosition(point: { x: number; y: number }): void;
  readonly screenX: number;
  readonly screenY: number;
  dragging: boolean;
  hovered: boolean;
  dragDirection?: "x" | "y";
  startDragging(dir?: "x" | "y"): void;
  mousemove(event: MouseEvent): void;
  mouseup(event: MouseEvent): void;
}

export class Point implements IPoint {
  $core;
  uuid;
  _x: number = 0;
  _y: number = 0;
  get x() {
    const prec = this.$core.layout?.prec ?? 6;
    return Math.round(this._x * 10 ** prec) / 10 ** prec;
  }
  get y() {
    const prec = this.$core.layout?.prec ?? 6;
    return Math.round(this._y * 10 ** prec) / 10 ** prec;
  }
  set x(val: number) {
    this._x = val;
  }
  set y(val: number) {
    this._y = val;
  }
  dragging: boolean = false;
  direction: "x" | "y" | null = null;
  dragDirection?: "x" | "y";
  hovered: boolean = false;
  constructor(
    $core: TCoreData,
    uuid: string,
    point: { x: number; y: number },
    screenPoint: boolean = true
  ) {
    this.$core = $core;
    this.uuid = uuid;
    if (screenPoint) {
      this.updatePosition(point);
    } else {
      this.x = point.x;
      this.y = point.y;
    }
    this.initEventHandlers();
  }
  initEventHandlers() {
    // Every Point is unique
    this.$core.hub.events.on(`point-${this.uuid}:double-click`, () => {
      this.dragging = false;
    });
  }
  get time(): number {
    if (!this.$core.layout.indexBased) return this.x;
    const { data } = this.$core.hub.data.panes[0].overlays[0];
    const x = Math.round(this.x);

    let dI = x < data.length ? x : data.length - 1;

    if (x < data.length) {
      dI = x;
    } else {
      const diff = x - data.length + 1;
      const { timeFrame } = this.$core.props;
      const time = data[data.length - 1][0] + timeFrame * diff;
      return time;
    }

    if (!!data[dI] && !!data[dI][0])
      return data[x < data.length ? x : data.length - 1][0];
    return 0;
  }

  mousemove(): void {
    if (this.dragging) {
      let [x, y] = [this.screenX + 1, this.screenY + 1];
      if (!this.direction || this.direction === "x") x = this.$core.cursor.x;
      if (!this.direction || this.direction === "y") y = this.$core.cursor.y;
      this.updatePosition({ x, y });
      return;
    }
    if (DistanceUtils.isCursorOnPoint(this.screen, this.$core.cursor))
      this.hovered = true;
    else this.hovered = false;
  }
  mouseup(): void {
    this.dragging = false;
  }
  draw(ctx: CanvasRenderingContext2D): void {
    DrawUtils.drawPoint(ctx, this.screenX, this.screenY);
  }
  updatePosition(point: { x: number; y: number }) {
    this.x = Math.round(this.$core.layout.x2ti(point.x));
    this.y = this.$core.layout.y2value(point.y);
    this.$core.hub.events.emit("point-update-coordinates");
  }
  startDragging(dir?: "x" | "y") {
    this.dragging = true;
    this.direction = dir ?? this.dragDirection ?? null;
    this.$core.hub.events.emit("scroll-lock", true);
  }
  get screen() {
    return { x: this.screenX, y: this.screenY };
  }
  get screenX(): number {
    return this.$core.layout.time2x(this.x);
  }
  get screenY(): number {
    return this.$core.layout.value2y(this.y);
  }

  static drawPoint(
    ctx: CanvasRenderingContext2D,
    point: BasicPoint,
    shape: "circle" | "square" | "arrow",
    width: number,
    stroke: string,
    strokeWidth: number
  ) {
    ctx.save();
    ctx.lineWidth = strokeWidth;
    ctx.strokeStyle = stroke;
    if (shape === "circle") {
      DrawUtils2.drawCirclePoint(ctx, point, width);
    }
    if (shape === "square") {
      DrawUtils2.drawSquarePoint(ctx, point, width);
    }
    ctx.restore();
  }
}
