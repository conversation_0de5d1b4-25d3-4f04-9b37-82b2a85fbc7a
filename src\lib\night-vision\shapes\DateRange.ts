import { TCoreData } from "../types";
import { Line } from "./LineTool";
import { BasicPoint } from "./base/Types";
import { BaseShapeInterface } from "./base/shape.types";
import { IBaseShape } from "./shapes/BaseShape";
import { DistanceUtils } from "./utils/DistanceUtils";
import { PreferenceUtils } from "./utils/PreferencesUtils";

export class DateRange
  extends Line
  implements BaseShapeInterface<"date-range">
{
  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (line: IBaseShape) => boolean
  ) {
    const linePoints = points.length
      ? points
      : [
          { x: $core.cursor.x, y: $core.cursor.y },
          { x: $core.cursor.x, y: $core.cursor.y }
        ];

    super($core, uuid, linePoints, screenPoints, onSelect);

    if (!points.length)
      $core.hub.events.emit("shape-draw-start", {
        shape: this,
        points: [{ p: this.points[1], dir: "x" }]
      });
    this.type = "date-range";
    this.properties = PreferenceUtils["date-range"];
    // this.midPointStyles.show = true
  }
  drawSidebar(): void {
    return;
  }

  detailsInfo: { [x: string]: boolean } = {
    dateTimeRange: true,
    barsRange: true
  };

  setProperty(baseKey: any, subKey: any, value: any) {
    if (subKey === "label_position") {
      return false;
    }

    return super.setProperty(baseKey, subKey, value);
  }

  mousedown(event: MouseEvent): void {
    const cursor = { x: event.offsetX, y: event.offsetY };
    if (
      DistanceUtils.isCursorOnLine(
        this.screenPoints[0],
        this.screenPoints[1],
        cursor
      )
    ) {
      this.onSelect(this);
      let dragPoint = false;
      for (let i = 0; i < this.points.length; i++) {
        const p = this.points[i];
        if (DistanceUtils.isCursorOnPoint(p.screen, cursor)) {
          p.startDragging("x");
          dragPoint = true;
          break;
        }
      }
      if (!dragPoint) {
        this.dragging = true;
        this.draggingPoint = { x: this.$core.cursor.x, y: this.$core.cursor.y };

        this.$core.hub.events.emit("scroll-lock", true);
      }
    } else {
      this.dragging = false;
      this.draggingPoint = null;
      this.selected = false;
    }
    this.$core.hub.events.emit("update-layout");
  }
}
