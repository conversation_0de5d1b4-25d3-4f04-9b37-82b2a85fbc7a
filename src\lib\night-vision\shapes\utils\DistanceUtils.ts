type BasicPoint = { x: number; y: number };
export class DistanceUtils {
  static threshold = 5;
  /**
   * Returns Euclidean distance
   */
  static distance(p1: BasicPoint, p2: BasicPoint) {
    return Math.sqrt((p1.x - p2.x) ** 2 + (p1.y - p2.y) ** 2);
  }
  /**
   * Returns Angle of line made from p1 to p2 with x-axis
   */
  static angleWithXAxis(p1: BasicPoint, p2: BasicPoint) {
    let x1 = p1.x;
    let y1 = p1.y;
    let x2 = p2.x;
    let y2 = p2.y;

    let angleRad = Math.atan2(y2 - y1, x2 - x1);
    let angleDeg = angleRad * (180 / Math.PI);
    return -angleDeg;
  }
  /**
   * Check if cursor point in on target point
   */
  static isCursorOnPoint(
    point: BasicPoint,
    cursor: BasicPoint,
    threshold?: number
  ) {
    return this.distance(point, cursor) < (threshold ?? this.threshold) * 2;
  }
  /**
   * Check if cursor point lies on target line
   */
  static isCursorOnLine(p1: BasicPoint, p2: BasicPoint, cursor: BasicPoint) {
    // Calculate distance between cursor and line segment
    if (p1.x === p2.x && p1.y === p2.y) {
      return this.isCursorOnPoint(p1, cursor);
    }
    const dx = p2.x - p1.x;
    const dy = p2.y - p1.y;
    const t =
      ((cursor.x - p1.x) * dx + (cursor.y - p1.y) * dy) / (dx * dx + dy * dy);

    // If the projection of cursor is beyond the endpoints of the segment,
    // constrain it to the endpoints
    const closestPoint = {
      x: Math.max(0, Math.min(1, t)) * dx + p1.x,
      y: Math.max(0, Math.min(1, t)) * dy + p1.y
    };

    // Calculate distance between cursor and closest point on line
    const distance = Math.sqrt(
      (cursor.x - closestPoint.x) ** 2 + (cursor.y - closestPoint.y) ** 2
    );

    // Return true if the distance is within the threshold
    return distance < this.threshold;
  }
  /**
   * Calculate time difference in days, hours, minutes
   */
  static getTimeDifference(timestamp1: number, timestamp2: number) {
    const difference = Math.abs(
      new Date(timestamp1).getTime() - new Date(timestamp2).getTime()
    );
    const millisecondsInDay = 1000 * 60 * 60 * 24; // Number of milliseconds in a day
    const millisecondsInHour = 1000 * 60 * 60; // Number of milliseconds in an hour
    const millisecondsInMinutes = 1000 * 60; // Number of milliseconds in a minute

    const days = Math.floor(difference / millisecondsInDay);
    let remainingMilliseconds = difference % millisecondsInDay;
    const hours = Math.floor(remainingMilliseconds / millisecondsInHour);
    remainingMilliseconds = difference % millisecondsInHour;
    const minutes = Math.floor(remainingMilliseconds / millisecondsInMinutes);

    if (days > 0) {
      if (hours === 0) {
        return `${days}D`;
      } else {
        return `${days}D ${hours}H`;
      }
    } else {
      return `${hours}H ${minutes}M`;
    }
  }
}
