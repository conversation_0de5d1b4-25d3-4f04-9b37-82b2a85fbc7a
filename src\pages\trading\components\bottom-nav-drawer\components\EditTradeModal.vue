<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { toast } from "vue3-toastify";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { ILabelInfo } from "@/lib/night-vision/shapes/NewOrder";

import { axios } from "@/api";
import { useDragElement } from "@/composables/useDragElement";
import { useLabelFunctions } from "@/chart-frontend/labelUtils";
import { handleRemoveSelectedTradeShape } from "@/chart-frontend/utils";
import { countDecimals } from "@/helpers/numberUtils";
import { getServerErrors } from "@/helpers/getErrors";
import { eaSocket } from "@/socketio";
import { useUserStore } from "@/store/userStore";
import { useChartStore } from "@/store/chartStore";
import { useCandleStickStore } from "@/store/candleStickStore";
import { useBottomNavDrawerStore } from "@/store/bottomNavDrawerStore";
import { useTradeShapeStore } from "@/store/tradeShapeStore";
import {
  EOrderType,
  EActiveOrders,
  EPlacedOrders,
  EOrderTypeTime,
  ETradeOperationType
} from "@/types/enums";

import Select from "@/components/Select.vue";
import NavItem from "@/components/NavItem.vue";
import Checkbox from "@/components/Checkbox.vue";
import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";
import { DateTime } from "luxon";

/*
  Active Trade
  - action
  - symbol
  - sl
  - tp
  - position (ticket)

  Pending Trade
  - action
  - sl
  - tp
  - price
  - order (ticket)
  - type_time
  - expiration
*/
interface ITrade {
  action: string;
  type: string;
  symbol: string;
  volume: number;
  sl: number;
  tp: number;
  ticket: number;
  price: number;
  tp1?: number;
  tp1_status?: string;
  vol1?: number;
  vol1_status?: string;
  sl_trail?: number;
  type_time?: string;
  expiration?: string;
}

const props = defineProps<ITrade>();

const userStore = useUserStore();
const chartStore = useChartStore();
const tradeShapeStore = useTradeShapeStore();
const candlestickStore = useCandleStickStore();
const bottomNavDrawerStore = useBottomNavDrawerStore();

const { left, top } = useDragElement(
  "edit-trade-modal",
  "edit-trade-modal-header"
);

const { calculateSlLabels, calculateTpLabels } = useLabelFunctions();

const trade = ref<ITrade>({
  ...props
});
const priceStep = ref(0);
const slStep = ref(0);
const tpStep = ref(0);
const tp1VolPercent = ref(50);
const timeTypeList = ref([
  {
    id: EOrderTypeTime.ORDER_TIME_GTC,
    name: "GTC"
  },
  {
    id: EOrderTypeTime.ORDER_TIME_DAY,
    name: "Today"
  },
  {
    id: EOrderTypeTime.ORDER_TIME_SPECIFIED,
    name: "Specified"
  }
]);
const bidPrice = ref(0);
const askPrice = ref(0);
const updateTP1 = ref(false);
const updateSLTrail = ref(false);
const btnLoading = ref(false);
const validationErrors = ref<string[]>([]);

const previousSl = props.sl;
const previousTp = props.tp;

onMounted(() => {
  bottomNavDrawerStore.wasTradeRecentlyUpdated = false;

  document.getElementById("sl")?.focus();

  const { price, sl, tp, type_time, expiration } = props;

  if (sl === 0 || tp === 0) {
    calculateInitialSLAndTP();
  }

  priceStep.value = 1 / Math.pow(10, countDecimals(price!));
  slStep.value = 1 / Math.pow(10, countDecimals(sl));
  tpStep.value = 1 / Math.pow(10, countDecimals(tp));

  if (props.vol1 !== 0 && props.vol1_status === "not_executed") {
    updateTP1.value = true;
    updateTP1AndTP1VOL1();
  }

  if (trade.value.action === ETradeOperationType.TRADE_ACTION_MODIFY) {
    const date = new Date(expiration!);

    console.log(expiration);

    if (type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED) {
      trade.value.expiration =
        DateTime.fromJSDate(date).toFormat("yyyy-MM-dd'T'HH:mm");
    } else if (type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED_DAY) {
      trade.value.expiration = DateTime.fromJSDate(date).toFormat("yyyy-MM-dd");
    }
  }

  if (tradeShapeStore.selectedTradeShape) {
    tradeShapeStore.selectedTradeShape.makeDraggable();
  }

  chartStore.shapeToolInstance?.addListener(
    "placeneworder",
    "update-price-value",
    (value: number) => {
      trade.value.price = value;
    }
  );

  chartStore.shapeToolInstance?.addListener(
    "placeneworder",
    "update-sl-value",
    (value: number) => {
      trade.value.sl = value;

      if (tradeShapeStore.selectedTradeShape && props.price) {
        const labelInfo: ILabelInfo = calculateSlLabels(
          {
            volume: props.volume,
            price: props.price,
            sl: value,
            orderType: props.type === "BUY" ? "buy" : "sell"
          },
          props.symbol
        );

        tradeShapeStore.selectedTradeShape.updateStopLoss(
          Number(value),
          labelInfo
        );
        chartStore.chart?.update();
      }
    }
  );

  chartStore.shapeToolInstance?.addListener(
    "placeneworder",
    "update-tp-value",
    (value: number) => {
      trade.value.tp = value;

      if (tradeShapeStore.selectedTradeShape && props.price) {
        const labelInfo: ILabelInfo = calculateTpLabels(
          {
            volume: props.volume,
            price: props.price,
            tp: value,
            orderType: props.type === "BUY" ? "buy" : "sell"
          },
          props.symbol
        );

        tradeShapeStore.selectedTradeShape.updateTakeProfit(
          Number(value),
          labelInfo
        );
        chartStore.chart?.update();
      }
    }
  );

  chartStore.shapeToolInstance?.addListener(
    "placeneworder",
    "update-tp1-value",
    (value: number) => {
      trade.value.tp1 = value;

      if (!updateTP1.value) {
        updateTP1.value = true;
      }

      if (tradeShapeStore.selectedTradeShape && props.price) {
        const labelInfo: ILabelInfo = calculateTpLabels(
          {
            volume: props.volume,
            price: props.price,
            tp: value,
            orderType: props.type === "BUY" ? "buy" : "sell"
          },
          props.symbol
        );

        tradeShapeStore.selectedTradeShape.updateTakeProfit1(
          Number(value),
          labelInfo
        );

        if (props.type === EPlacedOrders.ORDER_TYPE_BUY_LIMIT) {
          tradeShapeStore.selectedTradeShape.priceHLine.setMaxValue(
            candlestickStore.askPrice
          );
        }

        if (props.type === EPlacedOrders.ORDER_TYPE_BUY_STOP) {
          tradeShapeStore.selectedTradeShape.priceHLine.setMinValue(
            candlestickStore.askPrice
          );
        }

        if (props.type === EPlacedOrders.ORDER_TYPE_SELL_LIMIT) {
          tradeShapeStore.selectedTradeShape.priceHLine.setMinValue(
            candlestickStore.bidPrice
          );
        }

        if (props.type === EPlacedOrders.ORDER_TYPE_SELL_STOP) {
          tradeShapeStore.selectedTradeShape.priceHLine.setMaxValue(
            candlestickStore.bidPrice
          );
        }

        chartStore.chart?.update();
      }
    }
  );

  window.addEventListener("beforeunload", function () {
    handleRemoveSelectedTradeShape();
  });
});

watch([top, left], ([topPos, leftPos]) => {
  bottomNavDrawerStore.setEditTradeModalPosition(topPos, leftPos);
});

watch(
  () => trade.value.sl,
  (newSl) => {
    if (tradeShapeStore.selectedTradeShape) {
      tradeShapeStore.selectedTradeShape.updateStopLoss(Number(newSl));
      chartStore.chart?.update();
    }
  }
);

watch(
  () => trade.value.tp,
  (newTp) => {
    if (tradeShapeStore.selectedTradeShape) {
      tradeShapeStore.selectedTradeShape.updateTakeProfit(Number(newTp));
      chartStore.chart?.update();
    }
  }
);

watch(
  () => [trade.value.tp1, trade.value.vol1],
  ([newTp1, newVol1]) => {
    if (tradeShapeStore.selectedTradeShape && props.price) {
      const tp1Label: ILabelInfo = calculateTpLabels(
        {
          volume: newVol1!,
          price: props.price,
          tp: newTp1!,
          orderType: props.type === "BUY" ? "buy" : "sell"
        },
        props.symbol
      );

      let tpVol;

      if (!newTp1) {
        tpVol = props.volume;
      } else {
        tpVol = props.volume - newVol1!;
      }

      const tpLabel: ILabelInfo = calculateTpLabels(
        {
          volume: tpVol,
          price: props.price,
          tp: props.tp,
          orderType: props.type === "BUY" ? "buy" : "sell"
        },
        props.symbol
      );

      tradeShapeStore.selectedTradeShape.updateTakeProfit(
        Number(props.tp),
        tpLabel
      );

      tradeShapeStore.selectedTradeShape.updateTakeProfit1(
        Number(newTp1),
        tp1Label
      );
      chartStore.chart?.update();
    }
  }
);

function calculateInitialSLAndTP() {
  const { price, type } = props;

  const priceDecimalCount = countDecimals(price);

  const FACTOR = priceDecimalCount >= 4 ? 10000 : 100;

  const sign = type === EOrderType.ORDER_TYPE_SELL ? 1 : -1;

  const sl = price + sign * (20 / FACTOR);

  const tp = price - sign * (20 / FACTOR);

  trade.value.sl = parseFloat(sl.toFixed(priceDecimalCount));

  trade.value.tp = parseFloat(tp.toFixed(priceDecimalCount));
}

function handleClose() {
  if (tradeShapeStore.selectedTradeShape) {
    tradeShapeStore.selectedTradeShape.updateStopLoss(previousSl);
    tradeShapeStore.selectedTradeShape.updateTakeProfit(previousTp);
  }

  bottomNavDrawerStore.toggleEditTradeModal(false);
}

function updateTP1VolPercent() {
  const volumeDecimalCount = countDecimals(props.volume);

  trade.value.vol1 = parseFloat(
    ((tp1VolPercent.value / 100) * props.volume).toFixed(volumeDecimalCount)
  );
}

function handleTimeType() {
  const { type_time } = trade.value;

  if (type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED) {
    trade.value.expiration = DateTime.now().toFormat("yyyy-MM-dd'T'HH:mm");
  }
}

function validationForActiveTrade() {
  let errors: string[] = [];

  if (props.type === EActiveOrders.POSITION_TYPE_SELL) {
    if (trade.value.sl < bidPrice.value) {
      errors.push("SL should be higher than bid price.");
    }

    if (trade.value.tp > bidPrice.value) {
      errors.push("TP should be lower than bid price.");
    }

    if (updateTP1.value) {
      if (
        trade.value.tp1! > bidPrice.value &&
        trade.value.tp1! < trade.value.tp
      ) {
        errors.push("TP1 should be lower than bid price and higher than tp.");
      }

      if (trade.value.vol1! > props.volume) {
        errors.push("TP1 Vol must be less than volume.");
      }
    }
  }

  if (props.type === EActiveOrders.POSITION_TYPE_BUY) {
    if (trade.value.sl > askPrice.value) {
      errors.push("SL should be lower than ask price.");
    }

    if (trade.value.tp < askPrice.value) {
      errors.push("TP should be higher than ask price.");
    }

    if (updateTP1.value) {
      if (
        trade.value.tp1! < askPrice.value &&
        trade.value.tp1! > trade.value.tp
      ) {
        errors.push("TP1 should be higher than ask price and lower than TP.");
      }

      if (trade.value.vol1! > props.volume) {
        errors.push("TP1 Vol must be less than volume.");
      }
    }
  }

  return errors;
}

function validationForPendingTrade() {
  let errors: string[] = [];

  if (
    props.type === EPlacedOrders.ORDER_TYPE_SELL_LIMIT ||
    props.type === EPlacedOrders.ORDER_TYPE_SELL_STOP
  ) {
    if (trade.value.sl < props.price) {
      errors.push("SL should be higher than price.");
    }

    if (trade.value.tp > props.price) {
      errors.push("TP should be lower than price.");
    }
  }

  if (
    props.type === EPlacedOrders.ORDER_TYPE_BUY_LIMIT ||
    props.type === EPlacedOrders.ORDER_TYPE_BUY_STOP
  ) {
    if (trade.value.sl > props.price) {
      errors.push("SL should be lower than price.");
    }

    if (trade.value.tp < props.price) {
      errors.push("TP should be higher than price.");
    }
  }

  if (props.type === EPlacedOrders.ORDER_TYPE_BUY_LIMIT) {
    if (trade.value.price > candlestickStore.askPrice) {
      errors.push("Price should be lower than ask price.");
    }
  }

  if (props.type === EPlacedOrders.ORDER_TYPE_BUY_STOP) {
    if (trade.value.price < candlestickStore.askPrice) {
      errors.push("Price should be higher than ask price.");
    }
  }

  if (props.type === EPlacedOrders.ORDER_TYPE_SELL_LIMIT) {
    if (trade.value.price < candlestickStore.bidPrice) {
      errors.push("Price should be higher than bid price.");
    }
  }

  if (props.type === EPlacedOrders.ORDER_TYPE_SELL_STOP) {
    if (trade.value.price > candlestickStore.bidPrice) {
      errors.push("Price should be lower than bid price.");
    }
  }

  return errors;
}

function handleValidaton() {
  const { bid, ask } = candlestickStore.marketWatchSymbolList.find(
    (item) => item.symbol === trade.value.symbol
  )!;

  bidPrice.value = bid;
  askPrice.value = ask;

  if (props.action === ETradeOperationType.TRADE_ACTION_SLTP) {
    return validationForActiveTrade();
  } else {
    return validationForPendingTrade();
  }
}

async function placeEditOrder() {
  try {
    validationErrors.value = [];
    btnLoading.value = true;

    const errors = handleValidaton();

    if (errors.length !== 0) {
      btnLoading.value = false;

      let message = "";

      errors.forEach((err) => {
        message += err + "<br>";
      });

      toast.error(message, {
        autoClose: false,
        dangerouslyHTMLString: true
      });
      return;
    }

    const {
      action,
      symbol,
      sl,
      tp,
      ticket,
      volume,
      tp1,
      sl_trail,
      price,
      type_time,
      expiration
    } = trade.value;

    const data = {} as ITrade;

    data.action = action;

    if (action === ETradeOperationType.TRADE_ACTION_SLTP) {
      data.symbol = symbol;
    }

    data.sl = sl;
    data.tp = tp;
    data.ticket = ticket;

    const apiData = {} as ITrade;

    if (updateTP1.value) {
      apiData.tp1 = tp1;

      const volumeDecimalCount = countDecimals(volume);

      apiData.vol1 = parseFloat(
        ((tp1VolPercent.value / 100) * volume).toFixed(volumeDecimalCount)
      );

      apiData.tp1_status = "not_executed";
      apiData.vol1_status = "not_executed";
    } else {
      apiData.tp1 = 0;
      apiData.tp1_status = "not_executed";

      apiData.vol1 = 0;
      apiData.vol1_status = "not_executed";
    }

    if (updateSLTrail.value) {
      apiData.sl_trail = sl_trail;
    } else {
      apiData.sl_trail = 0;
    }

    const mt5Id = candlestickStore.eaAccount?.mt5_id;

    axios.put(`/orders/${props.ticket}/${mt5Id}`, apiData);

    if (action === ETradeOperationType.TRADE_ACTION_MODIFY) {
      data.price = price;
      data.type_time = type_time;

      if (type_time === EOrderTypeTime.ORDER_TIME_GTC) {
        data.expiration = DateTime.now().toFormat("dd.MM.yy HH:mm");
      } else if (type_time === EOrderTypeTime.ORDER_TIME_DAY) {
        data.expiration = DateTime.now().toFormat("dd.MM.yy");
      } else if (type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED) {
        data.expiration = DateTime.fromISO(expiration!).toFormat(
          "dd.MM.yy HH:mm"
        );
      } else if (type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED_DAY) {
        data.expiration = DateTime.fromISO(expiration!).toFormat("dd.MM.yy");
      }
    }

    eaSocket?.emit(`trade_data_${userStore.eaAccessToken}`, data);

    bottomNavDrawerStore.wasTradeRecentlyUpdated = true;

    bottomNavDrawerStore.toggleEditTradeModal(false);
  } catch (e) {
    const message = getServerErrors(e);
    toast.error(message[0]);
  } finally {
    btnLoading.value = false;
  }
}

function resetCustomFields(resetType: "update_tp1" | "update_sl_trail") {
  if (resetType === "update_tp1") {
    updateTP1AndTP1VOL1();
  }

  if (resetType === "update_sl_trail" && updateSLTrail.value) {
    trade.value.sl_trail = props.sl_trail ? props.sl_trail : 20;
  }
}

function updateTP1AndTP1VOL1() {
  if (updateTP1.value) {
    const { price, type, tp, volume, tp1, vol1 } = props;

    if (tp1) {
      trade.value.tp1 = tp1;
    } else {
      const priceDecimalCount = countDecimals(price);

      const sign = type === EOrderType.ORDER_TYPE_SELL ? -1 : 1;

      trade.value.tp1 = parseFloat(
        (price + sign * Math.abs((tp - price) / 2)).toFixed(priceDecimalCount)
      );
    }

    if (vol1) {
      if (trade.value.vol1_status === "executed") {
        trade.value.vol1 = volume * 0.5;
        tp1VolPercent.value = 50;
      } else {
        tp1VolPercent.value = Math.floor((vol1 * 100) / volume);
        trade.value.vol1 = vol1;
      }
    } else {
      trade.value.vol1 = volume * 0.5;
      tp1VolPercent.value = 50;
    }
  } else {
    // To remove TP1 line from chart
    trade.value.tp1 = 0;
  }
}
</script>

<template>
  <div
    id="edit-trade-modal"
    class="absolute z-20 w-72 rounded-md border bg-white text-sm shadow-lg"
    :style="{
      top: bottomNavDrawerStore.editTradeModalPosition.top + 'px',
      left: bottomNavDrawerStore.editTradeModalPosition.left + 'px'
    }"
  >
    <div
      id="edit-trade-modal-header"
      class="relative flex cursor-grab justify-end bg-gray-50 p-0.5"
    >
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-base font-bold"
      >
        Edit Trade
      </div>

      <NavItem @click="handleClose">
        <FontAwesomeIcon size="xl" icon="fa-solid fa-xmark" />
      </NavItem>
    </div>

    <form
      novalidate
      class="border-t p-2 px-3 pt-2.5"
      @submit.prevent="placeEditOrder"
    >
      <div
        class="mb-2"
        v-if="action === ETradeOperationType.TRADE_ACTION_MODIFY"
      >
        <InputLabel for="price">Price</InputLabel>

        <InputText
          min="0.1"
          id="price"
          type="number"
          :step="priceStep"
          v-model="trade.price"
        />
      </div>

      <div class="grid grid-cols-2 gap-x-4">
        <div>
          <InputLabel for="sl">SL</InputLabel>

          <InputText
            id="sl"
            min="0.1"
            type="number"
            :step="slStep"
            v-model="trade.sl"
          />
        </div>

        <div>
          <InputLabel for="tp">TP</InputLabel>

          <InputText
            id="tp"
            min="0.1"
            type="number"
            :step="tpStep"
            v-model="trade.tp"
          />
        </div>
      </div>

      <template v-if="action === ETradeOperationType.TRADE_ACTION_SLTP">
        <div class="mt-3 flex items-center gap-x-2">
          <Checkbox
            id="update_tp1"
            v-model="updateTP1"
            @change="resetCustomFields('update_tp1')"
          />

          <InputLabel for="update_tp1" class="!mb-0">
            Update TP1 and TP1 Vol
          </InputLabel>
        </div>

        <div class="mb-3 mt-1.5 grid grid-cols-2 gap-x-4" v-if="updateTP1">
          <div>
            <InputLabel for="tp1">TP1</InputLabel>

            <InputText
              min="0.1"
              id="tp1"
              type="number"
              :step="tpStep"
              v-model="trade.tp1"
            />
          </div>

          <div>
            <InputLabel for="tp1_vol">TP1 Vol</InputLabel>

            <div class="flex items-center gap-x-2">
              <InputText
                min="0.1"
                max="100"
                id="tp1_vol"
                type="number"
                v-model="tp1VolPercent"
                @change="updateTP1VolPercent"
              />

              <div>%</div>
            </div>
          </div>
        </div>

        <div class="mt-2 flex items-center gap-x-2">
          <Checkbox
            id="update_sl_trial"
            v-model="updateSLTrail"
            @change="resetCustomFields('update_sl_trail')"
          />

          <InputLabel for="update_sl_trial" class="!mb-0">
            Update SL Trail
          </InputLabel>
        </div>

        <div class="mt-1.5" v-if="updateSLTrail">
          <InputLabel for="sl_trail">SL Trail</InputLabel>

          <InputText
            min="1"
            type="number"
            id="sl_trail"
            v-model="trade.sl_trail"
          />
        </div>
      </template>

      <template v-if="action === ETradeOperationType.TRADE_ACTION_MODIFY">
        <div class="mt-2">
          <InputLabel for="type_time">Expiration</InputLabel>

          <Select
            id="type_time"
            :defaultOption="false"
            v-model="trade.type_time"
            @change="handleTimeType"
          >
            <option :value="exp.id" v-for="exp in timeTypeList" :key="exp.id">
              {{ exp.name }}
            </option>
          </Select>
        </div>

        <div
          class="mt-2"
          v-if="trade.type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED"
        >
          <InputLabel for="expiration">Date</InputLabel>

          <InputText
            id="expiration"
            type="datetime-local"
            :min="DateTime.now().toFormat('yyyy-MM-dd\'T\'HH:mm')"
            v-model="trade.expiration"
          />
        </div>

        <div
          class="mt-2"
          v-if="trade.type_time === EOrderTypeTime.ORDER_TIME_SPECIFIED_DAY"
        >
          <InputLabel for="expiration" class="self-center">Date</InputLabel>

          <InputText
            type="date"
            id="expiration"
            class="text-xs"
            :min="DateTime.now().toFormat('yyyy-MM-dd')"
            v-model="trade.expiration"
          />
        </div>
      </template>

      <div class="mt-3 flex justify-center">
        <PrimaryButton
          type="submit"
          class="flex w-full justify-center"
          :loading="btnLoading"
        >
          Update Trade
        </PrimaryButton>
      </div>
    </form>
  </div>
</template>
