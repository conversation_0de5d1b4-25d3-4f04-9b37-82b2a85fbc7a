<script setup lang="ts">
import Appbar from "@website/components/Appbar.vue";
import Footer from "@website/components/Footer.vue";

import PrimaryButton from "@/components/PrimaryButton.vue";
</script>

<template>
  <Appbar />

  <main class="grid grow place-items-center bg-accent">
    <div class="w-1/3 text-center">
      <div class="text-2xl">404 - Page Not Found</div>

      <div class="mt-3">
        Sorry, the page you're trying to access is not available at the moment,
        or the page may have been removed.
      </div>

      <div class="mt-5">
        <PrimaryButton @click="$router.replace({ name: 'home' })">
          Go to Home
        </PrimaryButton>
      </div>
    </div>
  </main>

  <Footer />
</template>

<style>
#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
</style>
