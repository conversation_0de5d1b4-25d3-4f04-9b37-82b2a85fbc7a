import { Events } from "@pipclimber/night-vision";
import { NightVisionLibs } from "../libs";
type Cursor = {
  x: number;
  y: number;
  ti: number;
  time: number;
  values: any;
  scales: {};
  quantizeTime(hub: any, layout: any, props: any): void;
  y2value(y: any, scale: any): number;
  getValue(paneId: any, ovId: any): any;
  hide(): void;
  visible: boolean;
};

type Colors = {
  back: string;
  grid: string;
  text: string;
};

type Layout = {
  $hi: number;
  $lo: number;
  step: number;
  width: number;
  height: number;
  spacex: number;
  scaleSpecs?: { log?: boolean };
  indexBased: boolean;
  candles?: any[]; // Assuming 'self.candles' is an array of candlestick data
  master_grid?: { candles: any[] }; // Assuming 'self.master_grid.candles' is an array of candlestick data
  A: number;
  B: number;
  tStep: number;
  prec: number;
  indexOffset?: number;
  ti: (t: number, i: number) => number;
  ti2x: (t: number, i: number) => number;
  time2x: (t: number) => number;
  value2y: (y: number) => number;
  tMagnet: (t: number) => void; // Function with undefined return type
  y2value: (y: number) => number;
  x2time: (x: number) => number;
  x2ti: (x: number) => number;
  $magnet: (price: number) => void; // Function with undefined return type
  cMagnet: (t: number) => any; // Return type depends on the structure of 'self.candles' or 'self.master_grid.candles'
  dataMagnet: (t: number) => void; // Function with undefined return type
};

type Props = {
  interval: number;
  timeFrame: number;
  timezone: number;
  showLogo: boolean;
  width: number;
  config: {
    lib: typeof NightVisionLibs;
  };
};

type DataItem = any; // Define this type according to your actual data structure

type DataSubset = DataItem[];

type DataExtItem = any; // Define this type according to your actual data structure

type DataExt = {
  lines?: DataExtItem[];
  // Add other properties if available
};

type Events = {
  /** Immediately calls all handlers with the specified type (there can be only one listener of this type per each component)*/
  emit(type: string, obj?: {}): void;
  /** Emit an event to a specific component */
  emitSpec(comp: any, type: string, obj: {}): void;
  /** Add an event listener to a specific component */
  on(compAndType: any, f: any): void;
  /** Remove event listener(s) from a specific component */
  off(comp: any, type?: string): void;
};
type Hub = {
  events: Events; // Define this type according to your actual data structure
  indexBased: boolean;
  data: any;
  mainPaneId: number;
  mainOv: {
    data: number[][];
    dataExt: any;
    dataSubset: number[][];
    dataView: any;
    id: any;
    main: boolean;
    name: string;
    props: {
      avgVolumeSMA: number;
      colorAvgVol: string;
      colorBodyDw: string;
      colorBodyUp: string;
      colorVolDw: string;
      colorVolUp: string;
      colorWickDw: string;
      colorWickUp: string;
      currencySymbol: string;
      priceLine: boolean;
      scaleSymbol: boolean;
      showAvgVolume: boolean;
      showValueTracker: boolean;
      showVolume: boolean;
    };
    settings: {
      scale: string;
    };
    type: string;
    uuid: string;
  };
  // Add other properties if available
};

type Src = {
  type: string;
  name: string;
  id: number;
  // Add other properties if available
};

export type TCoreData = {
  colors: Colors;
  cursor: Cursor;
  data: DataItem[];
  dataExt: DataExt;
  dataSubset: DataSubset;
  hub: Hub;
  id: number;
  indexOffset?: undefined;
  layout: Layout;
  meta: any; // Define this type according to your actual data structure
  paneId: number;
  props: Props;
  range: number[];
  scan: {
    main: Array<[number, number, number, number, number, number]>;
  }; // Define this type according to your actual data structure
  src: Src;
  uuid: string;
  view: any; // Define this type according to your actual data structure
};
