// Website
const WebsiteLayout = () => import("@website/Layout.vue");
const LandingPage = () => import("@website/pages/Landing.vue");
const TermsOfService = () => import("@website/pages/TermsOfService.vue");
const PrivacyPolicy = () => import("@website/pages/PrivacyPolicy.vue");
const CommunityVideos = () => import("@website/pages/CommunityVideos.vue");

const Login = () => import("@/pages/auth/Login.vue");
const Register = () => import("@/pages/auth/Register.vue");
// const ForgotPassword = () => import("@/pages/auth/ForgotPassword.vue")
const Home = () => import("@/pages/trading/Home.vue");
const RenewSubscription = () => import("@/pages/auth/RenewSubscription.vue");
const AccountLayout = () => import("@/pages/account/AccountLayout.vue");
const Dashboard = () => import("@/pages/account/components/Dashboard.vue");
const HowToSetup = () => import("@/pages/account/components/HowToSetup.vue");
const FAQ = () => import("@/pages/account/components/FAQ.vue");
const ContactUs = () => import("@/pages/account/components/ContactUs.vue");
const AccountSettings = () =>
  import("@/pages/account/components/AccountSettings.vue");
const Account = () =>
  import("@/pages/account/components/account-settings/Account.vue");
const ChangeEmail = () =>
  import("@/pages/account/components/account-settings/ChangeEmail.vue");
const ChangePhoneNumber = () =>
  import("@/pages/account/components/account-settings/ChangePhoneNumber.vue");
const ChangeLocation = () =>
  import("@/pages/account/components/account-settings/ChangeLocation.vue");
const ChangePassword = () =>
  import("@/pages/account/components/account-settings/ChangePassword.vue");
const EditProfile = () =>
  import("@/pages/account/components/account-settings/EditProfile.vue");
const EditPaymentInformation = () =>
  import(
    "@/pages/account/components/account-settings/EditPaymentInformation.vue"
  );
const PageNotFound = () => import("@/components/PageNotFound.vue");

export const routes = [
  {
    path: "/",
    component: WebsiteLayout,
    children: [
      {
        path: "/",
        name: "home",
        component: LandingPage
      },
      {
        path: "/legal/tos",
        name: "terms-of-service",
        component: TermsOfService
      },
      {
        path: "/community/videos",
        name: "community-videos",
        component: CommunityVideos
      },
      {
        path: "/legal/privacy_policy",
        name: "privacy-policy",
        component: PrivacyPolicy
      }
      // {
      //   path: "/forgot-password",
      //   name: "forgot-password",
      //   component: ForgotPassword,
      //   meta: { requiresGuest: true }
      // }
    ]
  },
  {
    path: "/login",
    name: "login",
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: "/register",
    name: "register",
    component: Register,
    meta: { requiresGuest: true }
  },

  {
    path: "/renew-subscription",
    name: "renew-subscription",
    component: RenewSubscription,
    meta: { requiresGuest: true }
  },
  {
    path: "/trading",
    name: "trading",
    component: Home,
    meta: { requiresAuth: true }
  },
  {
    path: "/",
    component: AccountLayout,
    children: [
      {
        path: "dashboard",
        name: "dashboard",
        component: Dashboard
      },
      {
        path: "setup",
        name: "how-to-setup",
        component: HowToSetup
      },
      {
        path: "faq",
        name: "faq",
        component: FAQ
      },
      {
        path: "contact",
        name: "contact-us",
        component: ContactUs
      },
      {
        path: "settings",
        name: "settings",
        redirect: "/settings/account",
        component: AccountSettings,
        children: [
          {
            path: "account",
            name: "account",
            component: Account
          },
          {
            path: "edit",
            name: "edit",
            component: EditProfile
          },
          {
            path: "email",
            name: "change-email",
            component: ChangeEmail
          },
          {
            path: "phone",
            name: "change-phone-number",
            component: ChangePhoneNumber
          },
          {
            path: "location",
            name: "change-location",
            component: ChangeLocation
          },
          {
            path: "password",
            name: "change-password",
            component: ChangePassword
          },
          {
            path: "payment",
            name: "edit-payment-information",
            component: EditPaymentInformation
          }
        ]
      }
    ],
    meta: { requiresAuth: true }
  },
  { path: "/:pathMatch(.*)*", name: "page-not-found", component: PageNotFound }
];
