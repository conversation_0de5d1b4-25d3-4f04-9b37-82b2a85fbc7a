/**
 * Convert hex color to RGBA.
 * @param hex Hex color code.
 * @param opacity Opacity of the color.
 * @returns Returns the equivalent RGBA color.
 */
export function hexToRGBA(hex: string, opacity: number) {
  const tempHex = hex.replace("#", "");

  if (tempHex.length === 3) {
    const r = parseInt(tempHex.substring(0, 1).repeat(2), 16);
    const g = parseInt(tempHex.substring(1, 2).repeat(2), 16);
    const b = parseInt(tempHex.substring(2, 3).repeat(2), 16);
    return `rgba(${r},${g},${b},${opacity / 100})`;
  }

  if (tempHex.length === 6) {
    const r = parseInt(tempHex.substring(0, 2), 16);
    const g = parseInt(tempHex.substring(2, 4), 16);
    const b = parseInt(tempHex.substring(4, 6), 16);
    return `rgba(${r},${g},${b},${opacity / 100})`;
  }

  return `rgba(${0},${0},${0},${opacity / 100})`;
}

/**
 * Convert hex color to RGBA.
 * @param rgba RGBA color.
 * @param forceRemoveAlpha Remove alpha value from the color (Default = false).
 * @returns Returns the equivalent hex color.
 */
export function rgbaToHex(rgba: string, forceRemoveAlpha = false) {
  return (
    "#" +
    rgba
      .replace(/^rgba?\(|\s+|\)$/g, "")
      .split(",")
      .filter((_, index) => !forceRemoveAlpha || index !== 3)
      .map((string) => parseFloat(string))
      .map((number, index) => (index === 3 ? Math.round(number * 255) : number))
      .map((number) => number.toString(16))
      .map((string) => (string.length === 1 ? "0" + string : string))
      .join("")
  );
}
