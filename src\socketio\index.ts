import { Socket, io } from "socket.io-client";

export let ohlcSocket: Socket | null;
export let eaSocket: Socket | null;
export let socket: Socket | null;

export function initializeOHLCSocket(accessToken: string) {
  ohlcSocket?.disconnect();

  ohlcSocket = io(import.meta.env.VITE_APP_SOCKET_OHLC_URL, {
    auth: {
      token: accessToken
    }
  });

  ohlcSocket.on("connect", () => {
    if (import.meta.env.DEV) {
      console.log("OHLC Socket Connection Established");
    }
  });

  ohlcSocket.on("disconnect", () => {
    if (import.meta.env.DEV) {
      console.log("OHLC Socket Connection Disconnected");
    }
  });

  ohlcSocket.on("connect_error", (err) => {
    if (import.meta.env.DEV) {
      // console.log("OHLC Socket Error: ", err);
    }
  });
}

export function initializeEASocket(eaToken: string) {
  eaSocket?.disconnect();

  eaSocket = io(import.meta.env.VITE_APP_SOCKET_TRADE_MARKET_URL, {
    auth: {
      token: eaToken
    }
  });

  eaSocket.on("connect", () => {
    if (import.meta.env.DEV) {
      console.log("EA Socket Connection Established");
    }
  });

  eaSocket.on("disconnect", () => {
    if (import.meta.env.DEV) {
      console.log("EA Socket Connection Disconnected");
    }
  });

  eaSocket.on("connect_error", (err) => {
    if (import.meta.env.DEV) {
      console.log("EA Socket Error: ", err);
    }
  });
}

export function initializeSocket(accessToken: string) {
  socket?.disconnect();

  if(import.meta.env.VITE_APP_ENV=="Local"){
    socket = io(import.meta.env.VITE_APP_SOCKET_URL_EVENTS_AND_NOTIFICATION, {
      auth: {
        token: accessToken
      },
      autoConnect: true, 
      // path: "/events-notification/socket.io",
      transports: ["websocket"], 
    });
  }else{
    socket = io(import.meta.env.VITE_APP_SOCKET_URL_EVENTS_AND_NOTIFICATION, {
      auth: {
        token: accessToken
      },
      autoConnect: true, 
      path: "/events-notification/socket.io", 
      transports: ["websocket"],
    });
  }

  socket.on("connect", () => {
    if (import.meta.env.DEV) {
      console.log("Socket Connection Established");
    }
  });

  socket.on("disconnect", () => {
    if (import.meta.env.DEV) {
      console.log("Socket Connection Disconnected");
    }
  });

  socket.on("connect_error", (err) => {
    if (import.meta.env.DEV) {
      console.log("Socket Error: ", err);
    }
  });
}
