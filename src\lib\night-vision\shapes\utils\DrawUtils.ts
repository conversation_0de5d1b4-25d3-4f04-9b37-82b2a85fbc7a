import { LabelStyle } from "../base/shape-properties.types";
import { BasicPoint } from "../base/Types";
import { getLabelPosition } from "./labelUtils";
import { LabelPosition } from "./PreferencesUtils";

/**
 * Draw Arrow Head at a point
 * @param ctx Canvas
 * @param p1 Point at which arrow head should be drawn
 * @param angle angle in degrees
 */
function drawArrowHead(
  ctx: CanvasRenderingContext2D,
  p1: BasicPoint,
  angle: number
) {
  const width = 12,
    height = 12;
  const arrowPath = [
    [-width, -height / 2],
    [0, 0],
    [-width, height / 2]
  ];
  ctx.save();
  ctx.translate(p1.x, p1.y);
  ctx.rotate(angle);

  ctx.beginPath();
  // Draw lines to each subsequent point
  for (let i = 0; i < arrowPath.length; i++) {
    ctx.lineTo(arrowPath[i][0], arrowPath[i][1]);
  }
  // Close the path
  ctx.closePath();

  // Fill the arrow (you can use stroke() instead if you want an outline)
  ctx.fill();
  ctx.restore();
}

interface DrawDetailsParams {
  ctx: CanvasRenderingContext2D;
  details: string[];
  styles: LabelStyle;
  labelPosition: LabelPosition;
  p1: BasicPoint;
  p2: BasicPoint;
}
export class DrawUtils {
  static pointStyles = {
    stroke: "#ffffff",
    fill: "#363556",
    roundRect: false,
    width: 3
  };
  static lineStyles = { stroke: "#ffffff", width: 2, dash: [1, 0] };
  static sidebarStyles = { fill: "#ffffff", text: "#000" };
  static botbarStyles = { fill: "#ffffff", text: "#000" };
  /**
   * Draw point (circle or round rect depending on pointStyle)
   */
  static drawPoint(
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    styles = this.pointStyles
  ) {
    ctx.strokeStyle = styles.stroke;
    ctx.fillStyle = styles.fill;
    ctx.beginPath();
    const halfWidth = styles.width / 2;

    if (styles.roundRect) {
      const borderRadius = halfWidth / 4;
      const rectX = x - halfWidth; // Adjust x to center the rectangle
      const rectY = y - halfWidth; // Adjust y to center the rectangle
      ctx.moveTo(rectX + borderRadius, rectY);
      ctx.arcTo(
        rectX + styles.width,
        rectY,
        rectX + styles.width,
        rectY + styles.width,
        borderRadius
      );
      ctx.arcTo(
        rectX + styles.width,
        rectY + styles.width,
        rectX,
        rectY + styles.width,
        borderRadius
      );
      ctx.arcTo(rectX, rectY + styles.width, rectX, rectY, borderRadius);
      ctx.arcTo(rectX, rectY, rectX + borderRadius, rectY, borderRadius);
      ctx.closePath();
    } else {
      ctx.arc(x, y, halfWidth, 0, Math.PI * 2, true);
    }
    ctx.fill();
    ctx.stroke();
    ctx.closePath();
  }

  static drawDetails({
    ctx,
    details,
    styles,
    labelPosition,
    p1,
    p2
  }: DrawDetailsParams) {
    const { bgColor, fontColor, fontFace, fontSize } = styles;

    ctx.beginPath();
    ctx.font = `${fontSize}px ${fontFace}`;
    ctx.letterSpacing = "0.2px";

    const textHeight = 14;
    const [paddingX, paddingY] = [8, 8];
    const lineSpacing = 8;

    let bgHeight = 0,
      bgWidth = 0;
    details.forEach((d) => {
      const { width } = ctx.measureText(d);
      bgWidth = bgWidth < width ? width : bgWidth;
      bgHeight += fontSize + lineSpacing;
    });

    // Apply padding
    bgWidth = bgWidth === 0 ? 0 : bgWidth + paddingX * 2;
    bgHeight = bgHeight === 0 ? 0 : bgHeight + paddingY * 2;

    const labelCoordinates = getLabelPosition({
      bgHeight,
      bgWidth,
      position: labelPosition,
      p1,
      p2
    });

    // Draw bg
    ctx.fillStyle = bgColor + "ee";
    ctx.roundRect(
      labelCoordinates.x,
      labelCoordinates.y,
      bgWidth,
      -bgHeight,
      5
    );
    ctx.fill();

    // Draw text
    ctx.fillStyle = fontColor;
    let currentY = labelCoordinates.y - bgHeight + paddingY + textHeight;
    const inc: number = 22;
    details.forEach((text) => {
      ctx.fillText(text, labelCoordinates.x + paddingX, currentY);
      currentY += inc;
    });
  }

  static drawArrow(
    ctx: CanvasRenderingContext2D,
    p1: { x: number; y: number },
    p2: { x: number; y: number },
    styles = this.lineStyles
  ) {
    this.drawLine(ctx, p1, p2, styles);
    const length = 5; // You can adjust this value as needed
    // End line ⊥
    // Vertical line
    if (p2.x - p1.x === 0) {
      this.drawLine(
        ctx,
        { x: p1.x - length, y: p1.y },
        { x: p1.x + length, y: p1.y },
        styles
      );
      ctx.fillStyle = styles.stroke;
      ctx.strokeStyle = styles.stroke;
    }
    if (p2.y - p1.y === 0) {
      this.drawLine(
        ctx,
        { x: p1.x, y: p1.y - length },
        { x: p1.x, y: p1.y + length },
        styles
      );
      ctx.fillStyle = styles.stroke;
      ctx.strokeStyle = styles.stroke;
    }
    drawArrowHead(ctx, p2, Math.atan2(p2.y - p1.y, p2.x - p1.x));
    // // Horizontal line
    // if (p2.x - p1.x === 0) {
    //   // End line ⊥
    //   this.drawLine(
    //     ctx,
    //     { x: p1.x, y: p1.y - length },
    //     { x: p1.x, y: p1.y + length },
    //     styles
    //   )
    //   drawArrowHead(ctx, p2, 90);
    //   return
    // }
    // const slope = (p2.y - p1.y) / (p1.x - p2.x)
    // const perpendicularSlope = -1 / slope
    // const newX = length / Math.sqrt(1 + perpendicularSlope * perpendicularSlope)
    // const newY = perpendicularSlope * newX
    // ctx.beginPath()
    // ctx.moveTo(p1.x - newX, p1.y - newY)
    // ctx.lineTo(newX + p1.x, newY + p1.y)
    // ctx.stroke()
    // ctx.closePath()
  }
  /**
   * Draw line on canvas
   */
  static drawLine(
    ctx: CanvasRenderingContext2D,
    p1: { x: number; y: number },
    p2: { x: number; y: number },
    styles = this.lineStyles
  ) {
    ctx.setLineDash(styles.dash); // For straight lines
    //                  _____________________________________________
    // For dashed lines | 0 -> length of dash | 1  -> length of gap |
    //                  ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞ ͞
    // ctx.setLineDash([5,4]);
    ctx.strokeStyle = styles.stroke;
    ctx.lineWidth = styles.width;
    ctx.beginPath();
    ctx.moveTo(p1.x, p1.y);
    ctx.lineTo(p2.x, p2.y);
    ctx.stroke();
    ctx.setLineDash([]);
  }
  /**
   * Draw rectangle on canvas
   */
  static drawRect(
    ctx: CanvasRenderingContext2D,
    p1: { x: number; y: number },
    size: { w: number; h: number },
    styles = this.lineStyles
  ) {
    ctx.strokeStyle = styles.stroke;
    ctx.lineWidth = styles.width;
    ctx.strokeRect(p1.x, p1.y, size.w, size.h);
  }
  /**
   * Draw pill shape label on sidebar
   */
  static drawSidebar(
    ctx: CanvasRenderingContext2D,
    label: string,
    y: number,
    styles = this.sidebarStyles
  ) {
    ctx.beginPath();
    ctx.fillStyle = styles.fill;
    const sidebarW = ctx.canvas.clientWidth || ctx.canvas.width;
    const pillH = 18;
    const rectRad = [0, 4, 4, 0];
    ctx.roundRect(1, y - pillH / 2, sidebarW - 5, pillH, rectRad);
    ctx.fill();
    ctx.fillStyle = styles.text;
    ctx.fillText(label, 7, y + 4);
  }
  /**
   * Draw pill shape label on bottom-bar
   */
  static drawBotbar(
    ctx: CanvasRenderingContext2D,
    label: string,
    x: number,
    styles = this.sidebarStyles
  ) {
    ctx.beginPath();
    ctx.fillStyle = styles.fill;
    const sidebarH = ctx.canvas.clientHeight || ctx.canvas.height;
    const pillW = ctx.measureText(label).width + 18;
    const rectRad = [0, 0, 4, 4];
    ctx.roundRect(x - pillW / 2, 1, pillW, sidebarH - 6, rectRad);
    // ctx.roundRect(1, y - pillH / 2, sidebarW - 5, pillH, rectRad)
    ctx.fill();
    ctx.fillStyle = styles.text;
    ctx.fillText(label, x, 16);
  }
}
