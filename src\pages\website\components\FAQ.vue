<script setup lang="ts">
import { onMounted, ref } from "vue";
import { initFlowbite } from "flowbite";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { useUserStore } from "@/store/userStore";

const userStore = useUserStore();

const faqList = ref([
  {
    question: "How to set up Tradeawaay Expert Advisor in MetaTrader 5?",
    answer: `You can set up Expert Advisor in MetaTrader 5 by watching video tutorial (look for "Watch the video" button above).`
  },
  {
    question: "What instruments can I use for trading?",
    answer: `Our platform supports different number of instruments which are listed below:<br/>Major Currency Pairs<br/>AUDUSD, EURUSD, GBPUSD, NZDUSD, USDCAD, USDJPY, USDCHF, AUDJPY, CHFJPY, GBPJPY, EURJPY, CADJPY, NZDJPY, EURAUD, EURCAD, EURGBP, EURNZD, EURCHF, GBPCAD, GBPAUD, GBPNZD, GBPCHF, AUDCHF, CADCHF, NZDCHF, AUDCAD, AUDNZD, NZDCAD<br/><br/>CFD INDICES: SPX500, NAS100, US30, DAX40, UK100<br/><br/>CRYPTO: BTCUSD<br/><br/>Commodities: XAUUSD, XAGUSD, BRENT, WTI<br/><br/>We will be adding more instruments in the future. 
`
  },
  {
    question: "What brokers can I use for trading?",
    answer:
      "Our platform supports different number of brokers for trading which are listed below:<br/>EightCap, FTMO, ThinkMarket, Purple Trading, Fintokei, Blueberry, and Monevis.<br><br>If your broker is not on the list, please email us with the details and we can onboard your broker."
  },
  {
    question:
      "What is the connection speed between Tradeawaay and my trading terminal?",
    answer:
      "From our testing, the typical speed of MetaTrader 5 is around 1 millisecond (ms). However, this can vary depending on the speed of your internet connection. If you experience slower speeds, we recommend hosting MetaTrader 5 in the cloud. This setup offers a more stable and faster connection. Cloud servers can be set up for as little as $15/month. If you need assistance with setting up your cloud server, feel free to contact us for support."
  },
  {
    question: "What about chart data provider and market watch?",
    answer:
      "We have multiple data provider, all of them are top-tier brokers. We monitor market price data and switch our chart data between brokers to accurately reflect market prices. The bid and ask prices on the market watch, as well as order data, are streamed directly from your terminal for real-time updates."
  },
  {
    question: "Can I use chart in multiple windows?",
    answer:
      "Currently, our platform does not support multiple chart windows. However, we do have plan for implementing such feature in the future."
  },
  {
    question: "My trading terminal is not working, how can I troubleshoot it?",
    answer: `If your trading terminal is not working, here are some steps you can follow to troubleshoot the issue:<br/><ol class="list-decimal px-4 mt-1"><li><span class="font-medium text-black">Check Broker Support:</span> Ensure that your broker is supported by our platform. If your broker is not currently supported, please email us, and we'll work on adding support for it as soon as possible.</li><li><span class="font-medium text-black">Verify Expert Advisor:</span> Make sure that your Expert Advisor (EA) is running in your MetaTrader 5 (MT5) terminal and that you are logged into your broker account.</li><li><span class="font-medium text-black">Refesh Market Watch:</span> Click the refresh button in the Market Watch panel to update the data.</li></ol><br/>If the problem persists, please email us with the details, and we will investigate further to resolve the issue.`
  },
  {
    question: "Are indicators available?",
    answer:
      "Currently, our platform does not include indicators. It is designed for price action trading, which means you trade based on the price movements you see on the charts. However, we do plan to implement price-action indicators in the future, including algorithm-driven pattern recognition, statistical price data, and more."
  }
]);

onMounted(() => {
  initFlowbite();
});
</script>

<template>
  <section
    id="faq"
    class="bg-accent pb-16"
    :class="{ '!pt-0': userStore.user }"
  >
    <div class="container mx-auto px-5">
      <h2 class="text-center text-4xl font-semibold">
        <span class="border-b-4 border-blue-700 pb-4">FAQ</span>
      </h2>

      <p class="mt-12 text-center text-gray-600">
        Have your questions? We've got you covered!
      </p>

      <div
        class="mt-7"
        id="faq-accordion"
        data-accordion="collapse"
        data-active-classes="!text-blue-700"
      >
        <template v-for="(faq, idx) in faqList">
          <h2 class="pt-2.5" :id="`accordion-faq-heading-${idx}`">
            <button
              type="button"
              aria-expanded="false"
              class="flex w-full cursor-pointer items-center justify-between gap-x-5 bg-white px-5 pb-4 pt-5 font-medium text-gray-600 hover:text-primary md:px-7"
              :aria-controls="`accordion-faq-body-${idx}`"
              :data-accordion-target="`#accordion-faq-body-${idx}`"
            >
              <div class="text-left">{{ faq.question }}</div>

              <FontAwesomeIcon icon="fa-solid fa-chevron-down" />
            </button>
          </h2>

          <div
            class="hidden bg-white"
            :id="`accordion-faq-body-${idx}`"
            :aria-labelledby="`accordion-faq-heading-${idx}`"
          >
            <div
              class="px-5 pb-5 text-sm text-gray-700 md:px-7"
              v-html="faq.answer"
            ></div>
          </div>
        </template>
      </div>
    </div>
  </section>
</template>
