<script setup lang="ts">
import { onMounted, ref, useTemplateRef, watch } from "vue";
import { onClickOutside } from "@vueuse/core";
import { cloneDeep } from "lodash-es";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { ILine } from "@/lib/night-vision/shapes/base/shape-properties.types";
import {
  BoxProperties,
  ILineProperties
} from "@/lib/night-vision/shapes/utils/PreferencesUtils";

import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";
import { useDragElement } from "@/composables/useDragElement";
import { countDecimals } from "@/helpers/numberUtils";
import { kebabToTitleCase } from "@/helpers/formatString";
import { useChartStore } from "@/store/chartStore";
import { useAppbarStore } from "@/store/appbarStore";

import LineColor from "./components/LineColor.vue";
import LineWidth from "./components/LineWidth.vue";
import LineType from "./components/LineType.vue";
import FIB from "./fib/FIB.vue";
import Text from "./text/Text.vue";

import NavItem from "@/components/NavItem.vue";
import Checkbox from "@/components/Checkbox.vue";
import Dropdown from "@/components/Dropdown.vue";
import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import ColorPicker from "@/components/ColorPicker.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const chartStore = useChartStore();
const appbarStore = useAppbarStore();

const { top: topPos, left: leftPos } = useDragElement(
  "chart-settings-modal",
  "chart-settings-modal-header"
);

const selectedShape = chartStore.selectedShape;

const chartModalRef = useTemplateRef("chart-modal");
const toggleFeatures = ref(["extend_line", "labels", "label_position"]);
const backgroundColorDropdown = ref(false);
const selectedBackgroundColor = ref("");
const isLeftLineExtended = ref(false);
const isRightLineExtended = ref(false);
const shapeCoords = ref();
const priceStep = ref(1);
const labelProperties = ref([
  {
    id: "show_label",
    name: "Show Label",
    value: false
  },
  {
    id: "show_price_range",
    name: "Price range",
    value: false
  },
  {
    id: "show_percent_change",
    name: "Percent change",
    value: false
  },
  {
    id: "show_change_in_pips",
    name: "Change in pips",
    value: false
  },
  {
    id: "show_diff",
    name: "Diff",
    value: false
  },
  {
    id: "show_bars_range",
    name: "Bars range",
    value: false
  },
  {
    id: "show_date_time_range",
    name: "Date/time range",
    value: false
  },
  {
    id: "show_distance",
    name: "Distance",
    value: false
  },
  {
    id: "show_angle",
    name: "Angle",
    value: false
  }
]);
const labelPositionDropdown = ref(false);
const labelPositions = ref(["left", "center", "right"]);
const selectedLabelPosition = ref("");

let aa = false;

onClickOutside(chartModalRef, () => {
  if (aa) {
    appbarStore.toggleModal("chartSettingsModal", false);
  } else {
    aa = true;
  }
});

// @ts-expect-error
const isFib = ref(selectedShape.type === "fib-level");
// @ts-expect-error
const isText = ref(selectedShape.type === "text");

let previousShapeSettings = {} as {
  properties: ILine;
  backgroundProperties: string;
  coords: any;
};

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set chart settings");
  }

  if (selectedShape.type === "fib-level" || selectedShape.type === "text") {
    return;
  }

  const { lineProperties, labelProperties } =
    selectedShape.properties as ILineProperties;

  if (selectedShape.type === "box") {
    selectedBackgroundColor.value = (
      selectedShape.properties as BoxProperties
    ).backgroundProperties.fill_color;
  }

  isLeftLineExtended.value = lineProperties.extend_dir_1;
  isRightLineExtended.value = lineProperties.extend_dir_2;

  shapeCoords.value = selectedShape.getCoordinates();
  initializePriceStep();

  if (
    selectedShape.type === "horizontal-line" ||
    selectedShape.type === "vertical-line" ||
    selectedShape.type === "crosshair"
  ) {
    toggleFeatures.value = [];
  } else if (selectedShape.type === "box") {
    toggleFeatures.value = ["background"];
  } else if (
    selectedShape.type === "price-range" ||
    selectedShape.type === "date-range" ||
    selectedShape.type === "date-and-price-range"
  ) {
    toggleFeatures.value = ["labels"];
  }

  if (toggleFeatures.value.includes("labels")) {
    intializeShapeLabelProperties();
    selectedLabelPosition.value = labelProperties.label_position;
  }

  previousShapeSettings = {
    properties: cloneDeep(lineProperties),
    backgroundProperties: selectedBackgroundColor.value,
    coords: selectedShape.getCoordinates()
  };
});

watch([topPos, leftPos], ([topPos, leftPos]) => {
  chartStore.setChartModalPosition(topPos, leftPos);
});

function initializePriceStep() {
  let priceDecimalCount = 1;

  if ("endPoint1" in shapeCoords.value) {
    priceDecimalCount = countDecimals(shapeCoords.value.endPoint1.price);
  } else if ("price" in shapeCoords.value) {
    priceDecimalCount = countDecimals(shapeCoords.value.price);
  } else if ("row" in shapeCoords.value) {
    priceDecimalCount = countDecimals(shapeCoords.value.row);
  } else if ("diagonalEndpoint1" in shapeCoords.value) {
    priceDecimalCount = countDecimals(
      shapeCoords.value.diagonalEndpoint1.price
    );
  } else if ("coords" in shapeCoords.value) {
    priceDecimalCount = countDecimals(shapeCoords.value.coords.price);
  }

  priceStep.value = 1 / Math.pow(10, priceDecimalCount);
}

function intializeShapeLabelProperties() {
  labelProperties.value.forEach((label) => {
    // @ts-expect-error
    label.value = selectedShape.properties.labelProperties[label.id];
  });

  if (selectedShape?.type === "price-range") {
    labelProperties.value = labelProperties.value.filter((_, idx) => {
      return idx < 5;
    });
  }

  if (selectedShape?.type === "date-range") {
    const ids = [0, 5, 6, 7];

    labelProperties.value = labelProperties.value.filter((_, idx) => {
      return ids.includes(idx);
    });
  }

  if (selectedShape?.type === "date-and-price-range") {
    labelProperties.value = labelProperties.value.filter((_, idx) => {
      return idx < 7;
    });
  }
}

function closeModal() {
  appbarStore.toggleModal("chartSettingsModal", false);
}

function handleBackgroundColor(color: string) {
  if (!chartStore.chart) {
    throw new ChartInstanceError("Cannot change background color");
  }

  selectedBackgroundColor.value = color;

  // @ts-expect-error
  selectedShape.setProperty("backgroundProperties", "fill_color", color);

  chartStore.chart.update();
}

function handleLeftLineExtend() {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to extend shape left line");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to extend shape left line");
  }

  selectedShape.setProperty(
    "lineProperties",
    "extend_dir_1",
    isLeftLineExtended.value
  );

  chartStore.chart.update();
}

function handleRightLineExtend() {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to extend shape right line");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to extend shape right line");
  }

  selectedShape.setProperty(
    "lineProperties",
    "extend_dir_2",
    isRightLineExtended.value
  );

  chartStore.chart.update();
}

function updateCoordinates(endpointName: string) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to update shape coordinates");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to update shape coordinates");
  }

  const endPoint = shapeCoords.value[endpointName];

  selectedShape.setCoordinates(endpointName, endPoint);

  chartStore.chart.update();
}

function handleLabelProperties(label: keyof ILine, value: boolean) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to change shape label properties");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to change shape label properties");
  }

  selectedShape.setProperty("labelProperties", label, value);

  chartStore.chart.update();
}

function handlePositionChange(position: string) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to change shape label position");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to change shape label position");
  }

  selectedLabelPosition.value = position;

  labelPositionDropdown.value = false;

  selectedShape.setProperty("labelProperties", "label_position", position);

  chartStore.chart.update();
}

function restorePreviousSettings() {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to reset previous chart settings");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to reset previous chart settings");
  }

  for (const [k, v] of Object.entries(previousShapeSettings.properties)) {
    selectedShape.setProperty("labelProperties", k as keyof ILine, v);
  }

  for (let [key, value] of Object.entries(previousShapeSettings.coords)) {
    selectedShape.setCoordinates(key, value);
  }

  if (selectedShape.type === "box") {
    selectedShape.setProperty(
      "backgroundProperties",
      "fill_color",
      previousShapeSettings.backgroundProperties
    );
  }

  chartStore.chart.update();

  closeModal();
}
</script>

<template>
  <div
    ref="chart-modal"
    id="chart-settings-modal"
    class="absolute z-10 w-[350px] cursor-default rounded-md border bg-white text-sm shadow-lg"
    :style="{
      top: chartStore.chartModalPosition.top + 'px',
      left: chartStore.chartModalPosition.left + 'px'
    }"
  >
    <div
      id="chart-settings-modal-header"
      class="relative flex cursor-grab justify-end p-0.5"
    >
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-base font-semibold"
      >
        {{ kebabToTitleCase(chartStore.selectedShape?.type ?? "") }}
      </div>

      <NavItem class="px-2.5" @click="closeModal">
        <FontAwesomeIcon icon="fa fa-solid fa-xmark" size="lg" />
      </NavItem>
    </div>

    <FIB v-if="isFib" />

    <Text v-else-if="isText" />

    <template v-else>
      <div class="scrollbar h-[300px] overflow-auto border-t px-4 py-3">
        <div class="grid grid-cols-12 items-center">
          <div class="col-span-4">Line</div>

          <div class="col-span-8 flex gap-x-3">
            <LineColor />

            <LineWidth />

            <LineType />
          </div>
        </div>

        <div
          class="mt-3 grid grid-cols-12 items-center"
          v-if="toggleFeatures.includes('background')"
        >
          <div class="col-span-4">Background</div>

          <div class="col-span-8">
            <Dropdown
              id="chart-modal-fib-one-color-dropdown"
              toggle-id="chart-modal-fib-one-color-toggle-dropdown"
              class="flex h-10 w-10 items-center justify-center border"
              :class="{
                'border-info': backgroundColorDropdown
              }"
              :icon="false"
              :offset-skidding="110"
              @show="backgroundColorDropdown = true"
              @hide="backgroundColorDropdown = false"
            >
              <template #text>
                <img
                  width="24"
                  height="22"
                  src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQbYhroPyAlU14fdkwhbx3ullUY4SPp3lOjpq2HA-K3An2FbdJaxhvCjSiHbmZNUvRPzPE&usqp=CAU"
                  alt="Transparent Image"
                  v-if="selectedBackgroundColor.slice(0, 7) === '#ffffff'"
                />

                <div
                  class="h-[22px] w-[24px]"
                  :style="{
                    backgroundColor: selectedBackgroundColor.slice(0, 7)
                  }"
                  v-else
                ></div>
              </template>

              <template #content>
                <div>
                  <ColorPicker
                    :color="selectedBackgroundColor"
                    :show-opacity="true"
                    @update-color="handleBackgroundColor"
                  />
                </div>
              </template>
            </Dropdown>
          </div>
        </div>

        <template v-if="toggleFeatures.includes('extend_line')">
          <div class="mt-4 flex items-center gap-x-3">
            <Checkbox
              id="extend_left_line"
              v-model="isLeftLineExtended"
              @change="handleLeftLineExtend"
            />

            <InputLabel for="extend_left_line">Extend left</InputLabel>
          </div>

          <div class="mt-3 flex items-center gap-x-3">
            <Checkbox
              id="extend_right_line"
              v-model="isRightLineExtended"
              @change="handleRightLineExtend"
            />

            <InputLabel for="extend_right_line">Extend right</InputLabel>
          </div>
        </template>

        <div class="mt-4">Coordinates</div>

        <div class="flex flex-col gap-y-1">
          <template
            v-for="(endPoint, endPointName, idx) in shapeCoords"
            :key="endPointName"
          >
            <div
              class="grid grid-cols-12 items-center"
              v-if="typeof endPoint === 'object'"
            >
              <InputLabel class="col-span-4" :for="`coords-label-${idx + 1}`">
                #{{ idx + 1 }} ({{ Object.keys(endPoint).join(", ") }})
              </InputLabel>

              <div class="col-span-8 flex gap-x-2">
                <InputText
                  min="0"
                  type="number"
                  :id="`coords-label-${idx + 1}`"
                  v-model="endPoint.row"
                  @input="updateCoordinates(endPointName.toString())"
                />

                <InputText
                  min="0"
                  type="number"
                  :step="priceStep"
                  v-model="endPoint.price"
                  @input="updateCoordinates(endPointName.toString())"
                />
              </div>
            </div>

            <div class="grid grid-cols-12 items-center" v-else>
              <InputLabel class="col-span-4" for="coords-label">
                #{{ idx + 1 }} ({{ Object.keys(shapeCoords).join("") }})
              </InputLabel>

              <div class="col-span-5">
                <InputText
                  min="0"
                  type="number"
                  id="coords-label"
                  :step="priceStep"
                  v-model="shapeCoords[endPointName]"
                  @input="updateCoordinates(endPointName.toString())"
                />
              </div>
            </div>
          </template>
        </div>

        <template v-if="toggleFeatures.includes('labels')">
          <div class="mt-2">Labels</div>

          <div class="mt-2 grid grid-cols-2 gap-y-2">
            <div class="col-span-2 flex gap-x-3">
              <Checkbox
                :id="`label_properties_${labelProperties[0].id}`"
                v-model="labelProperties[0].value"
                @change="
                  handleLabelProperties(
                    labelProperties[0].id as keyof ILine,
                    labelProperties[0].value
                  )
                "
              />

              <InputLabel :for="`label_properties_${labelProperties[0].id}`">
                {{ labelProperties[0].name }}
              </InputLabel>
            </div>

            <div
              class="flex gap-x-3"
              v-for="label in labelProperties.slice(1)"
              :key="label.id"
            >
              <Checkbox
                :id="`label_properties_${label.id}`"
                v-model="label.value"
                @change="
                  handleLabelProperties(label.id as keyof ILine, label.value)
                "
              />

              <InputLabel :for="`label_properties_${label.id}`">
                {{ label.name }}
              </InputLabel>
            </div>
          </div>
        </template>

        <template v-if="toggleFeatures.includes('label_position')">
          <div class="mt-3 grid grid-cols-12 items-center gap-x-3">
            <div class="col-span-4">Position</div>

            <Dropdown
              id="label-position-dropdown"
              toggle-id="label-position-toggle-dropdown"
              class="col-span-4 flex items-center justify-between border"
              placement="top"
              :class="{ 'border-info': labelPositionDropdown }"
              :offset-skidding="9"
              @show="labelPositionDropdown = true"
              @hide="labelPositionDropdown = false"
            >
              <template #text v-if="selectedLabelPosition">
                {{
                  selectedLabelPosition[0].toUpperCase() +
                  selectedLabelPosition.slice(1)
                }}
              </template>

              <template #content="{ close }">
                <div class="my-1 w-32">
                  <div
                    class="px-3 pb-1.5 pt-2 hover:bg-accent"
                    :class="{
                      'bg-selected text-white hover:bg-selected':
                        selectedLabelPosition === position
                    }"
                    v-for="position in labelPositions"
                    :key="position"
                    @click="(close(), handlePositionChange(position))"
                  >
                    {{ position[0].toUpperCase() + position.slice(1) }}
                  </div>
                </div>
              </template>
            </Dropdown>
          </div>
        </template>
      </div>

      <div class="flex justify-end gap-x-3 border-t px-3 pb-1.5 pt-2">
        <PrimaryButton class="px-3" @click="closeModal">OK</PrimaryButton>

        <PrimaryButton
          class="border border-info bg-white !text-info hover:border-secondary hover:!text-white"
          @click="restorePreviousSettings"
        >
          Discard
        </PrimaryButton>
      </div>
    </template>
  </div>
</template>

<style scoped>
label {
  margin-bottom: 0;
  font-weight: normal;
}
</style>
