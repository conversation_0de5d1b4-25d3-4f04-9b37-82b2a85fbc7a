<script setup lang="ts">
import { ref } from "vue";

import Dropdown from "@/components/Dropdown.vue";

import { axios } from "@/api";
import { useChartStore } from "@/store/chartStore";
import { useCandleStickStore } from "@/store/candleStickStore";
import { BoxPlotData } from "@/types";

import CogOutlineSVG from "@/assets/svg/cog-outline.svg";
import CheckSVG from "@/assets/svg/check.svg";

const chartStore = useChartStore();
const candleStickStore = useCandleStickStore();

const toggleOHLCVolume = ref(candleStickStore.chartSettings.volume);
const marketCloseLine = ref(candleStickStore.chartSettings.marketCloseLine);
const newYorkMarket = ref(candleStickStore.chartSettings.newYorkMarket);
const asiaMarket = ref(candleStickStore.chartSettings.asiaMarket);
const londonMarket = ref(candleStickStore.chartSettings.londonMarket);

async function handleBoxPlot(boxPlotType: "box_plot_high" | "box_plot_low") {
  if (
    candleStickStore.interval === "PERIOD_D1" ||
    candleStickStore.interval === "PERIOD_W1"
  ) {
    return;
  }

  // For toggling box plot on/off
  if (candleStickStore.symbol === candleStickStore.boxPlot.symbol) {
    if (boxPlotType === "box_plot_high") {
      if (candleStickStore.boxPlot.boxPlotHigh) {
        candleStickStore.boxPlot.boxPlotHigh = false;
        chartStore.setBoxPlotValues(false, boxPlotType);
      } else {
        candleStickStore.boxPlot.boxPlotHigh = true;
        chartStore.setBoxPlotValues(
          true,
          boxPlotType,
          candleStickStore.boxPlotData!
        );
      }
    }

    if (boxPlotType === "box_plot_low") {
      if (candleStickStore.boxPlot.boxPlotLow) {
        candleStickStore.boxPlot.boxPlotLow = false;
        chartStore.setBoxPlotValues(false, boxPlotType);
      } else {
        candleStickStore.boxPlot.boxPlotLow = true;
        chartStore.setBoxPlotValues(
          true,
          boxPlotType,
          candleStickStore.boxPlotData!
        );
      }
    }

    return;
  }

  try {
    if (boxPlotType === "box_plot_high") {
      candleStickStore.boxPlot.boxPlotHigh = true;
    } else {
      candleStickStore.boxPlot.boxPlotLow = true;
    }

    const data = {
      symbol: candleStickStore.symbol,
      highPrice: candleStickStore.boxPlot.high_price,
      lowPrice: candleStickStore.boxPlot.low_price,
      broker: candleStickStore.symbolBroker
    };

    const resp = await axios.post("/indicators/box-plot", data);

    const boxPlotData: BoxPlotData = resp.data.data;

    candleStickStore.boxPlot.symbol = candleStickStore.symbol;
    candleStickStore.boxPlotData = boxPlotData;

    chartStore.setBoxPlotValues(true, boxPlotType, boxPlotData);
  } catch (e) {
    candleStickStore.boxPlot.boxPlotHigh = false;
    candleStickStore.boxPlot.boxPlotLow = false;
  }
}

function toggleVolume() {
  if (candleStickStore.chartSettings.volume) {
    chartStore.toggleVolume(false);

    candleStickStore.storeChartSettings({
      volume: false
    });

    toggleOHLCVolume.value = false;

    return;
  }

  chartStore.toggleVolume(true);

  candleStickStore.storeChartSettings({
    volume: true
  });

  toggleOHLCVolume.value = true;
}

function toggleMarketCloseLine(market: "ny" | "as" | "ln" | "close") {
  if (market === "ny") {
    newYorkMarket.value = !newYorkMarket.value;
    chartStore.toggleMarketClosingLines(newYorkMarket.value, market);
  } else if (market === "as") {
    asiaMarket.value = !asiaMarket.value;
    chartStore.toggleMarketClosingLines(asiaMarket.value, market);
  } else if (market === "ln") {
    londonMarket.value = !londonMarket.value;
    chartStore.toggleMarketClosingLines(londonMarket.value, market);
  } else if (market === "close") {
    marketCloseLine.value = !marketCloseLine.value;
    chartStore.toggleMarketClosingLines(marketCloseLine.value, market);
  }

  candleStickStore.storeChartSettings({
    marketCloseLine: marketCloseLine.value
  });
}
</script>

<template>
  <Dropdown
    id="chart-settings-dropdown"
    toggle-id="chart-settings-toggle-dropdown"
    class="rounded-none"
    placement="top-start"
    :icon="false"
  >
    <template #text>
      <CogOutlineSVG />
    </template>

    <template #content>
      <div class="my-1 h-64 w-52 cursor-default select-none">
        <div
          class="flex px-3 pb-1.5 pt-2 hover:bg-accent"
          :class="{
            'opacity-50':
              candleStickStore.interval === 'PERIOD_D1' ||
              candleStickStore.interval === 'PERIOD_W1'
          }"
          @click="handleBoxPlot('box_plot_high')"
        >
          <div class="w-9">
            <CheckSVG v-show="candleStickStore.boxPlot.boxPlotHigh" />
          </div>

          <div>Box Plot (High, n=30)</div>
        </div>

        <div
          class="flex px-3 pb-1.5 pt-2 hover:bg-accent"
          :class="{
            'opacity-50':
              candleStickStore.interval === 'PERIOD_D1' ||
              candleStickStore.interval === 'PERIOD_W1'
          }"
          @click="handleBoxPlot('box_plot_low')"
        >
          <div class="w-9">
            <CheckSVG v-show="candleStickStore.boxPlot.boxPlotLow" />
          </div>

          <div>Box Plot (Low, n=30)</div>
        </div>

        <div
          class="flex px-3 pb-1.5 pt-2 hover:bg-accent"
          @click="toggleVolume"
        >
          <div class="w-9">
            <CheckSVG v-show="toggleOHLCVolume" />
          </div>

          <div>Volume</div>
        </div>

        <div class="mt-2 flex px-3 text-xs">Breaks</div>

        <div
          class="flex px-3 pb-1.5 pt-2 hover:bg-accent"
          @click="toggleMarketCloseLine('close')"
        >
          <div class="w-9">
            <CheckSVG v-show="marketCloseLine" />
          </div>

          <div>Session</div>
        </div>

        <div
          class="flex px-3 pb-1.5 pt-2 hover:bg-accent"
          @click="toggleMarketCloseLine('ny')"
        >
          <div class="w-9">
            <CheckSVG v-show="newYorkMarket" />
          </div>

          <div>New York</div>
        </div>

        <div
          class="flex px-3 pb-1.5 pt-2 hover:bg-accent"
          @click="toggleMarketCloseLine('as')"
        >
          <div class="w-9">
            <CheckSVG v-show="asiaMarket" />
          </div>

          <div>Asia</div>
        </div>

        <div
          class="flex px-3 pb-1.5 pt-2 hover:bg-accent"
          @click="toggleMarketCloseLine('ln')"
        >
          <div class="w-9">
            <CheckSVG v-show="londonMarket" />
          </div>

          <div>London</div>
        </div>
      </div>
    </template>
  </Dropdown>
</template>
