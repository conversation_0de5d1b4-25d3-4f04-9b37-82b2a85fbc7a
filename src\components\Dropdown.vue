<script setup lang="ts">
import { onMounted, ref } from "vue";
import { Dropdown } from "flowbite";
import type { DropdownOptions, DropdownTriggerType } from "flowbite";
import type { Placement } from "@popperjs/core";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

defineOptions({
  inheritAttrs: false
});

const props = withDefaults(
  defineProps<{
    id: string;
    toggleId: string;
    icon?: boolean;
    placement?: Placement;
    triggerType?: DropdownTriggerType;
    offsetDistance?: number;
    offsetSkidding?: number;
  }>(),
  {
    icon: true,
    placement: "bottom",
    triggerType: "click",
    offsetDistance: 0,
    offsetSkidding: 0
  }
);

const emit = defineEmits(["show", "hide"]);

const toggleIcon = ref(false);
let dropdown: Dropdown;

function closeDropdown() {
  dropdown.hide();
}

onMounted(() => {
  const targetEl = document.getElementById(props.toggleId);
  const triggerEl = document.getElementById(props.id);

  if (triggerEl === null || triggerEl === null) {
    return;
  }

  const options: DropdownOptions = {
    placement: props.placement,
    triggerType: props.triggerType,
    offsetDistance: props.offsetDistance,
    offsetSkidding: props.offsetSkidding,
    onShow: () => {
      toggleIcon.value = true;
      emit("show");
    },
    onHide: () => {
      toggleIcon.value = false;
      emit("hide");
    }
  };

  dropdown = new Dropdown(targetEl, triggerEl, options);
});
</script>

<template>
  <button
    type="button"
    class="rounded px-2 pb-1.5 pt-2 text-sm hover:bg-accent"
    v-bind="$attrs"
    :id
  >
    <slot name="text" />

    <template v-if="icon">
      <FontAwesomeIcon icon="fa-solid fa-chevron-down" v-show="!toggleIcon" />

      <FontAwesomeIcon icon="fa-solid fa-chevron-up" v-show="toggleIcon" />
    </template>
  </button>

  <div
    :id="toggleId"
    class="z-10 hidden cursor-default rounded border bg-white text-sm shadow-lg"
  >
    <slot name="content" :close="closeDropdown"></slot>
  </div>
</template>
