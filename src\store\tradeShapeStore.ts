import { ref, watch } from "vue";
import { defineStore } from "pinia";

import { NewOrder } from "@/lib/night-vision/shapes/NewOrder";
import { SelectedOrder } from "@/lib/night-vision/shapes/SelectedOrder";

import { useChartStore } from "./chartStore";
import { useCandleStickStore } from "./candleStickStore";
import { useBottomNavDrawerStore } from "./bottomNavDrawerStore";

export const useTradeShapeStore = defineStore("tradeshape", () => {
  const chartStore = useChartStore();
  const candleStickStore = useCandleStickStore();
  const bottomNavDrawerStore = useBottomNavDrawerStore();

  const newOrderShape = ref<NewOrder | null>(null);
  const selectedTradeShape = ref<SelectedOrder | null>(null);

  watch(
    () => newOrderShape.value,
    (order) => {
      if (!order) {
        // chartStore.shapeToolInstance?.resetGlobalLock()
        // chartStore.isShapeToolLocked = false
      } else {
        // chartStore.shapeToolInstance?.lockShapesForTrading()
        // chartStore.isShapeToolLocked = true

        if (selectedTradeShape.value) {
          removeSelectedTradeShape();
        }
      }
    },
    { immediate: true }
  );

  watch(
    () => selectedTradeShape.value,
    (order) => {
      if (!order) {
        chartStore.shapeToolInstance?.resetGlobalLock();
        chartStore.isShapeToolLocked = false;
      } else {
        if (bottomNavDrawerStore.editTradeModal) {
          chartStore.isShapeToolLocked = true;
          chartStore.shapeToolInstance?.lockShapesForTrading();
        }

        if (newOrderShape.value) {
          removeOrderShape();
        }
      }
    },
    { immediate: true }
  );

  /**
   * When an order shape is previosly removed,
   * only the lines are removed from the chart.
   * This function takes the order details from the
   * `NewOrder` instance and show it on the chart.
   */
  function showOrderShape() {
    if (newOrderShape.value) {
      const {
        orderData,
        orderExecution,
        orderType,
        priceHLine,
        takeProfitHLine,
        takeProfit1HLine,
        stopLossHLine
      } = newOrderShape.value;

      newOrderShape.value = null;

      if (chartStore.shapeToolInstance) {
        newOrderShape.value = chartStore.shapeToolInstance.addOrder(
          orderData,
          orderType,
          orderExecution
        );

        newOrderShape.value.priceHLine.ratioLabel = priceHLine.ratioLabel;
        newOrderShape.value.stopLossHLine.labelInfo = stopLossHLine.labelInfo;
        newOrderShape.value.takeProfitHLine.labelInfo =
          takeProfitHLine.labelInfo;
        newOrderShape.value.takeProfit1HLine.labelInfo =
          takeProfit1HLine.labelInfo;
      }
    }
  }

  function hideNewOrderShape() {
    if (newOrderShape.value) {
      const orderUuid = newOrderShape.value.uuid;

      chartStore.shapeToolInstance?.removeOrder(orderUuid);

      const symbol = candleStickStore.symbol;
      const interval = candleStickStore.interval;

      const currentDataName = symbol + "_" + interval;
      chartStore.shapeToolInstance?.saveShapes(currentDataName);
      chartStore.shapeToolInstance?.saveShapes();
    }
  }

  function removeOrderShape() {
    hideNewOrderShape();
    newOrderShape.value = null;
  }

  function showSelectedTradeShape() {
    if (selectedTradeShape.value) {
      const {
        orderData,
        orderExecution,
        orderType,
        priceHLine,
        takeProfitHLine,
        takeProfit1HLine,
        stopLossHLine
      } = selectedTradeShape.value;

      if (!takeProfitHLine.labelInfo || !stopLossHLine.labelInfo) {
        throw new Error("Trade labels not found");
      }

      selectedTradeShape.value = null;

      if (chartStore.shapeToolInstance) {
        selectedTradeShape.value =
          chartStore.shapeToolInstance.showSelectedOrder(
            orderData,
            orderType,
            orderExecution,
            stopLossHLine.labelInfo,
            takeProfitHLine.labelInfo,
            takeProfit1HLine.labelInfo
          );

        selectedTradeShape.value.priceHLine.ratioLabel = priceHLine.ratioLabel;
      }
    }
  }

  function hideSelectedTradeShape() {
    if (selectedTradeShape.value) {
      const orderUuid = selectedTradeShape.value.uuid;

      chartStore.shapeToolInstance?.removeOrder(orderUuid);

      const symbol = candleStickStore.symbol;
      const interval = candleStickStore.interval;

      const currentDataName = symbol + "_" + interval;
      chartStore.shapeToolInstance?.saveShapes(currentDataName);
      chartStore.shapeToolInstance?.saveShapes();
    }
  }

  function removeSelectedTradeShape() {
    hideSelectedTradeShape();
    selectedTradeShape.value = null;
  }

  return {
    newOrderShape,
    selectedTradeShape,
    hideNewOrderShape,
    removeOrderShape,
    showOrderShape,
    showSelectedTradeShape,
    hideSelectedTradeShape,
    removeSelectedTradeShape
  };
});
