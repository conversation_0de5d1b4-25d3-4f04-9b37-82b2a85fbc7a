<template>
  <div id="features" class="bg-white py-24">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
      <div class="grid gap-12 sm:grid-cols-2 lg:grid-cols-3">
        <div
          v-for="(feature, index) in features"
          :key="index"
          class="group relative flex flex-col space-y-6 rounded-2xl border border-gray-200 p-8 transition-all duration-300 hover:border-blue-500"
        >
          <div class="flex items-center gap-4">
            <div
              class="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-50 transition-colors duration-300 group-hover:bg-blue-100"
            >
              <component :is="feature.icon" class="h-6 w-6 text-blue-700" />
            </div>

            <h3
              class="text-lg font-semibold leading-8 tracking-tight text-gray-900"
            >
              {{ feature.title }}
            </h3>
          </div>

          <p class="text-base leading-7 text-gray-600">
            {{ feature.description }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { FunctionalComponent } from "vue";

import {
  BookOpen,
  Cpu,
  DollarSign,
  GraduationCap,
  LineChart,
  Users
} from "lucide-vue-next";

interface Feature {
  title: string;
  description: string;
  icon: FunctionalComponent;
}

const features: Feature[] = [
  {
    title: "ONLINE RECORDED CLASSES",
    description:
      "Our recorded online lessons provide a comprehensive introduction to the fundamentals of forex trading. Learn at your own pace with support from experienced teachers who provide personalized guidance and answer your questions.",
    icon: BookOpen
  },
  {
    title: "APPLIED LEARNING WITH VIRTUAL MENTOR",
    description:
      "Practice paper trading on our trading platform, applying the trading systems we teach. Our virtual mentor tracks your trading performance and provides automated metrics, ensuring you're supported every step of the way.",
    icon: GraduationCap
  },
  {
    title: "SIMPLE, REPEATABLE PRICE ACTION TRADING",
    description:
      "We develop easy-to-follow, price action-based intra-trading system that anyone can learn and master. Our risk management system is designed to help you minimize potential losses and maximize your chances of profitability.",
    icon: LineChart
  },
  {
    title: "PROPRIETARY TECHNOLOGY",
    description:
      "Our trading platform seamlessly integrates with MT5, a popular platform in forex trading, offering automated features that give you an edge from the start. We are continually developing new trading tools to enhance your success. Once you're ready to go live, you can transition smoothly, using our platform just like you practiced.",
    icon: Cpu
  },
  {
    title: "MENTORSHIP AND SUPPORT",
    description:
      "With over 16 years of experience in forex and system trading, our educators are here to guide you every step of the way. Our daily calls provide the latest market updates and insights, helping you navigate the markets with confidence. We are dedicated to supporting you and ensuring your success on your trading journey.",
    icon: Users
  },
  {
    title: "AFFORDABLE PRICING PLANS",
    description:
      "Get the Forex education you need without breaking the bank. Our plan is just $49.99 per month. Join our community where success is shared. We're developing a leaderboard and a copy trading system, enabling you to automatically copy trades from our successful members. Together, we all win.",
    icon: DollarSign
  }
];
</script>
