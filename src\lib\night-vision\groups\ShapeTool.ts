/* eslint-disable @typescript-eslint/no-explicit-any */
import { CrossHair } from "../shapes/CrossHair";
import { DateAndPriceRange } from "../shapes/DateAndPriceRange";
import { DateRange } from "../shapes/DateRange";
import { FibRetrace } from "../shapes/FibRetrace";
import { HLine } from "../shapes/HLine";
import { Line } from "../shapes/LineTool";
import {
  ILabelInfo,
  NewOrder,
  OrderData,
  OrderExecution,
  OrderType
} from "../shapes/NewOrder";
import { PriceHLine } from "../shapes/PriceHLine";
import { PriceRange } from "../shapes/PriceRange";
import { Rectangle } from "../shapes/Rectangle";
import { SelectedOrder } from "../shapes/SelectedOrder";
import { TextShape } from "../shapes/Text";
import { VLine } from "../shapes/VLine";
import { BaseShapeInterface } from "../shapes/base/shape.types";
import { Point } from "../shapes/shapes/Point";
import { NavyDefaults } from "./NavyDefaults";

function changeCursor(cursor: string) {
  document.body.style.cursor = cursor;
}

let time = Date.now();

type CursorType =
  | "auto"
  | "default"
  | "none"
  | "context-menu"
  | "help"
  | "pointer"
  | "progress"
  | "wait"
  | "cell"
  | "crosshair"
  | "text"
  | "vertical-text"
  | "alias"
  | "copy"
  | "move"
  | "no-drop"
  | "not-allowed"
  | "grab"
  | "grabbing"
  | "all-scroll"
  | "col-resize"
  | "row-resize"
  | "n-resize"
  | "e-resize"
  | "s-resize"
  | "w-resize"
  | "ne-resize"
  | "nw-resize"
  | "se-resize"
  | "sw-resize"
  | "ew-resize"
  | "ns-resize"
  | "nesw-resize"
  | "nwse-resize"
  | "zoom-in"
  | "zoom-out";

export const shapes = {
  "trend-line": Line,
  "horizontal-line": HLine,
  "vertical-line": VLine,
  "price-range": PriceRange,
  "date-range": DateRange,
  "date-and-price-range": DateAndPriceRange,
  box: Rectangle,
  crosshair: CrossHair,
  "fib-level": FibRetrace,
  text: TextShape,
  "no-shape": undefined
};

export type AvailableShapes = keyof typeof shapes;

type AvailableShapesConstructors = (typeof shapes)[keyof typeof shapes];

enum NewShapeState {
  WAITING_TO_DRAW,
  DRAWING
}

/*
  TODOS:
  1. Send price coordinates value upto 4 precision points.
*/

const shapeEvents = {
  SHAPE_TOOL_CREATED: "shape-tool-created",
  SHAPE_TOOL_READY: "shape-tool-ready",
  SHAPE_SELECT: "select-shape",
  SHAPE_DELETE: "shape-delete",
  SHAPE_DRAW_WAIT: "shape-draw-wait",
  SHAPE_DRAW_START: "shape-draw-start",
  SHAPE_DRAW_COMPLETE: "shape-draw-complete",
  SHAPE_DRAW_TRUNCATE: "shape-draw-truncate",
  SHAPE_PROPERTY_CHANGE: "shape-property-changed",
  UPDATE_COORDINATES: "point-update-coordinates",
  SCROLL_LOCK: "scroll-lock",
  SHAPE_LOCKED: "shape-locked",
  DOUBLE_CLICK: "double-click",
  CHART_DISPLAY_BARS_UPDATE: "chart-display-bars-update",
  UPDATE_CHART_LAYOUT: "update-layout",
  UPDATE_PRICE_VALUE: "update-price-value",
  UPDATE_SL_VALUE: "update-sl-value",
  UPDATE_TP_VALUE: "update-tp-value",
  UPDATE_TP1_VALUE: "update-tp1-value",
  UPDATE_EDIT_ORDER: "update-edit-order",
  SL_DRAG_EDIT: "sl-drag-edit",
  TP_DRAG_EDIT: "tp-drag-edit",
  TP1_DRAG_EDIT: "tp1-drag-edit",
  PRICE_DRAG_EDIT: "price-drag-edit"
} as const;

type ShapeEventsKeys = keyof typeof shapeEvents;
type ShapeEvents = (typeof shapeEvents)[ShapeEventsKeys];

interface ShapeToolExternal {
  /**
   * Delete shape from chart.
   */
  deleteShape(uuid: string): void;

  /**
   * Delete all shapes from chart.
   */
  deleteAllShapes(): void;

  /**
   * Adds new shape to chart.
   */
  addShape(
    shape: AvailableShapes | string,
    shapeData: { [x: string]: any }
  ): void;

  /**
   * Add shapes to NightVision canvas (Use only if you have exported shapes from pipclimber).
   * @param {AvailableShapes | string} shape
   * @param shapeData
   */
  addShapesFromJSON(dataName: string): void;

  /**
   * Start drawing mode for a new shape.
   */
  makeNewShape(shape: AvailableShapes): void;

  /**
   * Toggles the lock on a shape with given UUID.
   */
  toggleShapeLock(shapeUUID: string): boolean;

  /**
   * Add a listener to the shape canvas.
   * @param component
   * @param event
   * @param fxn
   */
  addListener(
    component: string,
    event: ShapeEvents,
    fxn: (...data: any) => void
  ): void;

  /**
   * Remove a listener from the shape canvas.
   * @param component
   * @param event
   */
  removeListener(component: string, event: ShapeEvents): void;
  /**
   * External control to save shapes to localStorage
   * @param name What name should be used to save in localstorage. For example: 'EUR_USD_15M'
   */
  saveShapes(name: string): void;
}

interface Time2X {
  [key: string]: number;
}

interface SinglePoint {
  x: number;
  y: number;
}

export class ShapeTool extends NavyDefaults implements ShapeToolExternal {
  private _shapes: Map<
    string,
    { type: string; shape: BaseShapeInterface<AvailableShapes> }
  >;
  private _newShapeType?: AvailableShapes;
  private _NewShape?: AvailableShapesConstructors;
  private _newShapeState?: NewShapeState;
  private selectedShape?: BaseShapeInterface<AvailableShapes>;
  private _newSelected = false;
  private _lockedShapes: string[] = [];
  private _globalLock = false;
  private _time2x: Time2X = {};
  // TODO: research if this getter is needed
  // get lockedShapes(): string[] {
  //   return [...this._lockedShapes]
  // }
  private updateChart() {
    this.dispatchEvent("update-layout");
  }
  constructor(env: any) {
    super(env);
    this.updateCurrentTime();
    this._shapes = new Map<
      string,
      {
        type: string;
        shape: BaseShapeInterface<AvailableShapes>;
      }
    >();
    this.initializeEventListeners();
    this.initializeTime2x();
    this.dispatchEvent("shape-tool-ready", this);
  }
  private currentDataName: string = "";
  setCurrentDataName(name: string) {
    this.currentDataName = name;
  }

  initializeEventListeners() {
    this.$events.on(
      "shapetool:shape-draw-start",
      this.handleShapeDrawStart.bind(this)
    );
    this.$events.on(
      "shapetool:shape-draw-complete",
      this.handleShapeDrawComplete.bind(this)
    );
    this.saveShapes(this.currentDataName);
    this.$events.on(
      "shapetool:update-layout",
      this.updateCurrentTime.bind(this)
    );
    this.$events.on("shapetool:shape-delete", () =>
      this.saveShapes(this.currentDataName)
    );
    this.$events.on(
      "shapetool:$range-update",
      this.updateCurrentTime.bind(this)
    );

    this.$events.on("shapetool:default-property-updated", (value: any) => {
      let defaultShapeProperties = {};

      const defaultShapePropertiesJson = localStorage.getItem(
        "default-shape-properties"
      );

      if (defaultShapePropertiesJson) {
        defaultShapeProperties = JSON.parse(defaultShapePropertiesJson);
      }

      const newShapeProperties = {
        [value.type]: {
          ...value
        }
      };

      defaultShapeProperties = {
        ...defaultShapeProperties,
        ...newShapeProperties
      };

      localStorage.setItem(
        "default-shape-properties",
        JSON.stringify(defaultShapeProperties)
      );
    });

    this.$events.on("shapetool:price-line-dragged", (value: number) => {
      this.$core.hub.events.emit("update-price-value", value);
    });

    this.$events.on("shapetool:sl-line-dragged", (value: number) => {
      this.$core.hub.events.emit("update-sl-value", value);
    });

    this.$events.on("shapetool:tp-line-dragged", (value: number) => {
      this.$core.hub.events.emit("update-tp-value", value);
    });

    this.$events.on("shapetool:tp1-line-dragged", (value: number) => {
      this.$core.hub.events.emit("update-tp1-value", value);
    });

    this.$events.on("shapetool:order-data-updated", (value: number) => {
      this.$core.hub.events.emit("update-edit-order", value);
    });

    this.$events.on("shapetool:price-drag-end", (value: number) => {
      this.$core.hub.events.emit("price-drag-edit", value);
    });

    this.$events.on("shapetool:sl-drag-end", (value: number) => {
      this.$core.hub.events.emit("sl-drag-edit", value);
    });

    this.$events.on("shapetool:tp-drag-end", (value: number) => {
      this.$core.hub.events.emit("tp-drag-edit", value);
    });

    this.$events.on("shapetool:tp1-drag-end", (value: number) => {
      this.$core.hub.events.emit("tp1-drag-edit", value);
    });
  }

  initializeTime2x() {
    const { data: candleData }: { data: number[][] } =
      this.$core.hub.data.panes[0].overlays[0];

    candleData.forEach((candle, index) => {
      this._time2x[candle[0]] = index + 1;
    });
  }

  handleShapeDrawStart({
    shape,
    points
  }: {
    shape: BaseShapeInterface<AvailableShapes>;
    points: { p: Point; dir: null }[];
  }) {
    this.selectedShape = shape;
    this._newShapeState = NewShapeState.DRAWING;
    points.forEach(({ p, dir }) => p.startDragging(dir ?? undefined));
    this.dispatchEvent("scroll-lock", true);
  }

  handleShapeDrawComplete(shape: BaseShapeInterface<AvailableShapes>) {
    this._selectShape(shape);
    this._NewShape = undefined;
    this._newShapeType = undefined;
    this._newShapeState = undefined;
    this._newSelected = false; // this should only be true when dragging

    this.updateChart();
    this.dispatchEvent("scroll-lock", false);
    this.saveShapes(this.currentDataName);
  }
  // TODO: this is not used
  _unselectShape() {
    for (const [_, shape] of this._shapes) {
      shape.shape.selected = false;
    }
    this.dispatchEvent("select-shape"); // Emit event to notify about the unselection
    this.updateChart(); // Redraw
    // if (this.selectedShape) {
    //   this.selectedShape.selected = false
    //   this.selectedShape = undefined
    //   this.dispatchEvent("select-shape") // Emit event to notify about the unselection
    // }
  }

  _selectShape(shape: BaseShapeInterface<AvailableShapes>) {
    if (!shape) return false;
    if (this.selectedShape?.uuid.startsWith("_")) return false;
    this._unselectShape();
    // changeCursor("none")

    this.dispatchEvent("select-shape", shape.getOptions());
    // REMOVE: Debug
    this._newSelected = true;
    shape.selected = true;
    this.selectedShape = shape;
    this.updateChart(); // Redraw

    return true;
  }

  deleteShape(uuid: string): void {
    const shape = this._shapes.get(uuid);
    if (shape && shape.shape.destroy) {
      shape.shape.destroy();
    }

    this._shapes.delete(uuid);
    // this.saveShapes(this.currentDataName)
    this.dispatchEvent("select-shape", undefined);
    this.dispatchEvent("shape-delete");
    this.updateChart();
  }

  deleteAllShapes(): void {
    this._shapes.clear();
    this.dispatchEvent("shape-delete");
    this.updateChart();
  }

  // TODO : toggleVisibilityForAllShapes(): void{}
  private _createUUid() {
    return Math.random().toString().slice(2).replace(/^0+/, "");
  }

  appendShape(type: string, shape: BaseShapeInterface<AvailableShapes>) {
    this._shapes.set(shape.uuid, { type: type, shape: shape });
    if (!shape.name.length) shape.setName(type);
    shape.onSelect = this._selectShape.bind(this);
  }

  findInBetweenIndexForTime(time: number) {
    const { data } = this.$core.hub.data.panes[0].overlays[0];

    const dataTimeArray: number[] = data.map(
      (candleData: number[]) => candleData[0]
    );

    for (let i = 0; i < dataTimeArray.length; i++) {
      if (time > dataTimeArray[i - 1] && time < dataTimeArray[i]) {
        return i;
      }
    }

    throw new Error("Can't find correct index");
  }

  mapPointsTime2X(points: SinglePoint[], time: number[]) {
    const mappedPoints: SinglePoint[] = [];

    const { data } = this.$core.hub.data.panes[0].overlays[0];
    const latestDataTime = data[data.length - 1][0];

    points.forEach((point: any, index: any) => {
      let mappedPoint: number;

      if (this._time2x[`${time[index]}`]) {
        mappedPoint = this._time2x[`${time[index]}`] - 1;
      } else if (time[index] > latestDataTime) {
        mappedPoint = this.mapFutureTime2x(time[index]);
      } else {
        mappedPoint = this.findInBetweenIndexForTime(time[index]);
      }

      mappedPoints.push({
        x: mappedPoint,
        y: point.y
      });
    });

    return mappedPoints;
  }

  mapFutureTime2x(time: number) {
    const { data } = this.$core.hub.data.panes[0].overlays[0];

    const dataLength = data.length;

    const timeDiff = time - data[dataLength - 1][0];

    const candleDiff = timeDiff / this.$core.props.timeFrame;

    const x = dataLength - 1 + candleDiff;

    return x;
  }

  addShape(shape: string, shapeData: { [x: string]: any }) {
    const ShapeCreator = shapes[shape as AvailableShapes];
    if (!ShapeCreator) throw new Error(`Shape ${shape} doesn't exist`);
    const uuid = this._createUUid();
    try {
      const { points, time, showDetails, ...properties } = shapeData;

      const mappedPoints = this.mapPointsTime2X(points, time);

      const newShape = new ShapeCreator(this.$core, uuid, mappedPoints, false);
      Object.entries(properties).forEach(([key, value]) => {
        // @ts-expect-error
        newShape[key] = value;
      });
      this.appendShape(shape, newShape);
    } catch (e) {
      throw new Error(`Shape ${shape} creation failed`);
    }
  }

  addShapesFromJSON(dataName: string) {
    try {
      this.setCurrentDataName(dataName);
      const json = localStorage.getItem(dataName);

      if (!json) {
        return;
      }

      const data = JSON.parse(json);

      // There is no JSON; no shapes
      if (!Array.isArray(data)) {
        return;
      }

      data.forEach((shapeData, index) => {
        try {
          if (shapeData.type === "no-shape") {
            return;
          }
          this.addShape(shapeData.type, shapeData);
        } catch (e: any) {
          // Skip processing this invalid shape and continue with the next one
          console.error(`Error adding shape at index ${index}: ${e.message}`);
        }
      });
    } catch (e: any) {
      console.error(`${e.message}`);
    }
  }
  makeNewShape(shape: AvailableShapes): void {
    const ShapeCreator = shapes[shape];
    if (this._newShapeState === NewShapeState.DRAWING && this.selectedShape) {
      this.deleteShape(this.selectedShape.uuid);
      this.dispatchEvent("shape-draw-truncate");
    }
    this.dispatchEvent("shape-draw-wait");

    if (!ShapeCreator) {
      this._NewShape = undefined;
      this._newShapeType = undefined;
      this._newShapeState = undefined;
      return;
    }
    this._NewShape = ShapeCreator;
    this._newShapeType = shape;
    this._newShapeState = NewShapeState.WAITING_TO_DRAW;
  }

  updateShowShapeDetails(shapeUUID: string, _name?: string) {
    const name = _name ?? this.currentDataName;
    const shapes: any[] = [];
    const shapeToUpdate = this._shapes.get(shapeUUID);
    shapeToUpdate?.shape.toggleDetails();
    this.saveShapes(name);

    this._shapes.forEach((shape) => {
      const json = shape.shape?.toJSON?.();
      if (json) shapes.push(json);
    });
    if (name) localStorage.setItem(name, JSON.stringify(shapes));
    this.selectedShape = undefined;
  }

  updateShapeCoordinates() {
    this.initializeTime2x();

    this._shapes.forEach(({ shape }) => {
      if (
        shape.name === "PRICE" ||
        shape.name === "SL" ||
        shape.name === "TP" ||
        shape.name === "TP1"
      ) {
        return;
      }

      const json = localStorage.getItem(this.currentDataName);
      if (!json) return;
      const data = JSON.parse(json);

      const shapeJsonData = data.find(
        (shapeData: any) => shapeData.uuid === shape.uuid
      );

      if (!shapeJsonData || !shapeJsonData.time) {
        return;
      }

      const jsonTime = shapeJsonData.time;

      shape.points.forEach((point, index) => {
        let mappedX;

        if (this._time2x[`${jsonTime[index]}`]) {
          mappedX = this._time2x[`${jsonTime[index]}`] - 1;
        } else {
          mappedX = this.mapFutureTime2x(jsonTime[index]);
        }

        point.x = mappedX;
      });
    });

    this.updateChart();
  }

  toggleShapeLock(shapeUUID: string): boolean {
    const locked = this._lockedShapes.findIndex((s) => s === shapeUUID);
    // Remove the shape from the locked list
    // TODO: Add functionality for toggleTo
    let lock;
    if (locked !== -1) {
      this._lockedShapes.splice(locked, 1);
      if (this.selectedShape) this.selectedShape.locked = false;
      lock = false;
    } else {
      this._lockedShapes.push(shapeUUID);
      if (this.selectedShape) this.selectedShape.locked = true;
      lock = true;
    }
    // this.dispatchEvent("shape-locked", lock)
    return lock;
  }

  propagate(event: string, data?: any) {
    const eventD = event as keyof BaseShapeInterface<AvailableShapes>;
    const passThroughEvents = [
      "mousedown",
      "mouseup",
      "mouseover",
      "draw",
      "drawBotbar",
      "drawSidebar"
    ];
    if (event)
      this._shapes.forEach((value, _key) => {
        if (passThroughEvents.indexOf(event) === -1) {
          if (
            this._lockedShapes.includes(_key) ||
            (this._globalLock &&
              !_key.includes("-price") &&
              !_key.includes("-sl") &&
              !_key.includes("-tp") &&
              !_key.includes("-no") &&
              !_key.includes("-so"))
          )
            return;
        }
        if (value && value.shape[eventD]) {
          if (Array.isArray(data)) {
            // @ts-expect-error
            value.shape[eventD](...data);
          } else {
            // @ts-expect-error
            value.shape[eventD](data);
          }
        }
      });
  }

  getCursor(): CursorType {
    if (this._NewShape) return "pointer";

    for (const [_, { shape }] of this._shapes) {
      for (const point of shape.points) {
        if ((shape.selected && point.dragging) || point.hovered) return "auto";
      }

      if (shape.selected && shape.dragging) return "grabbing";

      if (shape.hovered) {
        if (
          shape.name === "PRICE" ||
          shape.name === "SL" ||
          shape.name === "TP" ||
          shape.name === "TP1"
        ) {
          const orderShape = shape as PriceHLine;
          if (!orderShape.draggable) {
            return "crosshair";
          }
          return "ns-resize";
        }

        return "pointer";
      }
    }

    return "crosshair";
  }

  updateCursor() {
    const cursor = this.getCursor();
    changeCursor(cursor);
  }
  keydown(...data: any) {
    if (!(data[0] instanceof KeyboardEvent)) return;

    if (data[0].code === "Escape") {
      this.cancelDrawing();
    }

    if ((data[0].target as HTMLElement)?.id !== "chart-container") return;
    if (data[0].code === "Delete") {
      if (this.selectedShape && !this.selectedShape.isEditing)
        this.deleteShape(this.selectedShape.uuid);
    }
    this.propagate("keydown", data);
  }
  mousemove(...data: any) {
    this.propagate("mousemove", data);
    this.propagate("mouseover", data);
    this.updateCursor();
  }
  mouseout(...data: any) {
    changeCursor("auto");
    this.propagate("mouseout", data);
  }
  mousedown(...data: any) {
    if (this._newShapeState === NewShapeState.WAITING_TO_DRAW) {
      this._newShapeState = NewShapeState.DRAWING;
      this.dispatchEvent("scroll-lock", true);
      const uuid = this._createUUid();
      if (this._NewShape && this._newShapeType) {
        const newShape = new this._NewShape(this.$core, uuid, []);

        const defaultShapePropertiesJson = localStorage.getItem(
          "default-shape-properties"
        );

        let defaultShapeProperties;
        if (defaultShapePropertiesJson) {
          defaultShapeProperties = JSON.parse(defaultShapePropertiesJson);

          if (defaultShapeProperties[newShape.type]) {
            newShape.properties =
              defaultShapeProperties[newShape.type].properties;
          }
        }

        this._selectShape(newShape);
        this.appendShape(this._newShapeType, newShape);
        this.saveShapes(this.currentDataName);
        this.updateChart();
      }
    } else {
      if (this._newShapeState !== NewShapeState.DRAWING)
        this.selectedShape = undefined;
      this.propagate("mousedown", data);
    }
    // Handle double click on chart
    if (Date.now() - time < 500) {
      this.dispatchEvent("double-click");
    }
    if (this._newShapeState !== NewShapeState.DRAWING) time = Date.now();
  }

  currentTime: { hi: number; lo: number; count: number; indexBased: boolean } =
    {
      hi: 0,
      lo: 0,
      count: 0,
      indexBased: false
    };
  updateCurrentTime() {
    const w_candle = this.$core.layout.indexBased
      ? 1
      : this.$core.scan.main[1][0] - this.$core.scan.main[0][0];

    const lo = this.$core.layout.x2ti(0);
    const hi = this.$core.layout.x2ti(this.$core.layout.width);
    const data = {
      lo,
      hi,
      count: (hi - lo) / w_candle,
      indexBased: this.$core.layout.indexBased
    };
    if (data.lo === this.currentTime.lo && data.hi === this.currentTime.hi)
      return;
    this.dispatchEvent("chart-display-bars-update", data);
    this.currentTime = data;
  }

  mouseup(...data: any) {
    // get min max x val
    if (this._newSelected) this._newSelected = false;
    else this._unselectShape();

    const mouseEvent: MouseEvent = data[0];

    if (mouseEvent.button === 2) {
      this.cancelDrawing();
    }

    // If drawing a new shape, check if the shape is valid
    if (this._newShapeState === NewShapeState.DRAWING) {
      const validShape = (
        this.selectedShape as BaseShapeInterface<AvailableShapes>
      ).isValid;
      if (validShape) {
        this.dispatchEvent("shape-draw-complete", this.selectedShape); // Emit event indicating shape drawing is complete
      } else {
        if (this.selectedShape) {
          if (this.selectedShape.points[0].dragging) {
            this.cancelDrawing();
          }
        }
        return;
      }
    }
    // Propagate the mouseup event to all shapes
    this.propagate("mouseup", data);
    this.updateCursor();
  }
  private dispatchEvent(event: ShapeEvents, eventData?: any): void {
    this.$events.emit(event, eventData);
  }

  addListener(comp: string, event: ShapeEvents, fxn: (...data: any) => void) {
    this.$events.on(comp + ":" + event, fxn);
  }
  removeListener(component: string, event: ShapeEvents): void {
    this.$events.off(component, event);
  }

  cancelDrawing() {
    if (this._newShapeState === NewShapeState.DRAWING && this.selectedShape) {
      this.deleteShape(this.selectedShape.uuid);
      this.dispatchEvent("shape-draw-truncate");
    }
    this._unselectShape();
    if (this._NewShape) this.dispatchEvent("shape-draw-truncate");
    this._NewShape = undefined;
    this._newShapeType = undefined;
    this._newShapeState = undefined;
    this.updateCursor();
    this.dispatchEvent("scroll-lock", false);
  }

  saveShapes(_name?: string) {
    const name = _name ?? this.currentDataName;
    const shapes: any[] = [];

    if (this._shapes.size === 0) {
      localStorage.removeItem(name);
      return;
    }

    this._shapes.forEach((shape) => {
      const json = shape.shape?.toJSON?.();
      if (json) shapes.push(json);
    });
    if (name) localStorage.setItem(name, JSON.stringify(shapes));
  }

  lockShapesForTrading() {
    this._globalLock = true;
  }

  resetGlobalLock() {
    this._globalLock = false;
  }

  addOrder(
    orderData: OrderData,
    orderType: OrderType,
    orderExecution: OrderExecution
  ): NewOrder {
    const uuid = this._createUUid() + "-no";
    const newOrder = new NewOrder(this.$core, uuid, {
      orderData,
      orderType,
      orderExecution
    });

    this._shapes.set(uuid, { type: "order", shape: newOrder });

    const { priceHLine, stopLossHLine, takeProfitHLine, takeProfit1HLine } =
      newOrder;
    this._shapes.set(priceHLine.uuid, {
      type: "price-hline",
      shape: priceHLine
    });
    this._shapes.set(stopLossHLine.uuid, {
      type: "stoploss-hline",
      shape: stopLossHLine
    });
    this._shapes.set(takeProfitHLine.uuid, {
      type: "takeprofit-hline",
      shape: takeProfitHLine
    });
    this._shapes.set(takeProfit1HLine.uuid, {
      type: "takeprofit1-hline",
      shape: takeProfit1HLine
    });

    this.updateChart();
    return newOrder;
  }

  removeOrder(uuid: string) {
    this._shapes.delete(uuid);
    this.deleteShape(`${uuid}-price`);
    this.deleteShape(`${uuid}-sl`);
    this.deleteShape(`${uuid}-tp`);
    this.deleteShape(`${uuid}-tp1`);

    this.updateChart();
  }

  showSelectedOrder(
    orderData: OrderData,
    orderType: OrderType,
    orderExecution: OrderExecution,
    slLabel: ILabelInfo,
    tpLabel: ILabelInfo,
    tp1Label?: ILabelInfo
  ): SelectedOrder {
    const uuid = this._createUUid() + "-so";
    const selectedOrder = new SelectedOrder(this.$core, uuid, {
      orderData,
      orderType,
      orderExecution,
      slLabel,
      tpLabel,
      tp1Label
    });
    this._shapes.set(uuid, { type: "order", shape: selectedOrder });

    const { priceHLine, stopLossHLine, takeProfitHLine, takeProfit1HLine } =
      selectedOrder;
    this._shapes.set(priceHLine.uuid, {
      type: "price-hline",
      shape: priceHLine
    });
    this._shapes.set(stopLossHLine.uuid, {
      type: "stoploss-hline",
      shape: stopLossHLine
    });
    this._shapes.set(takeProfitHLine.uuid, {
      type: "takeprofit-hline",
      shape: takeProfitHLine
    });
    this._shapes.set(takeProfit1HLine.uuid, {
      type: "takeprofit1-hline",
      shape: takeProfit1HLine
    });

    this.updateChart();
    return selectedOrder;
  }
}
