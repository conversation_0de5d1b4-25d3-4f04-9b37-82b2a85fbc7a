<script setup lang="ts">
import { onMounted, ref } from "vue";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { useChartStore } from "@/store/chartStore";

import Dropdown from "@/components/Dropdown.vue";
import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"text"> | undefined =
  chartStore.selectedShape;

const fontSizeDropdown = ref(false);
const fontSizes = ref([10, 12, 14, 16, 18, 20]);
const selectedFontSize = ref(2);

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set font size for text shape");
  }

  selectedFontSize.value = selectedShape.properties.textProperties.font_size;
});

function handleFontSize(fontSize: number) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to change font size for text shape");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to change font size for text shape");
  }

  selectedFontSize.value = fontSize;

  selectedShape.setProperty("textProperties", "font_size", fontSize);

  chartStore.chart.update();
}
</script>

<template>
  <Dropdown
    id="chart-modal-text-font-size-dropdown"
    toggle-id="chart-modal-text-font-size-toggle-dropdown"
    class="border"
    :class="{ 'border-info bg-accent': fontSizeDropdown }"
    :icon="false"
    @show="fontSizeDropdown = true"
    @hide="fontSizeDropdown = false"
  >
    <template #text>{{ selectedFontSize }}px</template>

    <template #content="{ close }">
      <div class="my-1 w-16 cursor-pointer">
        <div
          class="flex px-3 pb-1.5 pt-2 hover:bg-accent"
          :class="{
            'bg-selected text-white hover:bg-selected':
              size === selectedFontSize
          }"
          v-for="size in fontSizes"
          @click="(close(), handleFontSize(size))"
          :key="size + 'px'"
        >
          {{ size }}px
        </div>
      </div>
    </template>
  </Dropdown>
</template>
