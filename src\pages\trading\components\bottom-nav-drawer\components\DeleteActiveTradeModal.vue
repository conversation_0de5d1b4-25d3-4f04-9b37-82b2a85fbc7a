<script setup lang="ts">
import { ref } from "vue";
import { number, object } from "yup";

import { countDecimals } from "@/helpers/numberUtils";
import { getClientValidationErrors } from "@/helpers/getErrors";
import { eaSocket } from "@/socketio";
import { useUserStore } from "@/store/userStore";
import { useAppbarStore } from "@/store/appbarStore";
import { ETradeOperationType } from "@/types/enums";

import NavItem from "@/components/NavItem.vue";
import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import RadioButton from "@/components/RadioButton.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

import CloseSVG from "@/assets/svg/close.svg";

const props = defineProps<{
  symbol: string;
  tp: number;
  ticket: number;
  volume: number;
  open_price: number;
}>();

const emit = defineEmits(["closeModal"]);

enum ECloseTrade {
  "FULL_CLOSE" = "full_close",
  "PARTIAL_CLOSE" = "partial_close",
  "BREAKEVEN_CLOSE" = "breakeven_close"
}

const userStore = useUserStore();
const appbarStore = useAppbarStore();

const selectedCloseTrade = ref("");
const activeTrade = ref({
  partialVolumePercent: 0
});
const validationErrors = ref<{ partialVolumePercent: string } | null>(null);

const validationSchema = object({
  partialVolumePercent: number()
    .max(100, "Percentage must be less than 100.")
    .min(0.1, "Percentage must be greater than 0.")
    .typeError("Percentage must be a number.")
});

function closeModal() {
  appbarStore.toggleModal("deleteTradeModal", false);

  emit("closeModal");
}

function handleCloseTrade() {
  if (selectedCloseTrade.value === ECloseTrade.FULL_CLOSE) {
    closeFullTrade();
  } else if (selectedCloseTrade.value === ECloseTrade.PARTIAL_CLOSE) {
    closePartialTrade();
  } else {
    closeBreakevenTrade();
  }
}

function closeFullTrade() {
  const data = {
    action: ETradeOperationType.TRADE_ACTION_CLOSE_BY,
    ticket: props.ticket,
    volume: props.volume
  };

  eaSocket?.emit(`trade_data_${userStore.eaAccessToken}`, data);

  closeModal();
}

async function closePartialTrade() {
  try {
    validationErrors.value = null;

    await validationSchema.validate(activeTrade.value, {
      abortEarly: false
    });

    const data = {
      action: ETradeOperationType.TRADE_ACTION_CUSTOM_CLOSE_BY_VOLUME,
      ticket: props.ticket,
      volume: props.volume
    };

    const volume =
      (activeTrade.value.partialVolumePercent / 100) * props.volume;

    const volumeDecimalCount = countDecimals(props.volume);

    data.volume = parseFloat(volume.toFixed(volumeDecimalCount));

    eaSocket?.emit(`trade_data_${userStore.eaAccessToken}`, data);

    closeModal();
  } catch (e) {
    validationErrors.value = getClientValidationErrors(e);
  }
}

function closeBreakevenTrade() {
  const { symbol, tp, ticket, open_price } = props;

  eaSocket?.emit(`trade_data_${userStore.eaAccessToken}`, {
    action: ETradeOperationType.TRADE_ACTION_SLTP,
    symbol,
    sl: open_price,
    tp,
    ticket
  });

  closeModal();
}
</script>

<template>
  <div
    class="absolute left-1/2 top-1/2 z-10 w-64 -translate-x-1/2 -translate-y-1/2 rounded-md border bg-white text-sm shadow-lg"
  >
    <div class="relative flex justify-end border-b py-0.5 pr-1.5">
      <div
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-base font-bold"
      >
        Close Trade
      </div>

      <NavItem @click="closeModal">
        <CloseSVG />
      </NavItem>
    </div>

    <div class="mt-2 flex items-center gap-x-3 px-3">
      <RadioButton
        id="full_close"
        value="full_close"
        v-model="selectedCloseTrade"
      />

      <InputLabel for="full_close" class="!mb-0">Full Close</InputLabel>
    </div>

    <div class="mt-1 flex items-center gap-x-3 px-3">
      <RadioButton
        id="partial_close"
        value="partial_close"
        v-model="selectedCloseTrade"
        @change="activeTrade.partialVolumePercent = 50"
      />

      <InputLabel for="partial_close" class="!mb-0">Partial Close</InputLabel>
    </div>

    <div class="mt-[5px] flex items-center gap-x-3 px-3">
      <RadioButton
        id="breakeven_close"
        value="breakeven_close"
        v-model="selectedCloseTrade"
      />

      <InputLabel for="breakeven_close" class="!mb-0">Breakeven</InputLabel>
    </div>

    <div
      class="mx-3 mt-2 flex items-center rounded-lg border pr-4 focus-within:border-blue-500 focus-within:ring-1 focus-within:ring-blue-500"
      :class="{
        '!border-danger focus-within:ring-red-500':
          validationErrors?.partialVolumePercent
      }"
      v-if="selectedCloseTrade === ECloseTrade.PARTIAL_CLOSE"
    >
      <InputText
        min="0"
        max="100"
        type="number"
        class="border-0 focus:ring-0"
        placeholder="Enter Percentage"
        v-model="activeTrade.partialVolumePercent"
      />

      <span>%</span>
    </div>

    <div class="mt-1 px-4 text-xs text-danger" v-if="validationErrors">
      {{ validationErrors.partialVolumePercent }}
    </div>

    <div class="p-3">
      <PrimaryButton class="w-full" @click="handleCloseTrade">
        Close Trade
      </PrimaryButton>
    </div>
  </div>
</template>
