<script setup lang="ts">
import { onMounted, ref } from "vue";

import { axios } from "@/api";
import { SubscriptionPlan } from "@/types";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const subscriptionPlans = ref<SubscriptionPlan[]>([]);

onMounted(async () => {
  try {
    const resp = await axios.get("/payments/subscription-plan", {
      params: {
        trial: 0
      }
    });

    subscriptionPlans.value = resp.data.data;
  } catch (e) {
    console.error(e);
  }
});
</script>

<template>
  <section id="pricing" class="bg-accent py-16">
    <div class="container mx-auto px-5">
      <h2 class="text-center text-4xl font-semibold">
        <span class="border-b-4 border-primary pb-4">Pricing Plan</span>
      </h2>

      <p class="mt-16 text-center">Choose the plan perfect for you.</p>

      <div
        class="mx-auto mt-10 grid grid-cols-12 gap-y-10 md:gap-x-10 xl:w-3/4 2xl:w-2/3"
      >
        <div
          class="col-span-12 flex flex-col rounded-xl bg-white px-5 py-7 shadow-lg sm:px-10 md:col-span-6"
          :class="{ 'bg-blue-200': idx === 1, 'bg-blue-300': idx === 2 }"
          v-for="(plan, idx) in subscriptionPlans"
        >
          <h4 class="text-center text-xl font-semibold">
            {{ plan.service_name }}
          </h4>

          <h2 class="mt-4 text-center text-2xl font-semibold lg:text-4xl">
            ${{ plan.charge }}/{{ plan.duration }}
          </h2>

          <ul class="mt-5 flex grow flex-col gap-y-2">
            <li
              class="flex items-center gap-x-5"
              v-for="feature in plan.features"
            >
              <FontAwesomeIcon icon="fa-solid fa-check" />
              {{ feature }}
            </li>
          </ul>

          <router-link
            :to="{ name: 'register', query: { subscription_plan: plan.name } }"
            :class="{
              '!bg-primary !text-white hover:!bg-secondary': idx === 1
            }"
            class="mt-10 block w-full rounded-lg border border-primary bg-white px-5 py-3 text-center text-primary hover:bg-primary hover:!text-white"
          >
            Start Trial
          </router-link>
        </div>
      </div>

      <p class="mx-auto mt-10 text-sm italic text-gray-600 xl:w-3/4 2xl:w-2/3">
        *Free trial offer valid for new users only. Terms Apply!
      </p>
    </div>
  </section>
</template>
