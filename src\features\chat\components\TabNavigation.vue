<script setup lang="ts">
defineProps({
  tabs: {
    type: Array,
    required: true
  },
  activeTab: {
    type: String,
    required: true
  }
});

defineEmits(["tab-change"]);
</script>

<template>
  <div>
    <!-- Tab buttons -->
    <div class="flex border-b border-gray-300">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        :class="[
          'flex-1 py-3 text-center font-semibold',
          activeTab === tab.id ? 'bg-white' : 'bg-gray-100'
        ]"
        @click="$emit('tab-change', tab.id)"
      >
        {{ tab.name }}
      </button>
    </div>
  </div>
</template>
