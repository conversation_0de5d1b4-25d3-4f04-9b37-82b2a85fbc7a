<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue";

import { toast } from "vue3-toastify";

import { handleRemoveSelectedTradeShape } from "@/chart-frontend/utils";

import { calculatePips } from "@/helpers/chartCalculation";
import { getElementWidthAndHeight } from "@/helpers/elementWidthAndHeight";

import { useAppbarStore } from "@/store/appbarStore";
import { useBottomNavDrawerStore } from "@/store/bottomNavDrawerStore";
import { useCandleStickStore } from "@/store/candleStickStore";
import { useChartStore } from "@/store/chartStore";
import { useLeftNavDrawerStore } from "@/store/leftNavDrawerStore";
import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";
import { useUserStore } from "@/store/userStore";

import Checkbox from "@/components/Checkbox.vue";
import Dropdown from "@/components/Dropdown.vue";
import InputLabel from "@/components/InputLabel.vue";
import Tooltip from "@/components/Tooltip.vue";

import { axios } from "@/api";
import { IUserSession } from "@/types";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const userStore = useUserStore();
const chartStore = useChartStore();
const appbarStore = useAppbarStore();
const candleStickStore = useCandleStickStore();
const rightNavDrawerStore = useRightNavDrawerStore();
const leftNavDrawerStore = useLeftNavDrawerStore();
const bottomNavDrawerStore = useBottomNavDrawerStore();

const showMarketWatchDetails = ref(true);
const tableHeaders = ref([
  {
    id: "symbol",
    text: "Symbol"
  },
  {
    id: "bid",
    text: "Bid"
  },
  {
    id: "ask",
    text: "Ask"
  },
  {
    id: "daily_change",
    text: "(%)"
  },
  {
    id: "spread",
    text: "Spread"
  }
]);
const sortColumn = ref("");
const sortDirection = ref("");
const marketWatchTableHeight = ref(200);
const marketWatchSettings = ref([
  {
    id: "bid_ask",
    name: "Bid & Ask"
  },
  {
    id: "daily_change",
    name: "Daily Change"
  },
  {
    id: "pips",
    name: "Pips"
  },
  {
    id: "daily_price",
    name: "Daily Price"
  },
  {
    id: "range",
    name: "Range"
  },
  {
    id: "contract_size",
    name: "Contract Size"
  }
]);
const selectedSettings = ref<string[]>([]);

onMounted(() => {
  initializeMarketWatchToggle();

  initializeMarketWatchDetailSettings();
});

watch(
  () => candleStickStore.marketWatchOnlineStatus,
  (status) => {
    candleStickStore.marketWatchLoader = status;
  }
);

watch(
  () => rightNavDrawerStore.rightNavContentAreaHeight,
  () => {
    if (!showMarketWatchDetails.value) {
      caculateMarketWatchTableHeight();
    }
  }
);

const filteredSymbolList = computed(() => {
  if (sortColumn.value === "") {
    return candleStickStore.marketWatchSymbolList;
  }

  return [...candleStickStore.marketWatchSymbolList].sort((a, b) => {
    const modifier = sortDirection.value === "asc" ? 1 : -1;

    // @ts-ignore
    let v1 = a[sortColumn.value];

    // @ts-ignore
    let v2 = b[sortColumn.value];

    if (sortColumn.value === "pip") {
      v1 = calculatePips(a.bid, a.symbol_session_open);
      v2 = calculatePips(b.bid, b.symbol_session_open);
    }

    if (v1 < v2) return -1 * modifier;

    if (v1 > v2) return 1 * modifier;

    return 0;
  });
});

function initializeMarketWatchToggle() {
  const show = candleStickStore.chartSettings.show_market_watch_detail;

  if (show) {
    marketWatchTableHeight.value = window.innerWidth > 1536 ? 300 : 200;
    return;
  }

  showMarketWatchDetails.value = false;

  caculateMarketWatchTableHeight();
}

function handleMarketWatchToggle() {
  if (showMarketWatchDetails.value) {
    hideMarketWatchDetail();
    return;
  }

  showMarketWatchDetail();
}

function showMarketWatchDetail() {
  marketWatchTableHeight.value = window.innerWidth > 1536 ? 300 : 200;

  showMarketWatchDetails.value = true;

  candleStickStore.chartSettings.show_market_watch_detail = true;

  candleStickStore.storeChartSettings({
    show_market_watch_detail: true
  });
}

function hideMarketWatchDetail() {
  showMarketWatchDetails.value = false;

  caculateMarketWatchTableHeight();

  candleStickStore.chartSettings.show_market_watch_detail = false;

  candleStickStore.storeChartSettings({
    show_market_watch_detail: false
  });
}

function caculateMarketWatchTableHeight() {
  const marketWatchHeader = getElementWidthAndHeight("market-watch-header");
  const marketWatchTableHeader = getElementWidthAndHeight(
    "market-watch-table-header"
  );

  marketWatchTableHeight.value =
    rightNavDrawerStore.rightNavContentAreaHeight -
    marketWatchHeader.height -
    marketWatchTableHeader.height;
}

function handleSorting(column: string) {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === "asc" ? "desc" : "asc";
  } else {
    sortColumn.value = column;
    sortDirection.value = "asc";
  }
}

function toggleSortIcon(column: string, direction: string) {
  return (
    `${sortColumn.value}_${sortDirection.value}` !== `${column}_${direction}`
  );
}

async function getUserEAToken() {
  try {
    candleStickStore.marketWatchLoader = true;

    const resp = await axios.get("/user");

    const userSessions: IUserSession[] = resp.data.data.sessions;

    const eaToken = userSessions.find(
      (session) => session.login_type === "ea"
    )?.access_token;

    if (eaToken) {
      userStore.eaAccessToken = eaToken;
    } else {
      userStore.eaAccessToken = null;

      candleStickStore.marketWatchLoader = false;

      toast.info(
        "Can't connect to EA right now.\nPlease verify that your EA is up and running properly.",
        {
          autoClose: 2000
        }
      );
    }
  } catch (e) {
    console.error(e);
  }
}

function openSymbolInChart(symbol: string) {
  leftNavDrawerStore.togglePlaceNewOrderModal(false);
  bottomNavDrawerStore.toggleEditTradeModal(false);
  appbarStore.toggleModal("deleteTradeModal", false);

  handleRemoveSelectedTradeShape();

  if (candleStickStore.brokerInfo) {
    chartStore.changeCurrentSymbol(symbol);
  }
}

function initializeMarketWatchDetailSettings() {
  showMarketWatchDetails.value =
    candleStickStore.chartSettings.show_market_watch_detail;

  selectedSettings.value = candleStickStore.chartSettings.market_watch_settings;
}

function saveMarketWatchDetailsSettings() {
  candleStickStore.storeChartSettings({
    market_watch_settings: selectedSettings.value
  });
}
</script>

<template>
  <div
    id="market-watch-header"
    class="flex justify-between border-b py-2 pl-3 pr-2"
  >
    <div class="flex items-center gap-x-1.5">
      Terminal Status:

      <template v-if="candleStickStore.marketWatchOnlineStatus">
        Online
        <div class="h-2 w-2 rounded-full bg-green-400"></div>
      </template>

      <template v-else>
        Offline
        <div class="h-2 w-2 rounded-full bg-red-500"></div>
      </template>
    </div>

    <div class="flex items-center gap-x-2">
      <template v-if="candleStickStore.marketWatchOnlineStatus">
        <Tooltip
          id="market-watch-detail-tooltip"
          trigger-id="market-watch-detail-trigger-tooltip"
          placement="left"
          class="text-gray-700"
          @click="handleMarketWatchToggle"
        >
          <template #trigger>
            <FontAwesomeIcon
              size="lg"
              icon="fa-regular fa-eye"
              v-show="showMarketWatchDetails"
            />

            <FontAwesomeIcon
              size="lg"
              icon="fa-regular fa-eye-slash"
              v-show="!showMarketWatchDetails"
            />
          </template>

          <template #content>Toggle Market Watch Detail</template>
        </Tooltip>

        <Tooltip
          id="market-watch-broker-info-tooltip"
          trigger-id="market-watch-broker-info-trigger-tooltip"
          placement="left"
          class="text-gray-700"
        >
          <template #trigger>
            <FontAwesomeIcon icon="fa-solid fa-circle-info" size="lg" />
          </template>

          <template #content>
            <div>
              <span class="font-normal">Account:</span>
              {{ candleStickStore.eaAccount?.mt5_id }}
            </div>

            <div>
              <span class="font-normal">Trader Server:</span>
              {{ candleStickStore.eaAccount?.trade_server }}
            </div>

            <div>
              <span class="font-normal">Broker:</span>
              {{ candleStickStore.brokerInfo?.broker }}
            </div>

            <div>
              <span class="font-normal">Currency:</span>
              {{ candleStickStore.eaAccount?.currency }}
            </div>

            <div>
              <span class="font-normal">Leverage:</span>
              {{ candleStickStore.eaAccount?.leverage }}
            </div>
          </template>
        </Tooltip>
      </template>

      <Tooltip
        id="market-watch-refresh-tooltip"
        trigger-id="market-watch-refresh-trigger-tooltip"
        placement="left"
        class="text-gray-700"
        @click="getUserEAToken"
        v-else
      >
        <template #trigger>
          <FontAwesomeIcon
            size="lg"
            icon="fa-solid fa-rotate"
            :class="{
              'animate-spin': candleStickStore.marketWatchLoader
            }"
          />
        </template>

        <template #content>Refresh Market Watch</template>
      </Tooltip>
    </div>
  </div>

  <div
    class="pt-3 text-center"
    v-if="!candleStickStore.marketWatchOnlineStatus"
  >
    EA not connected.
  </div>

  <template v-else>
    <div class="border-b-4">
      <div
        id="market-watch-table-header"
        class="grid select-none grid-cols-5 border-b pb-1.5 pl-1 pr-4 pt-2 font-semibold"
      >
        <div
          class="flex items-center gap-x-1 px-2"
          v-for="header in tableHeaders"
          :key="header.id"
          @click="handleSorting(header.id)"
        >
          {{ header.text }}

          <FontAwesomeIcon
            size="sm"
            icon="fa-solid fa-arrow-down"
            :class="{
              hidden: toggleSortIcon(header.id, 'asc')
            }"
          ></FontAwesomeIcon>

          <FontAwesomeIcon
            size="sm"
            icon="fa-solid fa-arrow-up"
            :class="{
              hidden: toggleSortIcon(header.id, 'desc')
            }"
          ></FontAwesomeIcon>
        </div>
      </div>

      <div
        id="market-watch-list"
        class="scrollbar overflow-auto text-xs"
        :style="{ height: marketWatchTableHeight + 'px' }"
      >
        <div
          class="grid grid-cols-5 border-b"
          :class="{
            'bg-blue-100': candleStickStore.marketWatchSymbol === item.symbol
          }"
          v-for="item in filteredSymbolList"
          :key="item.symbol"
        >
          <div
            class="select-none pb-1.5 pl-3 pt-2 font-semibold"
            @click="openSymbolInChart(item.symbol)"
          >
            {{ item.symbol }}
          </div>

          <div
            class="pb-1.5 pl-2.5 pt-2"
            :class="{
              'text-success': item.bid_color === 'green',
              'text-danger': item.bid_color === 'red',
              'text-gray-600': item.bid_color === 'grey'
            }"
          >
            {{ item.bid }}
          </div>

          <div
            class="pb-1.5 pl-2 pt-2"
            :class="{
              'text-success': item.ask_color === 'green',
              'text-danger': item.ask_color === 'red',
              'text-gray-600': item.ask_color === 'grey'
            }"
          >
            {{ item.ask }}
          </div>

          <div
            class="pb-1.5 pl-1.5 pt-2"
            :class="{
              'text-success': item.daily_change_color === 'green',
              'text-danger': item.daily_change_color === 'red',
              'text-gray-600': item.daily_change_color === 'grey'
            }"
          >
            {{ item.daily_change.toFixed(2) }}%
          </div>

          <div class="pb-1.5 pl-1.5 pt-2">
            {{ item.spread }}
          </div>
        </div>
      </div>
    </div>

    <div class="px-3 py-1" v-show="showMarketWatchDetails">
      <div class="flex items-center justify-between">
        <div class="text-base font-bold">
          {{ candleStickStore.marketWatchSymbol }}
        </div>

        <Dropdown
          id="market-watch-detail-settings-dropdown"
          toggle-id="market-watch-detail-settings-toggle-dropdown"
          :icon="false"
          :offset-distance="-38"
          :offset-skidding="-40"
        >
          <template #text>
            <FontAwesomeIcon icon="fa-solid fa-ellipsis" size="lg" />
          </template>

          <template #content>
            <div class="my-1">
              <div
                class="flex items-center gap-x-2.5 px-3 pb-1 pt-1.5"
                v-for="settings in marketWatchSettings"
                :key="settings.id"
              >
                <Checkbox
                  :id="settings.id"
                  :value="settings.id"
                  v-model="selectedSettings"
                  @change="saveMarketWatchDetailsSettings"
                />

                <InputLabel class="!mb-0" :for="settings.id">
                  {{ settings.name }}
                </InputLabel>
              </div>
            </div>
          </template>
        </Dropdown>
      </div>

      <div class="text-gray-600">
        Forex | {{ candleStickStore.brokerInfo?.display_name }}
      </div>

      <div class="mt-1 grid grid-cols-12">
        <div class="col-span-5">
          <div
            class="text-2xl font-semibold"
            :class="{
              'text-success': candleStickStore.askPriceColor === 'green',
              'text-danger': candleStickStore.askPriceColor === 'red',
              'text-gray-600': candleStickStore.askPriceColor === 'grey'
            }"
          >
            {{ candleStickStore.bidPrice }}
          </div>

          <div>Price</div>
        </div>

        <div
          class="col-span-7 flex items-center gap-x-2"
          v-if="selectedSettings.includes('bid_ask')"
        >
          <div
            class="rounded-md px-2 pb-0.5 pt-1"
            :class="{
              'bg-green-100 text-green-500':
                candleStickStore.bidPriceColor === 'green',
              'bg-red-100 text-danger':
                candleStickStore.bidPriceColor === 'red',
              'bg-gray-100 text-gray-600':
                candleStickStore.bidPriceColor === 'grey'
            }"
          >
            <div class="text-xs">Bid</div>

            <div>{{ candleStickStore.bidPrice }}</div>
          </div>

          <div
            class="rounded-md px-2 pb-0.5 pt-1"
            :class="{
              'bg-green-100 text-green-500':
                candleStickStore.askPriceColor === 'green',
              'bg-red-100 text-danger':
                candleStickStore.askPriceColor === 'red',
              'bg-gray-100 text-gray-600':
                candleStickStore.askPriceColor === 'grey'
            }"
          >
            <div class="text-xs">Ask</div>

            <div>{{ candleStickStore.askPrice }}</div>
          </div>
        </div>
      </div>

      <div class="mt-2 grid grid-cols-12 items-center">
        <div
          class="col-span-3"
          :class="{
            'text-success': candleStickStore.dailyChangeColor === 'green',
            'text-danger': candleStickStore.dailyChangeColor === 'red',
            'text-gray-600': candleStickStore.dailyChangeColor === 'grey'
          }"
          v-if="selectedSettings.includes('daily_change')"
        >
          {{ candleStickStore.dailyChange }}%
        </div>

        <div class="col-span-9" v-if="selectedSettings.includes('pips')">
          {{
            calculatePips(
              candleStickStore.bidPrice,
              candleStickStore.sessionOpenPrice
            )
          }}
          Pips
        </div>
      </div>

      <div class="mt-3 grid grid-cols-12">
        <div
          class="col-span-5 flex items-center"
          v-if="selectedSettings.includes('daily_price')"
        >
          <div
            class="rounded-md rounded-e-none bg-red-100 px-1.5 py-1 text-danger"
          >
            <div class="text-xs">Daily High</div>
            <div>{{ candleStickStore.bidHighPrice }}</div>
          </div>

          <div
            class="rounded-md rounded-s-none bg-green-100 px-1.5 py-1 text-green-500"
          >
            <div class="text-xs">Daily Low</div>
            <div>{{ candleStickStore.bidLowPrice }}</div>
          </div>
        </div>

        <div class="col-span-7 flex items-center gap-x-2">
          <div
            class="rounded-md bg-orange-100 px-2 py-1 text-orange-500"
            v-if="selectedSettings.includes('range')"
          >
            <div class="text-xs">Daily Range</div>
            <div>
              {{
                calculatePips(
                  candleStickStore.bidHighPrice,
                  candleStickStore.bidLowPrice
                )
              }}
              Pips
            </div>
          </div>

          <div
            class="rounded-md bg-blue-100 px-2 py-1 text-info"
            v-if="selectedSettings.includes('contract_size')"
          >
            <div class="text-xs">Contract Size</div>
            <div>{{ candleStickStore.contractSize }}</div>
          </div>
        </div>
      </div>
    </div>
  </template>
</template>
