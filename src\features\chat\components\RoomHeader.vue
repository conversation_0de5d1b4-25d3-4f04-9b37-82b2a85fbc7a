<script setup lang="ts">
import { PropType } from "vue";

import { Expand, Search } from "lucide-vue-next";
import { ref } from "vue";
import { Room } from "@/types/chat";
const inputActive=ref(false);
defineProps({
  room: {
    type: Object as PropType<Room>,
    required: true
  },
  search: {
    type: String,
    required: true
  },
  atTheRateActive: {
    type: Boolean,
    required: true
  },
  toggleAtTheRateActive: {
    type: Function as PropType<(event: MouseEvent) => void>,
    required: true
  }
});

const emit = defineEmits(['update:search', 'toggle-expand']);
function onInput(event: Event) {
  emit('update:search', (event.target as HTMLInputElement).value);
}
</script>

<template>
  <div
    class="px-4 flex items-center justify-between py-2 border-b border-gray-200"
  >
    <h2 class="text-lg font-medium">{{ room.name }}</h2>
    <div class="flex space-x-2">
      <div class="flex items-center space-x-2">
          <input
          v-if="inputActive"
          type="text"
          :value="search"
          @input="onInput"
          class="w-full px-4  py-2 bg-gray-50 border-1 border-gray-300  focus:outline-none focus:ring-1 focus:ring-blue-500 delay-300 ease-in-out focus:bg-white transition-all duration-200 placeholder-gray-500 resize-none overflow-y-auto rounded-full"
          placeholder="Search"
        />
        <button @click="()=>{
         
          inputActive = !inputActive;
          if(inputActive){
            $emit('update:search','')
          }
        }" class="p-1">
          <div class="relative">

            <Search class="size-5 relative" />
            <span class="absolute top-0 " v-if="search">.</span>
          </div>
        </button>
        <button class="p-1 font-bold text-lg cursor-pointer" :class=" {'text-blue-500 font-bold bg-gray-100 ': atTheRateActive}"  @click="toggleAtTheRateActive">
          @
        </button>
      </div>
      <button class="p-1">
        <Expand class="size-5" @click="$emit('toggle-expand')" />
      </button>
      <!-- not required for now -->
      <!-- <button class="p-1">
        <SquareArrowOutUpRight class="size-5" />
      </button> -->
    </div>
  </div>
</template>
