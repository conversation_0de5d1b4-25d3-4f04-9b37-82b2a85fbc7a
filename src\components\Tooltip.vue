<script setup lang="ts">
import { onMounted } from "vue";
import { Tooltip, TooltipOptions, TooltipTriggerType } from "flowbite";
import type { Placement } from "@popperjs/core";

defineOptions({
  inheritAttrs: false
});

const emit = defineEmits(["show", "hide"]);

const props = withDefaults(
  defineProps<{
    id: string;
    triggerId: string;
    placement?: Placement;
    triggerType?: TooltipTriggerType;
  }>(),
  {
    placement: "top",
    trigger: "hover"
  }
);

onMounted(() => {
  const targetEl = document.getElementById(props.id);
  const triggerEl = document.getElementById(props.triggerId);

  const options: TooltipOptions = {
    placement: props.placement,
    triggerType: props.triggerType,
    onShow: () => {
      emit("show");
    },
    onHide: () => {
      emit("hide");
    }
  };

  new Tooltip(targetEl, triggerEl, options);
});
</script>

<template>
  <button type="button" v-bind="$attrs" :id="triggerId">
    <slot name="trigger" />
  </button>

  <Teleport to="#modal-section">
    <div
      :id="id"
      role="tooltip"
      class="tooltip invisible absolute z-50 inline-block rounded bg-black px-2 pb-1 pt-1.5 text-xs font-medium text-white opacity-0 transition-opacity duration-300"
    >
      <slot name="content" />

      <div class="tooltip-arrow" data-popper-arrow></div>
    </div>
  </Teleport>
</template>
