<script setup lang="ts">
import { ref } from "vue";
import { object, string } from "yup";
import { toast } from "vue3-toastify";
import { useWindowSize } from "@vueuse/core";

import { axios } from "@/api";
import {
  getClientValidationErrors,
  getServerErrors
} from "@/helpers/getErrors";
import { useUserStore } from "@/store/userStore";

import Alert from "@/components/Alert.vue";
import Select from "@/components/Select.vue";
import Textarea from "@/components/Textarea.vue";
import InputText from "@/components/InputText.vue";
import InputLabel from "@/components/InputLabel.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const userStore = useUserStore();

const { height: cardHeight } = useWindowSize();

const contact = ref({
  title: "",
  category: "",
  description: ""
});
const btnLoading = ref(false);
const validationErrors = ref<typeof contact.value | null>(null);
const serverErrors = ref<string[]>([]);

const validationSchema = object({
  title: string().required("Title is required."),
  category: string().required("Subject is required."),
  description: string()
    .max(500, "Please limit your description up to 500 characters only.")
    .required("Description is required.")
});

async function handleContact() {
  try {
    validationErrors.value = null;
    serverErrors.value = [];

    btnLoading.value = true;

    const validateContact = await validationSchema.validate(contact.value, {
      abortEarly: false
    });

    const resp = await axios.post("/contact", {
      ...validateContact,
      email: userStore.user?.email
    });

    toast.info(resp.data.message);

    contact.value = {
      title: "",
      category: "",
      description: ""
    };
  } catch (e) {
    validationErrors.value = getClientValidationErrors(e);
    serverErrors.value = getServerErrors(e);
  } finally {
    btnLoading.value = false;
  }
}
</script>

<template>
  <div
    class="mx-auto w-10/12 overflow-auto rounded-md bg-white p-5 shadow-lg"
    :style="{ height: cardHeight - 55 - 40 + 'px' }"
  >
    <h1 class="text-center text-2xl font-bold">Contact Us</h1>

    <div class="mx-auto mt-4 w-52 border-t-2 border-blue-700"></div>

    <h2 class="mt-6 text-center text-gray-600">
      If you have a question or are experiencing an issue, please fill the form
      below to reach out to us.
    </h2>

    <form class="mx-auto mt-5 px-8">
      <Alert
        variant="danger"
        class="mb-4 text-xs"
        v-if="serverErrors.length !== 0"
      >
        <div v-for="error in serverErrors">
          {{ error }}
        </div>
      </Alert>

      <div>
        <InputLabel for="title">Title</InputLabel>

        <InputText
          id="title"
          placeholder="Title"
          :error="validationErrors?.title"
          v-model="contact.title"
        />
      </div>

      <div class="mt-4">
        <InputLabel for="category">Category</InputLabel>

        <Select
          id="category"
          placeholder="Category"
          :error="validationErrors?.category"
          v-model="contact.category"
        >
          <option value="account">Account</option>
          <option value="ea">Expert Advisor</option>
          <option value="others">Others</option>
        </Select>
      </div>

      <div class="mt-4">
        <InputLabel for="description">Description</InputLabel>

        <Textarea
          rows="7"
          id="description"
          :error="validationErrors?.description"
          v-model="contact.description"
        />
      </div>

      <div class="mt-4">
        <PrimaryButton
          class="flex w-full justify-center"
          :loading="btnLoading"
          @click="handleContact"
        >
          Submit
        </PrimaryButton>
      </div>
    </form>
  </div>
</template>
