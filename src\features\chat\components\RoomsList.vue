<script setup lang="ts">
import { PropType, ref } from "vue";

import { ChevronDown, ChevronUp, MessageCircle } from "lucide-vue-next";

import { convertToRelative } from "@/lib/utils/time";

import { Room } from "@/types/chat";

const { rooms } = defineProps({
  rooms: {
    type: Array as PropType<Room[]>,
    required: true
  },
  currentRoom: {
    type: Object as PropType<Room>,
    required: true
  }
});

const expanded = ref<boolean>(true);

const emit = defineEmits(["select-room", "collapse-change"]);

const toggleExpand = () => {
  expanded.value = !expanded.value;
  emit("collapse-change", !expanded.value);
};
</script>

<template>
  <div class="bg-white shadow-sm border-r border-gray-200 flex flex-col h-full">
    <!-- Header -->
    <div
      class="flex items-center justify-between px-4 py-3 border-b border-gray-100 bg-gray-50"
    >
      <h3 class="font-semibold text-gray-800 flex items-center gap-2">
        <MessageCircle class="w-4 h-4" />
        Rooms
      </h3>
      <!-- @click="toggleExpand" -->
       <!-- class hover:bg-gray-200 -->
      <button
        class="p-1.5 rounded-lg  transition-colors duration-200 text-gray-600 hover:text-gray-800"
      >
        {{ rooms.length }} {{ rooms.length === 1 ? 'room' : 'rooms' }}
      </button>
    </div>

    <!-- Rooms List -->
    <div v-if="expanded" class="flex-1 overflow-y-auto">
      <div
        v-for="room in rooms"
        :key="room._id"
        class="group px-4 py-3 border-b border-gray-50 cursor-pointer transition-all duration-200 hover:bg-blue-50 hover:border-blue-100"
        :class="{
          'bg-blue-100 border-blue-200 shadow-sm':
            currentRoom.name === room.name,
          'hover:shadow-sm': currentRoom.name !== room.name
        }"
        @click="$emit('select-room', room)"
      >
        <!-- Room Name -->
        <div class="flex items-center justify-between mb-2">
          <span
            class="font-medium text-gray-900 transition-colors"
            :class="{ 'text-blue-700': currentRoom.name === room.name }"
          >
            {{ room.name }}
          </span>
        </div>

        <!-- Room Metadata -->
        <div class="flex items-center gap-2 text-xs text-gray-500">
          <template v-if="room.lastMessage">
            <span class="font-semibold"
              >{{ room.lastMessage.sender.user_name }}:</span
            >
            <span class="truncate">{{ room.lastMessage.content }}</span>
            <span class="text-gray-500">•</span>
            <span class="whitespace-nowrap">{{
              convertToRelative(room.lastMessage.timestamp) === "0m"
                ? "Just Now"
                : convertToRelative(room.lastMessage.timestamp)
            }}</span>
          </template>
          <template v-else>
            <span>No messages</span>
          </template>
        </div>
      </div>
    </div>

    <!-- Collapsed State - More compact display -->
    <div v-else class="px-4 py-1 text-center text-xs text-gray-500 truncate">
      {{ rooms.length }} {{ rooms.length === 1 ? 'room' : 'rooms' }}
    </div>
  </div>
</template>
