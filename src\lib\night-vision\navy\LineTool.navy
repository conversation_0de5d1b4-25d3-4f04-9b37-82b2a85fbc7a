// Navy ~ 0.2-lite
// <ds>Drawing lines</ds>

[OVERLAY name=LineTool, ctx=Canvas, version=1.0.0]
var lines = {}; // Line objects stored by uuid
var horizontalLines = {};
var verticalLines = {};
var priceRanges = {};
var dateRanges = {};
var dateAndPriceRanges = {};
var rectangles = {};
var selectedLine = null;
var drawingShape = false;

function allLines(){
    return {...lines, ...horizontalLines, ...verticalLines, ...rectangles, ...priceRanges, ...dateRanges, ...dateAndPriceRanges};
}

function getLines() {
    const storedLines = localStorage.getItem('pipclimber__lines');
    if (storedLines) {
        parsedLines = JSON.parse(storedLines);
        parsedLines.forEach(line => {
            const lib = $core.props.config.lib;
            const lineType = line.type;
            const p1 = line.p1?{x : line.p1[0],y: line.p1[1] }:null;
            const p2 = line.p2?{x : line.p2[0],y: line.p2[1] }:null
            let uuid = $lib.Utils.uuid3()
            let c_line = null;
            if(lineType === "horizontal-line"){
                horizontalLines[uuid] = new lib.HLine($core,uuid,[p1], false, selectLine);
                c_line = horizontalLines[uuid];
            }
            else if(lineType === "vertical-line"){
                verticalLines[uuid] = new lib.VLine($core,uuid,[p1], false, selectLine);
                c_line = verticalLines[uuid];
            }
            else if(lineType === "box"){
                rectangles[uuid] = new lib.Rectangle($core,uuid,line.points, false, selectLine);
                c_line = rectangles[uuid];
            }
            else if(lineType === "price-range"){
                priceRanges[uuid] = new lib.PriceRange($core,uuid,[p1,p2], false, selectLine);
                c_line = priceRanges[uuid];
            }
            else if(lineType === "date-range"){
                dateRanges[uuid] = new lib.PriceRange($core,uuid,[p1,p2], false, selectLine);
                c_line = dateRanges[uuid];
            }
            else if(lineType === "date-and-price-range"){
                dateAndPriceRanges[uuid] = new lib.DateAndPriceRange($core,uuid,line.points, false, selectLine);
                c_line = dateAndPriceRanges[uuid];
            }
            else {
                lines[uuid] = new lib.Line($core,uuid, [p1,p2], false, selectLine);
                c_line = lines[uuid];
            }
        });
    }
}

function selectLine(line){
    let selectedLine = Object.values(allLines()).find(line => line.selected);
    if (!selectedLine) {
        line.selected = true;
        $events.emit('update-layout');
    }
}

function saveLines() {
    if(!lines) return;
    const nlines = Object.values(lines).map(l => ({
        type: "line",
        p1: [l.points[0].x, l.points[0].y],
        p2: [l.points[1].x, l.points[1].y]
    }));
    const pRanges = Object.values(priceRanges).map(l => ({
        type: "price-range",
        p1: [l.points[0].x, l.points[0].y],
        p2: [l.points[1].x, l.points[1].y]
    }))
    const dRanges = Object.values(dateRanges).map(l => ({
        type: "date-range",
        p1: [l.points[0].x, l.points[0].y],
        p2: [l.points[1].x, l.points[1].y]
    }))
    const datRanges = Object.values(dateAndPriceRanges).map(b => ({
        type: "date-and-price-range",
        points: b.points.map(p => ({x: p.x, y: p.y}))
    }))
    const hlines = Object.values(horizontalLines).map(l => ({
        type: "horizontal-line",
        p1: [l.points[0].x, l.points[0].y],
    }));
    const vlines = Object.values(verticalLines).map(l => ({
        type: "vertical-line",
        p1: [l.points[0].x, l.points[0].y],
    }));
    const boxes = Object.values(rectangles).map(b => ({
        type: "box",
        points: b.points.map(p => ({x: p.x, y: p.y}))
    }))
    localStorage.setItem('pipclimber__lines', JSON.stringify([nlines,hlines,vlines,boxes,pRanges, dRanges, datRanges].flat()));
}

init() {
    getLines();
    // $events.emit('update-layout');
    document.addEventListener('visibilitychange', saveLines);
    window.addEventListener('beforeunload', function(event) {
        saveLines();
        event.returnValue = ''; // This line ensures that the confirmation dialog appears in some browsers
    });
}

draw(ctx) {
    for (let line of Object.values(allLines())) {
        line.draw(ctx);
    }
}

drawSidebar(ctx, _ , scale) {
    for (let line of Object.values(allLines())) {
        line.drawSidebar(ctx, _ , scale);
    }
}

drawBotbar(ctx) {
    for (let line of Object.values(allLines())) {
        line.drawBotbar(ctx);
    }
}

newLine() {
    $events.emit('scroll-lock', true);
    const lineType = $core.props.config.lineType;
    let x = $core.cursor.x;
    let y = $core.cursor.y;
    let uuid = Math.random().toString().slice(2).replace(/^0+/, '')
    const lib = $core.props.config.lib;
    let line;
    drawingShape = true
    if(lineType === "horizontal-line"){
      horizontalLines[uuid] = new lib.HLine($core,uuid,[{x,y}]);
      line = horizontalLines[uuid]
      line.points[0].dragDirection = "y";
    }
    else if(lineType === "vertical-line"){
      verticalLines[uuid] = new lib.VLine($core,uuid,[{x,y}]);
      line = verticalLines[uuid]
      line.points[0].dragDirection = "x";
    }
    else if(lineType === "price-range"){
        priceRanges[uuid] = new lib.PriceRange($core,uuid,[{x,y}, {x,y}]);
        priceRanges[uuid].points[1].startDragging('y');
    }
    else if(lineType === "date-range"){
        dateRanges[uuid] = new lib.DateRange($core,uuid,[{x,y}, {x,y}]);
        dateRanges[uuid].points[1].startDragging('x');
    }
    else if(lineType === "date-and-price-range"){
        dateAndPriceRanges[uuid] = new lib.DateAndPriceRange($core,uuid,[{x,y}, {x,y}, {x,y},{x,y}]);
        line = dateAndPriceRanges[uuid]
        
        line.points[1].dragDirection = "x";
        line.points[3].dragDirection = "y";

        line.points[2].startDragging();
        line.points[3].startDragging("y");
        line.points[1].startDragging("x");
    }
    else if(lineType === "box"){
      rectangles[uuid] = new lib.Rectangle($core,uuid,[{x,y},{x,y},{x,y},{x,y}]);
      line = rectangles[uuid]
      
      line.points[1].dragDirection = "x";
      line.points[3].dragDirection = "y";
      
      line.points[2].startDragging();
      line.points[3].startDragging("y");
      line.points[1].startDragging("x");
    }
    else {
        line = new lib.Line($core,uuid, [{x,y},{x,y}]);
        lines[uuid] = line;
        lines[uuid].points[1].dragging = true
    }
    if(line){
        line.selected = true;
        line.onSelect = selectLine
        selectedLine = line
    }
    $events.emit('update-layout');
}

lineSelected(uuid) {
    for (let lineUuid in lines) {
        lines[lineUuid].selected = lineUuid === uuid;
    }
    $events.emit('update-layout');
}

removeLine(uuid) {
    lines[uuid] = null;
    delete lines[uuid];
    horizontalLines[uuid] = null;
    delete horizontalLines[uuid];
    verticalLines[uuid] = null;
    delete verticalLines[uuid]
    rectangles[uuid] = null;
    delete rectangles[uuid]
    dateRanges[uuid] = null;
    delete dateRanges[uuid]
    dateAndPriceRanges[uuid] = null;
    delete dateAndPriceRanges[uuid]
    priceRanges[uuid] = null;
    delete priceRanges[uuid]
    $events.emit('update-layout');
}

propagate(name, data) {
    for (let line of Object.values(allLines())) {
        line[name](data);
    }
} 
mouseover(event){
    propagate('mouseover', event);
}
mousedown(event) {
    mouseX = $core.cursor.values[0][0];
    propagate('mousedown', event);
    selectedLine = Object.values(allLines()).find(line => line.selected);
    $events.emit("select-shape", selectedLine);
    $events.emit('update-layout')
    if ($core.props.config.makingLine) newLine();
    $core.props.config.makingLine = false;
}

mouseup(event) {
    propagate('mouseup', event);
    if(drawingShape){
        if(selectedLine && selectedLine.points.length > 1){
            const basePoint = selectedLine.points[0]
            for(let i = 1; i < selectedLine.points.length; i++){
                const currentPoint = selectedLine.points[i]
                if(basePoint.x === currentPoint.x && basePoint.y === currentPoint.y){
                    currentPoint.startDragging();
                }
            }
        }
    }
    $core.props.config.makingLine = false;
    saveLines();
}

mousemove(event) {
    propagate('mousemove', event);
    propagate('mouseover', event);
}

keydown(event) {
    if (event.code === 'Backspace') {
        if (selectedLine) removeLine(selectedLine.uuid);
    }
}

legend() => null;
