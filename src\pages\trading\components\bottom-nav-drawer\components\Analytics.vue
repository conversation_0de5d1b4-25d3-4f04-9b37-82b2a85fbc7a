<script setup lang="ts">
import { ref, watch } from "vue";
import { initFlowbite } from "flowbite";
import ApexCharts from "apexcharts";
import { ApexOptions } from "apexcharts";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { axios } from "@/api";
import { useCandleStickStore } from "@/store/candleStickStore";
import { useBottomNavDrawerStore } from "@/store/bottomNavDrawerStore";
import {
  ProfitAnalytics,
  AnalyticsSummary,
  ProfitInstrumentAnalytics
} from "@/types";

import RiskTradeCalculation from "./RiskTradeCalculation.vue";

import Select from "@/components/Select.vue";

const props = defineProps<{
  activeTab: string;
}>();

const candleStickStore = useCandleStickStore();
const bottomNavDrawerStore = useBottomNavDrawerStore();

const analyticsSummary = ref<AnalyticsSummary | null>(null);
const balanceProfit = ref<ProfitAnalytics[]>([]);
const dailyProfits = ref<ProfitAnalytics[]>([]);
const weeklyProfits = ref<ProfitAnalytics[]>([]);
const monthlyProfits = ref<ProfitAnalytics[]>([]);
const selectedChartOne = ref("balance");
const instrumentProfits = ref<ProfitInstrumentAnalytics[]>([]);
const instrumentTrades = ref<ProfitInstrumentAnalytics[]>([]);
const dailyTrades = ref<ProfitInstrumentAnalytics[]>([]);
const weeklyTrades = ref<ProfitInstrumentAnalytics[]>([]);
const selectedChartTwo = ref("profit");

let chartOne: ApexCharts | null = null;
let chartOneOptions: ApexOptions | null = null;
let chartTwo: ApexCharts | null = null;
let chartTwoOptions: ApexOptions | null = null;

watch(
  () => [props.activeTab, candleStickStore.marketWatchOnlineStatus],
  ([tab, onlineStatus]) => {
    if (tab === "analytics-tab" && onlineStatus) {
      getAnalyticsSummary();

      getProfitsAnalytics();

      getInstrumentsAnalytics();

      initFlowbite();
    }
  }
);

async function getAnalyticsSummary() {
  try {
    const mt5Id = candleStickStore.eaAccount?.mt5_id;

    const resp = await axios.get(`/analytics/summary/${mt5Id}`);

    analyticsSummary.value = resp.data.data;
  } catch (e) {
    console.error(e);
  }
}

async function getProfitsAnalytics() {
  try {
    const mt5Id = candleStickStore.eaAccount?.mt5_id;

    let profit = selectedChartOne.value;

    if (profit === "daily_return") {
      profit = "daily";
    } else if (profit === "weekly_return") {
      profit = "weekly";
    } else if (profit === "monthly_return") {
      profit = "monthly";
    }

    const resp = await axios.get(`/analytics/profit/${profit}/${mt5Id}`);

    if (profit === "balance") {
      balanceProfit.value = resp.data.data;
    } else if (profit === "daily") {
      dailyProfits.value = resp.data.data;
      dailyProfits.value.sort((a, b) => b.date.localeCompare(a.date));
    } else if (profit === "weekly") {
      weeklyProfits.value = resp.data.data;
      weeklyProfits.value.sort((a, b) => b.date.localeCompare(a.date));
    } else if (profit === "monthly") {
      monthlyProfits.value = resp.data.data;
      monthlyProfits.value.sort((a, b) => b.date.localeCompare(a.date));
    }

    handleProfitChartType();
  } catch (e) {
    console.error(e);
  }
}

async function getInstrumentsAnalytics() {
  try {
    const mt5Id = candleStickStore.eaAccount?.mt5_id;

    const resp = await axios.get(
      `/analytics/instrument/${selectedChartTwo.value}/${mt5Id}`
    );

    if (selectedChartTwo.value === "profit") {
      instrumentProfits.value = resp.data.data;
    } else if (selectedChartTwo.value === "trade") {
      instrumentTrades.value = resp.data.data;
    }

    handleInstrumentChartType();
  } catch (e) {
    console.error(e);
  }
}

async function getTradesAnalytics() {
  try {
    const mt5Id = candleStickStore.eaAccount?.mt5_id;

    const resp = await axios.get(
      `/analytics/trades/${selectedChartTwo.value}/${mt5Id}`
    );

    if (selectedChartTwo.value === "daily") {
      dailyTrades.value = resp.data.data;
    } else if (selectedChartTwo.value === "week") {
      weeklyTrades.value = resp.data.data;
    }

    handleInstrumentChartType();
  } catch (e) {
    console.error(e);
  }
}

function handleProfitChartType() {
  if (selectedChartOne.value === "balance") {
    handleBalanceProfitChart();
  } else if (
    selectedChartOne.value === "daily" ||
    selectedChartOne.value === "daily_return"
  ) {
    if (dailyProfits.value.length === 0) {
      getProfitsAnalytics();
      return;
    }

    handleDailyProfitChart();
  } else if (
    selectedChartOne.value === "weekly" ||
    selectedChartOne.value === "weekly_return"
  ) {
    if (weeklyProfits.value.length === 0) {
      getProfitsAnalytics();
      return;
    }

    handleWeeklyProfitChart();
  } else if (
    selectedChartOne.value === "monthly" ||
    selectedChartOne.value === "monthly_return"
  ) {
    if (monthlyProfits.value.length === 0) {
      getProfitsAnalytics();
      return;
    }

    handleMonthlyProfitChart();
  }
}

function handleBalanceProfitChart() {
  const balances: number[] = [];
  const dates: string[] = [];

  balanceProfit.value.forEach((v) => {
    balances.push(v.balance);
    dates.push(v.date);
  });

  chartOne?.destroy();

  chartOneOptions = {
    series: [
      {
        name: "Balance",
        data: balances
      }
    ],
    chart: {
      type: "area",
      height: 260,
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    dataLabels: {
      enabled: false
    },
    xaxis: {
      categories: dates
    },
    tooltip: {
      x: {
        format: "dd/MM/yyyy"
      }
    }
  };

  const chartElement = document.getElementById("chart-one");
  chartOne = new ApexCharts(chartElement, chartOneOptions);
  chartOne.render();
}

function handleDailyProfitChart() {
  const profits: number[] = [];
  const dates: string[] = [];

  const seriesName = selectedChartOne.value === "daily" ? "Profit" : "% Return";

  dailyProfits.value.forEach((v) => {
    if (selectedChartOne.value === "daily") {
      profits.push(v.profitAmount);
    } else {
      profits.push(v.profitReturns);
    }

    dates.push(v.date);
  });

  chartOne?.destroy();

  chartOneOptions = {
    series: [
      {
        name: seriesName,
        data: profits
      }
    ],
    chart: {
      type: "bar",
      height: 260,
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    dataLabels: {
      enabled: false
    },
    xaxis: {
      type: "datetime",
      categories: dates
    },
    tooltip: {
      x: {
        format: "dd/MM/yyyy"
      }
    }
  };

  const chartElement = document.getElementById("chart-one");
  chartOne = new ApexCharts(chartElement, chartOneOptions);
  chartOne.render();
}

function handleWeeklyProfitChart() {
  const profits: number[] = [];
  const dates: string[] = [];

  const seriesName =
    selectedChartOne.value === "weekly" ? "Profit" : "% Return";

  weeklyProfits.value.forEach((v) => {
    if (selectedChartOne.value === "weekly") {
      profits.push(v.profitAmount);
    } else {
      profits.push(v.profitReturns);
    }

    dates.push(v.date);
  });

  chartOne?.destroy();

  chartOneOptions = {
    series: [
      {
        name: seriesName,
        data: profits
      }
    ],
    chart: {
      type: "bar",
      height: 260,
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    dataLabels: {
      enabled: false
    },
    xaxis: {
      type: "datetime",
      categories: dates
    },
    tooltip: {
      x: {
        format: "dd/MM/yyyy"
      }
    }
  };

  const chartElement = document.getElementById("chart-one");
  chartOne = new ApexCharts(chartElement, chartOneOptions);
  chartOne.render();
}

function handleMonthlyProfitChart() {
  const profits: number[] = [];
  const dates: string[] = [];

  const seriesName =
    selectedChartOne.value === "monthly" ? "Profit" : "% Return";

  monthlyProfits.value.forEach((v) => {
    if (selectedChartOne.value === "monthly") {
      profits.push(v.profitAmount);
    } else {
      profits.push(v.profitReturns);
    }

    dates.push(v.date);
  });

  chartOne?.destroy();

  chartOneOptions = {
    series: [
      {
        name: seriesName,
        data: profits
      }
    ],
    chart: {
      type: "bar",
      height: 260,
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    dataLabels: {
      enabled: false
    },
    xaxis: {
      type: "datetime",
      categories: dates
    },
    tooltip: {
      x: {
        format: "dd/MM/yyyy"
      }
    }
  };

  const chartElement = document.getElementById("chart-one");
  chartOne = new ApexCharts(chartElement, chartOneOptions);
  chartOne.render();
}

function handleInstrumentChartType() {
  if (selectedChartTwo.value === "profit") {
    handleInstrumentProfitChart();
  } else if (selectedChartTwo.value === "trade") {
    if (instrumentTrades.value.length === 0) {
      getInstrumentsAnalytics();
      return;
    }

    handleInstrumentTradeChart();
  } else if (selectedChartTwo.value === "daily") {
    if (dailyTrades.value.length === 0) {
      getTradesAnalytics();
      return;
    }

    handleDailyTradeChart();
  } else if (selectedChartTwo.value === "week") {
    if (weeklyTrades.value.length === 0) {
      getTradesAnalytics();
      return;
    }

    handleWeeklyTradeChart();
  }
}

function handleInstrumentProfitChart() {
  const profits: number[] = [];
  const instruments: string[] = [];

  instrumentProfits.value.forEach((v) => {
    profits.push(v.profit);
    instruments.push(v.instrument);
  });

  chartTwo?.destroy();

  chartTwoOptions = {
    series: [
      {
        name: "Profit",
        data: profits
      }
    ],
    chart: {
      type: "bar",
      height: 260,
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    dataLabels: {
      enabled: false
    },
    xaxis: {
      categories: instruments
    }
  };

  const chartElement = document.getElementById("chart-two");
  chartTwo = new ApexCharts(chartElement, chartTwoOptions);
  chartTwo.render();
}

function handleInstrumentTradeChart() {
  const trades: number[] = [];
  const instruments: string[] = [];

  instrumentTrades.value.forEach((v) => {
    trades.push(v.trades);
    instruments.push(v.instrument);
  });

  chartTwo?.destroy();

  chartTwoOptions = {
    series: trades,
    chart: {
      type: "pie",
      height: 300
    },
    labels: instruments
  };

  const chartElement = document.getElementById("chart-two");
  chartTwo = new ApexCharts(chartElement, chartTwoOptions);
  chartTwo.render();
}

function handleDailyTradeChart() {
  const trades: number[] = [];
  const dates: string[] = [];

  dailyTrades.value.forEach((v) => {
    trades.push(v.trades);
    dates.push(v.date);
  });

  chartTwo?.destroy();

  chartTwoOptions = {
    series: [
      {
        name: "Trades",
        data: trades
      }
    ],
    chart: {
      type: "bar",
      height: 300,
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    dataLabels: {
      enabled: false
    },
    xaxis: {
      type: "datetime",
      categories: dates
    },
    tooltip: {
      x: {
        format: "dd/MM/yyyy"
      }
    }
  };

  const chartElement = document.getElementById("chart-two");
  chartTwo = new ApexCharts(chartElement, chartTwoOptions);
  chartTwo.render();
}

function handleWeeklyTradeChart() {
  const trades: number[] = [];
  const dates: string[] = [];

  weeklyTrades.value.forEach((v) => {
    trades.push(v.trades);
    dates.push(v.date);
  });

  chartTwo?.destroy();

  chartTwoOptions = {
    series: [
      {
        name: "Trades",
        data: trades
      }
    ],
    chart: {
      type: "bar",
      height: 300,
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    dataLabels: {
      enabled: false
    },
    xaxis: {
      type: "datetime",
      categories: dates
    },
    tooltip: {
      x: {
        format: "dd/MM/yyyy"
      }
    }
  };

  const chartElement = document.getElementById("chart-two");
  chartTwo = new ApexCharts(chartElement, chartTwoOptions);
  chartTwo.render();
}
</script>

<template>
  <div
    class="scrollbar overflow-auto p-3 text-sm"
    :style="{
      height: bottomNavDrawerStore.bottomNavContentAreaHeight - 48 + 'px'
    }"
  >
    <div
      class="pt-4 text-center text-sm"
      v-if="!candleStickStore.marketWatchOnlineStatus"
    >
      Connect EA to see analytics.
    </div>

    <template v-else>
      <div class="grid grid-cols-12 items-start gap-x-8">
        <div class="col-span-3">
          <div
            id="analytics-stats-accordion"
            class="rounded-md border"
            data-accordion="collapse"
          >
            <div id="analytics-stats-accordion-header">
              <button
                type="button"
                aria-expanded="true"
                class="flex w-full cursor-pointer items-center justify-between bg-accent p-3 font-medium hover:text-primary"
                aria-controls="analytics-stats-accordion-header"
                data-accordion-target="#analytics-stats-accordion-body"
              >
                <div class="text-base font-semibold">Stats</div>

                <FontAwesomeIcon icon="fa-solid fa-chevron-down" />
              </button>
            </div>

            <div
              id="analytics-stats-accordion-body"
              class="mt-3 divide-y px-3"
              aria-labelledby="analytics-stats-accordion-header"
            >
              <div class="flex justify-between pb-2">
                <div>Total return</div>

                <div class="font-medium">
                  {{ analyticsSummary?.total_return }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Today return</div>

                <div class="font-medium">
                  {{ analyticsSummary?.today_return }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Weekly return</div>

                <div class="font-medium">
                  {{ analyticsSummary?.week_td_return }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Monthly return</div>

                <div class="font-medium">
                  {{ analyticsSummary?.month_td_return }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Yearly return</div>

                <div class="font-medium">
                  {{ analyticsSummary?.year_td_return }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Max drawdown</div>

                <div class="font-medium">
                  {{ analyticsSummary?.max_drawdown }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Trade win %</div>

                <div class="font-medium">
                  {{ analyticsSummary?.trade_win_percentage }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Profit factor</div>

                <div class="font-medium">
                  {{ analyticsSummary?.profit_factor }}
                </div>
              </div>
            </div>
          </div>

          <RiskTradeCalculation />

          <div
            id="analytics-risk-accordion"
            class="mt-3 rounded-md border"
            data-accordion="collapse"
          >
            <div id="analytics-risk-accordion-header">
              <button
                type="button"
                aria-expanded="false"
                class="flex w-full cursor-pointer items-center justify-between bg-accent p-3 font-medium hover:text-primary"
                aria-controls="analytics-risk-accordion-header"
                data-accordion-target="#analytics-risk-accordion-body"
              >
                <div class="text-base font-semibold">Risk</div>

                <FontAwesomeIcon icon="fa-solid fa-chevron-down" />
              </button>
            </div>

            <div
              id="analytics-risk-accordion-body"
              class="mt-3 hidden divide-y px-3"
              aria-labelledby="analytics-risk-accordion-header"
            >
              <div class="flex justify-between pb-2">
                <div>Risk/reward ratio</div>

                <div class="font-medium">
                  {{ analyticsSummary?.average.risk_reward_ratio }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Worst day</div>

                <div class="font-medium">
                  {{ analyticsSummary?.worst_day.profit }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Worst week</div>

                <div class="font-medium">
                  {{ analyticsSummary?.worst_week.profit }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Best day</div>

                <div class="font-medium">
                  {{ analyticsSummary?.best_day.profit }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Best week</div>

                <div class="font-medium">
                  {{ analyticsSummary?.best_week.profit }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Trade length</div>

                <div class="font-medium">
                  {{ analyticsSummary?.average_trade_length }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Avg result</div>

                <div class="font-medium">
                  {{ analyticsSummary?.average.average_result }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Avg win</div>

                <div class="font-medium">
                  {{ analyticsSummary?.average.average_win }}
                </div>
              </div>

              <div class="flex justify-between py-2">
                <div>Avg loss</div>

                <div class="font-medium">
                  {{ analyticsSummary?.average.average_loss }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-span-9 rounded-md border p-3">
          <div>
            <div class="w-60">
              <Select
                :defaultOption="false"
                v-model="selectedChartOne"
                @change="handleProfitChartType"
              >
                <optgroup label="Balance">
                  <option value="balance">Balance</option>
                  <option value="daily">Daily Profit</option>
                  <option value="weekly">Weekly Profit</option>
                  <option value="monthly">Monthly Profit</option>
                </optgroup>

                <optgroup label="Return">
                  <option value="daily_return">Daily Return</option>
                  <option value="weekly_return">Weekly Return</option>
                  <option value="monthly_return">Monthly Return</option>
                </optgroup>
              </Select>
            </div>

            <div id="chart-one"></div>
          </div>

          <div class="mt-9">
            <div class="w-64">
              <Select
                :defaultOption="false"
                v-model="selectedChartTwo"
                @change="handleInstrumentChartType"
              >
                <option value="profit">Instrument Profit</option>
                <option value="trade">Instrument number of trades</option>
                <option value="daily">Trades per day</option>
                <option value="week">Trades per week</option>
              </Select>
            </div>

            <div id="chart-two"></div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>
