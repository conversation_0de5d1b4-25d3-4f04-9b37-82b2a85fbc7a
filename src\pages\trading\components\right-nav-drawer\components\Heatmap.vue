<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useWindowSize } from "@vueuse/core";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

import { calculatePips } from "@/helpers/chartCalculation";
import { useCandleStickStore } from "@/store/candleStickStore";
import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";

import Toggle from "@/components/Toggle.vue";
import NavItem from "@/components/NavItem.vue";

const candleStickStore = useCandleStickStore();
const rightNavDrawerStore = useRightNavDrawerStore();

const { height } = useWindowSize();

const width = ref(1000);
const symbolList = ref([
  {
    id: "EUR",
    image: "https://cdn.countryflags.com/thumbs/europe/flag-round-250.png"
  },
  {
    id: "USD",
    image:
      "https://cdn.countryflags.com/thumbs/united-states-of-america/flag-round-250.png"
  },
  {
    id: "AUD",
    image: "https://cdn.countryflags.com/thumbs/australia/flag-round-250.png"
  },
  {
    id: "GBP",
    image:
      "https://cdn.countryflags.com/thumbs/united-kingdom/flag-round-250.png"
  },
  {
    id: "NZD",
    image: "https://cdn.countryflags.com/thumbs/new-zealand/flag-round-250.png"
  },
  {
    id: "CAD",
    image: "https://cdn.countryflags.com/thumbs/canada/flag-round-250.png"
  },
  {
    id: "CHF",
    image: "https://cdn.countryflags.com/thumbs/switzerland/flag-round-250.png"
  },
  {
    id: "JPY",
    image: "https://cdn.countryflags.com/thumbs/japan/flag-round-250.png"
  }
]);
const currencyPairList = ref<string[][]>([]);
const showPips = ref(candleStickStore.chartSettings.heatmap_show_pips);

onMounted(() => {
  generateCurrencyPairs();
});

function generateCurrencyPairs() {
  for (let i = 0; i < symbolList.value.length; i++) {
    let arr = [];

    for (let j = 0; j < symbolList.value.length; j++) {
      const a = symbolList.value[i];
      const b = symbolList.value[j];

      arr.push(a.id + b.id);
    }

    currencyPairList.value.push(arr);
  }
}

function isCurrencyPairSame(currencyPair: string) {
  const s1 = currencyPair.substring(0, 3);
  const s2 = currencyPair.substring(3);

  return s1 === s2;
}

function getDailyChange(currencyPair: string) {
  const item = candleStickStore.marketWatchSymbolList.find((v) => {
    const symbol = v.symbol.match(/[A-Z]/g)?.join("");
    return symbol === currencyPair;
  });

  // EURUSD
  if (item) {
    let backgroundColor = "";
    let sign = 1;

    if (item.daily_change >= 0) {
      backgroundColor = "bg-green-100";
    } else {
      backgroundColor = "bg-red-200";
      sign = -1;
    }

    const openPrice = item.symbol_session_open;

    const dailyHigh = ((item.symbol_bid_high - openPrice) / openPrice) * 100;
    const dailyLow = ((item.symbol_bid_low - openPrice) / openPrice) * 100;

    const dailyHighPip = calculatePips(
      item.symbol_bid_high,
      openPrice,
      item.symbol_digit
    );

    const dailyChangePip = calculatePips(
      item.bid,
      openPrice,
      item.symbol_digit
    );

    const dailyLowPip = calculatePips(
      item.symbol_bid_low,
      openPrice,
      item.symbol_digit
    );

    let high = "";
    let dailyChange = "";
    let low = "";

    if (showPips.value) {
      high = dailyHighPip.toFixed(2);
      dailyChange = (sign * dailyChangePip).toFixed(2);
      low = (-1 * dailyLowPip).toFixed(2);
    } else {
      high = dailyHigh.toFixed(2) + "%";
      dailyChange = item.daily_change.toFixed(2) + "%";
      low = dailyLow.toFixed(2) + "%";
    }

    return `<div class="px-2 pb-2 pt-3 ${backgroundColor}">
      <span class="text-xs bg-green-500 px-1.5 rounded-md text-white">${high}</span>
      <div class="mt-1 mb-0.5">${dailyChange}</div>
      <span class="text-xs bg-red-500 px-1.5 rounded-md text-white">${low}</span>
    </div>`;
  }

  // USDEUR
  const s1 = currencyPair.substring(0, 3);
  const s2 = currencyPair.substring(3);

  const item1 = candleStickStore.marketWatchSymbolList.find((v) => {
    const symbol = v.symbol.match(/[A-Z]/g)?.join("");
    return symbol === `${s2}${s1}`;
  });

  if (!item1) {
    return "-";
  }

  const openPrice = item1.symbol_session_open;

  const dailyHigh =
    -1 * ((item1.symbol_bid_high - openPrice) / openPrice) * 100;
  const dailyChange = -1 * item1.daily_change;
  const dailyLow = -1 * ((item1.symbol_bid_low - openPrice) / openPrice) * 100;

  let backgroundColor = "";
  let sign = 1;

  if (dailyChange >= 0) {
    backgroundColor = "bg-green-100";
  } else {
    backgroundColor = "bg-red-200";
    sign = -1;
  }

  const dailyHighPip = calculatePips(
    item1.symbol_bid_high,
    openPrice,
    item1.symbol_digit
  );

  const dailyChangePip = calculatePips(
    item1.bid,
    openPrice,
    item1.symbol_digit
  );

  const dailyLowPip = calculatePips(
    item1.symbol_bid_low,
    openPrice,
    item1.symbol_digit
  );

  let high = "";
  let dc = "";
  let low = "";

  if (showPips.value) {
    high = (-1 * dailyHighPip).toFixed(2);
    dc = (sign * dailyChangePip).toFixed(2);
    low = dailyLowPip.toFixed(2);
  } else {
    high = dailyHigh.toFixed(2) + "%";
    dc = dailyChange.toFixed(2) + "%";
    low = dailyLow.toFixed(2) + "%";
  }

  return `<div class="px-2 pb-2 pt-3 ${backgroundColor}">
    <span class="text-xs bg-green-500 px-1.5 rounded-md text-white">${low}</span>
    <div class="mt-1 mb-0.5">${dc}</div>
    <span class="text-xs bg-red-500 px-1.5 rounded-md text-white">${high}</span>
  </div>`;
}

function getTotalDailyChange(idx: number) {
  const currencyPair = currencyPairList.value[idx];

  let totalDailyChange = 0;

  currencyPair?.forEach((v) => {
    const s1 = v.substring(0, 3);
    const s2 = v.substring(3);

    const item = candleStickStore.marketWatchSymbolList.find((v) => {
      const symbol = v.symbol.match(/[A-Z]/g)?.join("");
      return symbol === s1 + s2;
    });

    if (item) {
      totalDailyChange += item.daily_change;
    } else {
      const item1 = candleStickStore.marketWatchSymbolList.find((v) => {
        const symbol = v.symbol.match(/[A-Z]/g)?.join("");
        return symbol === s2 + s1;
      });

      if (item1) {
        totalDailyChange += -1 * item1.daily_change;
      }
    }
  });

  const totalCurrencies = symbolList.value.length;

  return (totalDailyChange / totalCurrencies).toFixed(3) + "%";
}

function handleTogglePip() {
  candleStickStore.storeChartSettings({
    heatmap_show_pips: showPips.value
  });
}

function rank(arr: number[]) {
  const indexedArr = arr.map((num, index) => ({ num, index }));

  const sortedArr = [...indexedArr].sort((a, b) => b.num - a.num);

  const result: string[] = new Array(arr.length);

  let rank = 1;

  for (let i = 0; i < sortedArr.length; i++) {
    const { index } = sortedArr[i];

    let suffix = "th";

    if (rank === 1) suffix = "st";
    else if (rank === 2) suffix = "nd";
    else if (rank === 3) suffix = "rd";

    result[index] = `${rank}${suffix}`;

    rank++;
  }

  return result;
}
</script>

<template>
  <div
    class="absolute right-0 top-0 flex flex-col overflow-auto border-l bg-white text-sm shadow-lg"
    :style="{ width: width + 'px', height: height + 'px' }"
  >
    <div class="flex items-center justify-between px-5 pt-3">
      <div class="text-xl font-bold">Heatmap</div>

      <NavItem @click="rightNavDrawerStore.toggleHeatMap(false)">
        <FontAwesomeIcon icon="fa-solid fa-xmark" size="xl" />
      </NavItem>
    </div>

    <div
      class="flex grow items-center justify-center"
      v-if="!candleStickStore.marketWatchOnlineStatus"
    >
      Connect EA to see heatmap.
    </div>

    <template v-else>
      <div class="mt-1 flex items-center justify-between px-5">
        <div class="flex gap-x-1.5">
          <span
            class="rounded-md bg-green-500 px-1.5 pb-0.5 pt-1 text-xs text-white"
          >
            High Price
          </span>

          <span
            class="rounded-md bg-red-500 px-1.5 pb-0.5 pt-1 text-xs text-white"
          >
            Low Price
          </span>
        </div>

        <div>
          <Toggle v-model="showPips" @change="handleTogglePip"
            >Show in Pips</Toggle
          >
        </div>
      </div>

      <div class="mt-2 px-5 pb-3">
        <table class="w-full table-fixed text-center">
          <thead>
            <tr>
              <th></th>

              <th
                class="px-3 pb-3 pt-4 font-normal"
                v-for="symbol in symbolList"
                :key="symbol.id"
              >
                <div class="flex items-center gap-x-2">
                  <img width="20" height="20" :src="symbol.image" />

                  {{ symbol.id }}
                </div>
              </th>

              <th></th>
            </tr>
          </thead>

          <tbody>
            <tr v-for="(symbol, idx) in symbolList" :key="symbol.id">
              <td class="px-2 pb-2 pt-3">
                <div class="flex items-center justify-center gap-x-2">
                  <img width="20" height="20" :src="symbol.image" />

                  {{ symbol.id }}
                </div>
              </td>

              <template
                v-for="currencyPair in currencyPairList[idx]"
                :key="currencyPair"
              >
                <td
                  class="bg-green-50 px-2 pb-2 pt-3"
                  v-if="isCurrencyPairSame(currencyPair)"
                ></td>

                <td v-html="getDailyChange(currencyPair)" v-else></td>
              </template>

              <td class="px-2 pb-2 pt-3">{{ getTotalDailyChange(idx) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </template>
  </div>
</template>

<style scoped>
table th,
td {
  border: 1px solid #dfdfdf;
}
</style>
