image: node:21.5.0

stages:
  - build

variables:
  SSH_PRIVATE_KEY: $SSH_PRIVATE_KEY
  SSH_KNOWN_HOSTS: $SSH_KNOWN_HOSTS

before_script:
  - "which ssh-agent || ( apt-get update -y && apt-get install -y openssh-client )"
  - eval $(ssh-agent -s)
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh

  # Set up SSH key for each environment
  - if [ "$CI_COMMIT_BRANCH" = "staging" ]; then echo "$SSH_PRIVATE_KEY_DEV" > ~/.ssh/id_rsa; fi
  - if [ "$CI_COMMIT_BRANCH" = "feat/chat" ]; then echo "$SSH_PRIVATE_KEY_DEV" > ~/.ssh/id_rsa; fi
  - if [ "$CI_COMMIT_BRANCH" = "prod" ]; then echo "$SSH_PRIVATE_KEY_PROD" > ~/.ssh/id_rsa; fi
  - chmod 600 ~/.ssh/id_rsa

  # Set up known hosts
  - echo "$SSH_KNOWN_HOSTS" > ~/.ssh/known_hosts
  - chmod 644 ~/.ssh/known_hosts
  - cat ~/.ssh/known_hosts

  # Ensure the Node version is correct
  - node -v # Check Node.js version
  - npm -v # Check npm version

# Job for the 'staging' branch
build-job-dev:
  stage: build
  only:
    - staging
  script:
    - ssh -i ~/.ssh/id_rsa root@********** -vvv '
      cd /home/<USER>/vuejs/ &&
      git checkout staging &&
      git pull origin staging &&
      npm install &&
      npm run build-no-ts-check
      '
build-job-chat:
  stage: build
  only:
    - feat/chat
  script:
    - ssh -i ~/.ssh/id_rsa root@********** -vvv '
      cd /home/<USER>/vuejs/ &&
      git checkout feat/chat &&
      git pull origin feat/chat &&
      npm install &&
      npm run build-no-ts-check
      '
