<script setup lang="ts">
import { computed, ref, watch } from "vue";

import { useBottomNavDrawerStore } from "@/store/bottomNavDrawerStore";
import { useCandleStickStore } from "@/store/candleStickStore";

import { EHistorDealType } from "@/types/enums";

import Spinner from "@/components/Spinner.vue";

import { axios } from "@/api";
import { IHistoryDeals } from "@/types";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const props = defineProps<{
  activeTab: string;
}>();

const candleStickStore = useCandleStickStore();
const bottomNavDrawerStore = useBottomNavDrawerStore();

const tableHeaders = ref([
  {
    id: "open_time",
    text: "Open Time"
  },
  {
    id: "close_time",
    text: "Close Time"
  },
  {
    id: "symbol",
    text: "Symbol"
  },
  {
    id: "ticket",
    text: "Ticket"
  },
  {
    id: "type",
    text: "Type"
  },
  {
    id: "open_volume",
    text: "Open Volume"
  },
  {
    id: "close_volume",
    text: "Close Volume"
  },
  {
    id: "open_price",
    text: "Open Price"
  },
  {
    id: "close_price",
    text: "Close Price"
  },
  {
    id: "sl",
    text: "SL"
  },
  {
    id: "tp",
    text: "TP"
  },
  {
    id: "commission",
    text: "Commission"
  },
  {
    id: "swap",
    text: "Swap"
  },
  {
    id: "profit",
    text: "Profit"
  }
]);
const sortColumn = ref("");
const sortDirection = ref("");
const historyDeals = ref<IHistoryDeals[]>([]);
const profit = ref(0);
const initialBalance = ref(0);
const currentBalance = ref(0);
const totalCommission = ref(0);
const totalSwap = ref(0);
const totalProfit = ref(0);

const loading = ref(false);

let currentPage = 1;

watch(
  () => [props.activeTab, candleStickStore.marketWatchOnlineStatus],
  ([tab, onlineStatus]) => {
    if (tab === "history-tab" && onlineStatus) {
      currentPage = 1;

      sortColumn.value = "";
      sortDirection.value = "";
      historyDeals.value = [];

      getHistoryTrade();
      getTotalValues();
    }
  },
  {
    immediate: true
  }
);

const filteredHistory = computed(() => {
  if (sortColumn.value === "") {
    return historyDeals.value;
  }

  return [...historyDeals.value].sort((a, b) => {
    const modifier = sortDirection.value === "asc" ? 1 : -1;

    // @ts-expect-error
    if (a[sortColumn.value] < b[sortColumn.value]) return -1 * modifier;

    // @ts-expect-error
    if (a[sortColumn.value] > b[sortColumn.value]) return 1 * modifier;

    return 0;
  });
});

async function getHistoryTrade() {
  try {
    const mt5Id = candleStickStore.eaAccount?.mt5_id;

    const resp = await axios.get("/tradeHistory", {
      params: {
        mt5Id,
        page: currentPage,
        limit: 50
      }
    });

    let arr = [];

    for (let i = resp.data.data.trades.length - 1; i >= 0; i--) {
      arr.push(resp.data.data.trades[i]);
    }

    historyDeals.value = [...arr, ...historyDeals.value];
  } catch (e) {
    console.error(e);
  } finally {
    loading.value = false;
  }
}

async function getTotalValues() {
  try {
    const mt5Id = candleStickStore.eaAccount?.mt5_id;

    const resp = await axios.get(`/tradeHistory/aggregate/${mt5Id}`);

    const data = resp.data.data;

    profit.value = data.profit;
    currentBalance.value = data.currentBalance;
    initialBalance.value = data.initalBalance;
    totalCommission.value = data.totalCommission;
    totalSwap.value = data.totalSwap;
    totalProfit.value = data.totalProfit;
  } catch (e) {
    console.error(e);
  }
}

function handleScrolling(e: Event) {
  const top = (e.target as HTMLElement).scrollTop;

  if (historyDeals.value.length !== 0 && top === 0) {
    loading.value = true;

    currentPage++;

    getHistoryTrade();
  }
}

function handleSorting(column: string) {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === "asc" ? "desc" : "asc";
  } else {
    sortColumn.value = column;
    sortDirection.value = "asc";
  }
}

function toggleSortIcon(column: string, direction: string) {
  return (
    `${sortColumn.value}_${sortDirection.value}` !== `${column}_${direction}`
  );
}

function handleSLColor(out_comment: string | null) {
  if (out_comment && out_comment.slice(1, 3) === "sl") {
    return "bg-red-300";
  }

  return null;
}

function handleTPColor(out_comment: string | null) {
  if (out_comment && out_comment.slice(1, 3) === "tp") {
    return "bg-green-300";
  }

  return null;
}
</script>

<template>
  <div
    class="overflow-auto p-2"
    :style="{
      height: bottomNavDrawerStore.bottomNavContentAreaHeight - 48 + 'px'
    }"
    @scroll="handleScrolling"
  >
    <div
      class="pt-5 text-center text-sm"
      v-if="!candleStickStore.marketWatchOnlineStatus"
    >
      Connect EA to see history trades.
    </div>

    <table class="w-full text-left text-[11px]" v-else>
      <thead>
        <tr>
          <th
            class="bg-gray-50"
            v-for="header in tableHeaders"
            :key="header.id"
          >
            <div
              class="flex items-center gap-x-1.5 px-2 pb-1.5 pt-2 text-xs font-semibold"
              @click="handleSorting(header.id)"
            >
              {{ header.text }}

              <FontAwesomeIcon
                icon="fa-solid fa-arrow-down"
                :class="{
                  hidden: toggleSortIcon(header.id, 'asc')
                }"
              />

              <FontAwesomeIcon
                icon="fa-solid fa-arrow-up"
                :class="{
                  hidden: toggleSortIcon(header.id, 'desc')
                }"
              />
            </div>
          </th>
        </tr>
      </thead>

      <tbody>
        <tr v-if="loading">
          <td colspan="14" class="px-2 pb-1.5 pt-2">
            <Spinner class="mx-auto !h-5 !w-5" />
          </td>
        </tr>

        <tr v-if="filteredHistory.length === 0">
          <td colspan="14" class="px-2 pb-1.5 pt-2 text-center text-xs">
            No history trade found.
          </td>
        </tr>

        <template v-else>
          <tr class="even:bg-gray-50" v-for="item in filteredHistory">
            <td class="px-2 pb-1.5 pt-2">
              {{ item.open_time }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.close_time }}
            </td>

            <td class="px-2 pb-1.5 pt-2">{{ item.symbol }}</td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.ticket ? item.ticket : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ EHistorDealType[item.type as keyof typeof EHistorDealType] }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.open_volume ? item.open_volume : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.close_volume ? item.close_volume : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.open_price ? item.open_price : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.close_price ? item.close_price : "" }}
            </td>

            <td
              class="px-2 pb-1.5 pt-2"
              :class="handleSLColor(item.out_comment)"
            >
              {{ item.sl ? item.sl : "" }}
            </td>

            <td
              class="px-2 pb-1.5 pt-2"
              :class="handleTPColor(item.out_comment)"
            >
              {{ item.tp ? item.tp : "" }}
            </td>

            <td
              class="px-2 pb-1.5 pt-2"
              :class="{
                'text-success': item.commission > 0,
                'text-danger': item.commission < 0
              }"
            >
              {{ item.commission ? item.commission : "" }}
            </td>

            <td class="px-2 pb-1.5 pt-2">
              {{ item.swap ? item.swap : "" }}
            </td>

            <td
              class="px-2 pb-1.5 pt-2"
              :class="{
                'text-success': item.profit > 0,
                'text-danger': item.profit < 0
              }"
            >
              {{ item.profit ? item.profit : "" }}
            </td>
          </tr>

          <tr class="sticky bottom-[-7px] font-bold">
            <td colspan="11" class="bg-accent px-2 pb-1.5 pt-2">
              Profit:
              {{ profit.toFixed(2) }}

              <span class="ml-2">Balance: {{ currentBalance.toFixed(2) }}</span>
            </td>

            <td class="bg-accent px-2 pb-1.5 pt-2">
              {{ totalCommission.toFixed(2) }}
            </td>

            <td class="bg-accent px-2 pb-1.5 pt-2">
              {{ totalSwap.toFixed(2) }}
            </td>

            <td class="bg-accent px-2 pb-1.5 pt-2">
              {{ totalProfit.toFixed(2) }}
            </td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>

<style scoped>
table th,
td {
  border: 1px solid #dfdfdf;
}

table th {
  position: sticky;
  top: -9px;
}
</style>
