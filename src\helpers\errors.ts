export class ChartInstanceError extends <PERSON>rror {
  constructor(message = "") {
    super(message + "; chart instance is null.");
  }
}

export class ShapeToolInstanceError extends Error {
  constructor(message = "") {
    super(message + "; shape tool instance is undefined.");
  }
}

export class ShapeInstanceError extends Error {
  constructor(message = "") {
    super(message + "; selected shape instance is undefined.");
  }
}

export class StripeError extends Error {
  constructor(message: string) {
    super(message);
  }
}
