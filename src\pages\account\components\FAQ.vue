<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useWindowSize } from "@vueuse/core";
import { initFlowbite } from "flowbite";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const { height: cardHeight } = useWindowSize();

const faqList = ref([
  {
    id: "faq_1",
    question: "How do I get started with Tradeaway?",
    answer:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt, aliquid numquam ab accusantium fugiat voluptates quo amet reprehenderit repudiandae deleniti nobis iure sint fuga architecto aut commodi eius soluta excepturi!"
  },
  {
    id: "faq_2",
    question: "My Expert Advisor is not working. How do I fix this problem?",
    answer:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt, aliquid numquam ab accusantium fugiat voluptates quo amet reprehenderit repudiandae deleniti nobis iure sint fuga architecto aut commodi eius soluta excepturi!"
  },
  {
    id: "faq_3",
    question: "How do I set up my wallet for trading?",
    answer:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt, aliquid numquam ab accusantium fugiat voluptates quo amet reprehenderit repudiandae deleniti nobis iure sint fuga architecto aut commodi eius soluta excepturi!"
  },
  {
    id: "faq_4",
    question: "What happens when my trial period expires?",
    answer:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt, aliquid numquam ab accusantium fugiat voluptates quo amet reprehenderit repudiandae deleniti nobis iure sint fuga architecto aut commodi eius soluta excepturi!"
  },
  {
    id: "faq_5",
    question: "How do I cancel my trading subscription?",
    answer:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt, aliquid numquam ab accusantium fugiat voluptates quo amet reprehenderit repudiandae deleniti nobis iure sint fuga architecto aut commodi eius soluta excepturi!"
  },
  {
    id: "faq_6",
    question: "How can I delete my trading account?",
    answer:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt, aliquid numquam ab accusantium fugiat voluptates quo amet reprehenderit repudiandae deleniti nobis iure sint fuga architecto aut commodi eius soluta excepturi!"
  },
  {
    id: "faq_7",
    question:
      "I want a specific feature to be added to the system. How can I request for it?",
    answer:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt, aliquid numquam ab accusantium fugiat voluptates quo amet reprehenderit repudiandae deleniti nobis iure sint fuga architecto aut commodi eius soluta excepturi!"
  },
  {
    id: "faq_8",
    question: "I've found a bug in the system. How can I help?",
    answer:
      "Lorem ipsum dolor sit amet consectetur adipisicing elit. Deserunt, aliquid numquam ab accusantium fugiat voluptates quo amet reprehenderit repudiandae deleniti nobis iure sint fuga architecto aut commodi eius soluta excepturi!"
  }
]);

onMounted(() => {
  initFlowbite();
});
</script>

<template>
  <div
    class="mx-auto w-10/12 overflow-auto rounded-md bg-white p-5 shadow-lg"
    :style="{ height: cardHeight - 55 - 40 + 'px' }"
  >
    <h1 class="text-center text-2xl font-bold">Help Center</h1>

    <div class="mx-auto mt-4 w-52 border-t-2 border-blue-700"></div>

    <h2 class="mt-6 text-center text-gray-600">
      If you have a question or are experiencing an issue, look around through
      our FAQ below.
    </h2>

    <div
      class="mt-6"
      id="faq-accordion"
      data-accordion="collapse"
      data-active-classes="!text-blue-700 bg-accent"
    >
      <template v-for="(faq, idx) in faqList" :key="faq.id">
        <h2 :id="`accordion-faq-heading-${idx}`">
          <button
            type="button"
            aria-expanded="false"
            class="flex w-full cursor-pointer items-center justify-between border-b px-5 pb-4 pt-5 font-medium text-gray-600 hover:bg-accent"
            :aria-controls="`accordion-faq-body-${idx}`"
            :data-accordion-target="`#accordion-faq-body-${idx}`"
          >
            <span>{{ faq.question }}</span>

            <FontAwesomeIcon icon="fa-solid fa-chevron-down" />
          </button>
        </h2>

        <div
          class="hidden"
          :id="`accordion-faq-body-${idx}`"
          :aria-labelledby="`accordion-faq-heading-${idx}`"
        >
          <div class="border-b p-5 text-sm text-gray-500">
            {{ faq.answer }}
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
