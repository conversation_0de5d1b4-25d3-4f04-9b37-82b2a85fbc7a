<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, watch } from "vue";

import { TabItem, Tabs, TabsOptions } from "flowbite";

import { handleRemoveSelectedTradeShape } from "@/chart-frontend/utils";

import { getElementWidthAndHeight } from "@/helpers/elementWidthAndHeight";

import { useBottomNavDrawerStore } from "@/store/bottomNavDrawerStore";
import { useChartStore } from "@/store/chartStore";
import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";

import Tooltip from "@/components/Tooltip.vue";

import Analytics from "./components/Analytics.vue";
import OpenTrade from "./components/OpenTrade.vue";
import TradeHistory from "./components/TradeHistory.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const chartStore = useChartStore();
const rightNavDrawerStore = useRightNavDrawerStore();
const bottomNavDrawerStore = useBottomNavDrawerStore();

const activeTab = ref("");

let bottomNavHandle: HTMLElement | null;

onMounted(() => {
  const tabsElement = document.getElementById("bottom-drawer-tabs");

  const tabElements: TabItem[] = [
    {
      id: "trade-tab",
      triggerEl: document.querySelector("#trade-tab-trigger")!,
      targetEl: document.querySelector("#trade-tab-content")!
    },
    {
      id: "analytics-tab",
      triggerEl: document.querySelector("#analytics-tab-trigger")!,
      targetEl: document.querySelector("#analytics-tab-content")!
    },
    {
      id: "history-tab",
      triggerEl: document.querySelector("#history-tab-trigger")!,
      targetEl: document.querySelector("#history-trade-tab-content")!
    }
  ];

  const options: TabsOptions = {
    activeClasses: "text-white bg-selected hover:bg-selected",
    inactiveClasses: "text-gray-500",
    onShow(_, tab) {
      activeTab.value = tab.id;
    }
  };

  new Tabs(tabsElement, tabElements, options);

  bottomNavHandle = document.getElementById("bottomNavHandle");
  bottomNavHandle?.addEventListener(
    "mousedown",
    handleBottomNavContentAreaScrolling
  );

  // I think, this is not needed here.
  handleRemoveSelectedTradeShape();

  // Is this really needed? Needs to be discussed in detail.
  window.addEventListener("beforeunload", function () {
    handleRemoveSelectedTradeShape();
  });
});

onUnmounted(() => {
  bottomNavHandle?.removeEventListener(
    "mousedown",
    handleBottomNavContentAreaScrolling
  );

  // This trade clean up function should be moved to OpenTrade.vue.
  // It should be placed inside onUnmounted lifecycle hook.
  handleRemoveSelectedTradeShape();
});

watch(
  () => chartStore.isShapeToolSelected,
  (isSelected) => {
    if (isSelected) {
      bottomNavDrawerStore.editTradeModal = false;

      if (!chartStore.shapeToolInstance) {
        throw new Error(
          "Shape tool instance not found. Cannot reset global shape tool lock."
        );
      }

      chartStore.shapeToolInstance.resetGlobalLock();
    }
  }
);

function handleBottomNavContentAreaScrolling(e: MouseEvent) {
  let startY = 0;
  let initialHeight = 0;

  startY = e.clientY;
  initialHeight = bottomNavDrawerStore.bottomNavContentAreaHeight;

  const handleMouseUp = () => {
    // If height <= 140px when mouse is released then collapse the nav drawer.
    if (bottomNavDrawerStore.bottomNavContentAreaHeight <= 140) {
      closeBottomNavContentArea();
    }

    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);

    document.body.classList.remove("select-none");
    document.body.style.cursor = "auto";
  };

  const handleMouseMove = (e: MouseEvent) => {
    document.body.classList.add("select-none");
    document.body.style.cursor = "e-resize";

    let deltaY = startY - e.clientY;
    let newHeight = initialHeight + deltaY;

    // Prevent overflow when scrolling upward and downward
    if (newHeight >= window.innerHeight - 250 || newHeight < 130) {
      return;
    }

    const appbar = getElementWidthAndHeight("app-bar");
    const chartControlsBar = getElementWidthAndHeight("chart-controls-bar");

    chartStore.chartHeight =
      window.innerHeight - appbar.height - chartControlsBar.height - newHeight;

    bottomNavDrawerStore.bottomNavContentAreaHeight = newHeight;
    rightNavDrawerStore.rightNavContentAreaHeight =
      chartStore.chartHeight + chartControlsBar.height;
  };

  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
}

async function resetBottomNavContentArea() {
  const appbar = getElementWidthAndHeight("app-bar");
  const chartControlsBar = getElementWidthAndHeight("chart-controls-bar");

  bottomNavDrawerStore.bottomNavContentAreaHeight = 296;

  const height =
    window.innerHeight -
    appbar.height -
    chartControlsBar.height -
    bottomNavDrawerStore.bottomNavContentAreaHeight;

  chartStore.chartHeight = height;

  rightNavDrawerStore.rightNavContentAreaHeight =
    height + chartControlsBar.height;
}

async function closeBottomNavContentArea() {
  bottomNavDrawerStore.toggleEditTradeModal(false);
  bottomNavDrawerStore.toggleBottomNavContentArea(false);

  await nextTick();

  const appbar = getElementWidthAndHeight("app-bar");
  const chartControlsBar = getElementWidthAndHeight("chart-controls-bar");
  const bottomNavDrawer = getElementWidthAndHeight("bottom-nav-drawer");

  const height =
    window.innerHeight -
    appbar.height -
    chartControlsBar.height -
    bottomNavDrawer.height;

  chartStore.chartHeight = height;

  rightNavDrawerStore.rightNavContentAreaHeight =
    height + chartControlsBar.height;
}
</script>

<template>
  <div id="bottom-nav-content-area">
    <div
      id="bottomNavHandle"
      class="flex cursor-n-resize justify-between border-b bg-accent px-2"
    >
      <ul
        role="tablist"
        id="bottom-drawer-tabs"
        class="flex text-sm font-medium"
      >
        <li role="presentation">
          <button
            role="tab"
            type="button"
            aria-selected="false"
            id="trade-tab-trigger"
            aria-controls="trade-tab-content"
            data-tabs-target="#open-trade"
            class="rounded-t-xl px-4 pb-3 pt-4 hover:bg-blue-100"
          >
            Trade
          </button>
        </li>

        <li role="presentation">
          <button
            role="tab"
            type="button"
            aria-selected="false"
            id="analytics-tab-trigger"
            aria-controls="analytics-tab-content"
            class="rounded-t-xl px-4 pb-3 pt-4 hover:bg-blue-100"
          >
            Analytics
          </button>
        </li>

        <li role="presentation">
          <button
            role="tab"
            type="button"
            aria-selected="false"
            id="history-tab-trigger"
            aria-controls="history-trade-tab-content"
            class="rounded-t-xl px-4 pb-3 pt-4 hover:bg-blue-100"
          >
            History
          </button>
        </li>
      </ul>

      <div
        id="bottomNavHandle"
        class="flex cursor-n-resize justify-end gap-x-1"
      >
        <div class="flex items-center justify-end">
          <Tooltip
            id="reset-trading-panel-size-tooltip"
            trigger-id="reset-trading-panel-size-trigger-tooltip"
            class="rounded px-2 py-1 hover:bg-gray-300"
            @click="resetBottomNavContentArea"
          >
            <template #trigger>
              <FontAwesomeIcon icon="fa-solid fa-minus" />
            </template>

            <template #content>Reset Panel Size</template>
          </Tooltip>
        </div>

        <div class="flex items-center justify-end">
          <Tooltip
            id="close-trading-panel-tooltip"
            trigger-id="close-trading-panel-trigger-tooltip"
            class="rounded px-2 py-1 hover:bg-gray-300"
            @click="closeBottomNavContentArea"
          >
            <template #trigger>
              <FontAwesomeIcon icon="fa-solid fa-chevron-down" />
            </template>

            <template #content>Collapse Panel</template>
          </Tooltip>
        </div>
      </div>
    </div>

    <div>
      <div
        class="hidden"
        role="tabpanel"
        id="trade-tab-content"
        aria-labelledby="trade-tab"
      >
        <OpenTrade />
      </div>

      <div
        class="hidden"
        role="tabpanel"
        id="analytics-tab-content"
        aria-labelledby="analytics-tab"
      >
        <Analytics :active-tab="activeTab" />
      </div>

      <div
        class="hidden"
        role="tabpanel"
        id="history-trade-tab-content"
        aria-labelledby="history-trade-tab"
      >
        <TradeHistory :active-tab="activeTab" />
      </div>
    </div>
  </div>
</template>
