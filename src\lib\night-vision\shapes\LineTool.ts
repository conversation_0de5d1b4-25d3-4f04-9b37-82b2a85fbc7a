import { TCoreData } from "../types";
import { BaseLine } from "./shapes/BaseLine";
import { BasicPoint } from "./base/Types";
import { DistanceUtils } from "./utils/DistanceUtils";
import { DrawUtils } from "./utils/DrawUtils";
import { IBaseShape } from "./shapes/BaseShape";
import { PreferenceUtils } from "./utils/PreferencesUtils";
import { BaseShapeInterface } from "./base/shape.types";

interface LineDetails {
  readonly details: {
    priceRange: string;
    percentChange: string;
    changeInPips: string;
    barsRange: number;
    dateTimeRange: string;
    distance: string;
    angle: string;
  };
}

export class Line
  extends BaseLine
  implements LineDetails, BaseShapeInterface<"trend-line">
{
  dragState: "tracking" | "settled" | "dragging" | "tracking-x" | "tracking-y" =
    "settled";
  draggingPoint: { x: number; y: number } | null = null;
  dragging: boolean = false;

  get details() {
    const indexBased = this.$core.layout.indexBased;
    // Greater X index
    const prec = this.$core.meta.getAutoPrec(0, 0);
    const [lX, gX] =
      this.points[0].x > this.points[1].x
        ? [this.points[1].x, this.points[0].x]
        : [this.points[0].x, this.points[1].x];
    const [lY, gY] =
      this.points[0].y > this.points[1].y
        ? [this.points[1].y, this.points[0].y]
        : [this.points[0].y, this.points[1].y];
    const [s1, s2] = [this.points[0].screen, this.points[1].screen];
    const diff = (gY - lY).toFixed(prec);
    return {
      priceRange: `[${lY.toFixed(prec)}, ${gY.toFixed(prec)}]`,
      percentChange:
        (
          ((this.points[1].y - this.points[0].y) / this.points[1].y) *
          100
        ).toFixed(2) + "%",
      changeInPips: (((gY - lY) * 10 ** prec) / 10).toFixed(2),
      diff: diff,
      // TODO: this is not working with non index based date series.. how to get index value of current point???
      // Since date series can have gaps, it's not ideal to divide the time frame by time frame between two bars
      // if we have a guarantee of no gaps, we can do that
      // or we should just use index based values (but this won't solve the same shapes between 15m chart and 30m chart 🥲)
      barsRange: Math.floor(
        indexBased
          ? gX - lX
          : gX / this.$core.layout.tStep - lX / this.$core.layout.tStep
      ),
      dateTimeRange: DistanceUtils.getTimeDifference(
        this.points[0].time,
        this.points[1].time
      ),
      distance: "distance: " + DistanceUtils.distance(s1, s2).toFixed(1) + "px",
      angle: "∠: " + DistanceUtils.angleWithXAxis(s1, s2).toFixed(2) + "°"
    };
  }

  constructor(
    $core: TCoreData,
    uuid: string,
    points: BasicPoint[],
    screenPoints: boolean = true,
    onSelect?: (line: IBaseShape) => boolean
  ) {
    const linePoints = points.length
      ? points
      : [
          { x: $core.cursor.x, y: $core.cursor.y },
          { x: $core.cursor.x, y: $core.cursor.y }
        ];
    super(
      $core,
      uuid,
      linePoints,
      points.length ? screenPoints : true,
      onSelect
    );

    this.properties = PreferenceUtils["trend-line"];

    this.type = "trend-line";
    if (!points.length)
      $core.hub.events.emit("shape-draw-start", {
        shape: this,
        points: [{ p: this.points[1], dir: null }]
      });
  }

  drawDetails(ctx: CanvasRenderingContext2D): void {
    if (!this.properties.labelProperties.show_label) return;

    const details = [];

    if (this.properties.labelProperties.show_price_range) {
      details.push(`${this.details.priceRange}`);
    }

    let txt = "";

    if (this.properties.labelProperties.show_percent_change) {
      txt += `${this.details.percentChange}`;
    }

    if (this.properties.labelProperties.show_change_in_pips) {
      if (txt !== "") {
        txt += ", ";
      }
      txt += `${this.details.changeInPips} pips`;
    }

    if (this.properties.labelProperties.show_diff) {
      if (txt !== "") {
        txt += ", ";
      }
      txt += `${this.details.diff}  `;
    }

    if (txt !== "") {
      details.push(txt);
    }

    txt = "";

    if (this.properties.labelProperties.show_bars_range) {
      txt += `${this.details.barsRange} bars`;
    }

    if (this.properties.labelProperties.show_date_time_range) {
      if (txt !== "") {
        txt += ", ";
      }
      txt += `${this.details.dateTimeRange}`;
    }

    if (txt !== "") {
      details.push(txt);
    }

    if (this.properties.labelProperties.show_distance) {
      details.push(`${this.details.distance}`);
    }

    if (this.properties.labelProperties.show_angle) {
      details.push(`${this.details.angle}`);
    }

    // Handle default properties
    this.properties.labelProperties = {
      ...PreferenceUtils[this.type].labelProperties,
      ...this.properties.labelProperties
    };

    const { labelProperties } = this.properties;

    // const labelPositionCoordinates = getLabelPosition({
    //   ctx,
    //   p1: this.screenPoints[0],
    //   p2: this.screenPoints[1],
    //   labelProperties,
    //   details
    // })

    if (details.length)
      DrawUtils.drawDetails({
        ctx,
        details,
        styles: labelProperties.labelStyle,
        labelPosition: labelProperties.label_position,
        p1: this.endPoints[0],
        p2: this.endPoints[1]
      });
  }

  protected cursorOverLine(event: MouseEvent) {
    const cursor = { x: event.offsetX, y: event.offsetY };
    return DistanceUtils.isCursorOnLine(
      this.screenPoints[0],
      this.screenPoints[1],
      cursor
    );
  }

  mousedown(event: MouseEvent): void {
    const cursor = { x: event.offsetX, y: event.offsetY };
    // this.selected = false
    if (this.cursorOverLine(event)) {
      try {
        this.selected = this.onSelect(this);
      } catch (e) {
        console.error(e);
      }
      let dragPoint = false;
      for (let i = 0; i < this.points.length; i++) {
        const p = this.points[i];
        if (DistanceUtils.isCursorOnPoint(p.screen, cursor)) {
          p.startDragging();
          dragPoint = true;
          break;
        }
      }
      if (!dragPoint && this.selected) {
        this.dragging = true;
        this.draggingPoint = { x: this.$core.cursor.x, y: this.$core.cursor.y };

        this.$core.hub.events.emit("scroll-lock", true);
      }
    } else {
      this.dragging = false;
      this.draggingPoint = null;
      // // this.selected = false
    }
    this.$core.hub.events.emit("update-layout");
  }

  mouseover(event: MouseEvent): void {
    if (this.cursorOverLine(event)) {
      this.hovered = true;
    } else {
      this.hovered = false;
    }
  }
  mousemove(event: MouseEvent): void {
    this.points.forEach((point) => point.mousemove(event));
    if (this.dragging && this.draggingPoint && this.selected) {
      const difference = {
        x: this.$core.cursor.x - this.draggingPoint.x,
        y: this.$core.cursor.y - this.draggingPoint.y
      };
      this.draggingPoint = {
        x: this.$core.cursor.x,
        y: this.$core.cursor.y
      };
      this.moveShape(difference);
    }
  }

  mouseup(event: MouseEvent): void {
    this.points.forEach((p) => {
      p.mouseup(event);
    });
    this.dragging = false;
    this.$core.hub.events.emit("scroll-lock", false);
  }

  keydown?(): void {
    // throw new Error("Method not implemented.")
  }
}
