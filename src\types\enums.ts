export enum ETradeOperationType {
  TRADE_ACTION_DEAL = "TRADE_ACTION_DEAL",
  TRADE_ACTION_PENDING = "TRADE_ACTION_PENDING",
  TRADE_ACTION_SLTP = "TRADE_ACTION_SLTP",
  TRADE_ACTION_MODIFY = "TRADE_ACTION_MODIFY",
  TRADE_ACTION_CLOSE_BY = "TRADE_ACTION_CLOSE_BY",
  TRADE_ACTION_REMOVE = "TRADE_ACTION_REMOVE",

  // Custom Enums
  TRADE_ACTION_CUSTOM_CLOSE_ALL = "TRADE_ACTION_CUSTOM_CLOSE_ALL",
  TRADE_ACTION_CUSTOM_CLOSE_BY_VOLUME = "TRADE_ACTION_CUSTOM_CLOSE_BY_VOLUME"
}

export enum EOrderType {
  ORDER_TYPE_BUY = "ORDER_TYPE_BUY",
  ORDER_TYPE_SELL = "ORDER_TYPE_SELL",
  ORDER_TYPE_BUY_LIMIT = "ORDER_TYPE_BUY_LIMIT",
  ORDER_TYPE_SELL_LIMIT = "ORDER_TYPE_SELL_LIMIT",
  ORDER_TYPE_BUY_STOP = "ORDER_TYPE_BUY_STOP",
  ORDER_TYPE_SELL_STOP = "ORDER_TYPE_SELL_STOP",
  ORDER_TYPE_BUY_STOP_LIMIT = "ORDER_TYPE_BUY_STOP_LIMIT",
  ORDER_TYPE_SELL_STOP_LIMIT = "ORDER_TYPE_SELL_STOP_LIMIT",
  ORDER_TYPE_CLOSE_BY = "ORDER_TYPE_CLOSE_BY"
}

export enum EOrderTypeTime {
  ORDER_TIME_GTC = "ORDER_TIME_GTC",
  ORDER_TIME_DAY = "ORDER_TIME_DAY",
  ORDER_TIME_SPECIFIED = "ORDER_TIME_SPECIFIED",
  ORDER_TIME_SPECIFIED_DAY = "ORDER_TIME_SPECIFIED_DAY",
  ORDER_TIME_RELATIVE = "ORDER_TIME_RELATIVE"
}

export enum EActiveOrders {
  POSITION_TYPE_BUY = "BUY",
  POSITION_TYPE_SELL = "SELL",
  POSITION_TYPE_BUY_LIMIT = "BUY_LIMIT",
  POSITION_TYPE_SELL_LIMIT = "SELL_LIMIT",
  POSITION_TYPE_BUY_STOP = "BUY_STOP",
  POSITION_TYPE_SELL_STOP = "SELL_STOP",
  POSITION_TYPE_BUY_STOP_LIMIT = "BUY_STOP_LIMIT",
  POSITION_TYPE_SELL_STOP_LIMIT = "SELL_STOP_LIMIT",
  POSITION_TYPE_CLOSE_BY = "CLOSE_BY"
}

export enum EPlacedOrders {
  ORDER_TYPE_BUY_LIMIT = "BUY_LIMIT",
  ORDER_TYPE_SELL_LIMIT = "SELL_LIMIT",
  ORDER_TYPE_BUY_STOP = "BUY_STOP",
  ORDER_TYPE_SELL_STOP = "SELL_STOP",
  ORDER_TYPE_BUY_STOP_LIMIT = "BUY_STOP_LIMIT",
  ORDER_TYPE_SELL_STOP_LIMIT = "SELL_STOP_LIMIT",
  ORDER_TYPE_CLOSE_BY = "CLOSE_BY"
}

export enum EHistorDealType {
  DEAL_TYPE_BUY = "BUY",
  DEAL_TYPE_SELL = "SELL",
  DEAL_TYPE_BALANCE = "BALANCE",
  DEAL_TYPE_CREDIT = "CREDIT",
  DEAL_TYPE_CHARGE = "CHARGE",
  DEAL_TYPE_CORRECTION = "CORRECTION",
  DEAL_TYPE_BONUS = "BONUS",
  DEAL_TYPE_COMMISSION = "COMMISSION",
  DEAL_TYPE_COMMISSION_DAILY = "COMMISSION_DAILY",
  DEAL_TYPE_COMMISSION_MONTHLY = "COMMISSION_MONTHLY",
  DEAL_TYPE_COMMISSION_AGENT_DAILY = "COMMISSION_AGENT_DAILY",
  DEAL_TYPE_COMMISSION_AGENT_MONTHLY = "COMMISSION_AGENT_MONTHLY",
  DEAL_TYPE_INTEREST = "INTEREST",
  DEAL_TYPE_BUY_CANCELED = "BUY_CANCELED",
  DEAL_TYPE_SELL_CANCELED = "SELL_CANCELED",
  DEAL_DIVIDEND = "DIVIDEND",
  DEAL_DIVIDEND_FRANKED = "DIVIDEND_FRANKED",
  DEAL_TAX = "TAX"
}

export enum SymbolCalcMode {
  SYMBOL_CALC_MODE_FOREX = "SYMBOL_CALC_MODE_FOREX",
  SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE = "SYMBOL_CALC_MODE_FOREX_NO_LEVERAGE",
  SYMBOL_CALC_MODE_CFD = "SYMBOL_CALC_MODE_CFD",
  SYMBOL_CALC_MODE_CFDINDEX = "SYMBOL_CALC_MODE_CFDINDEX",
  SYMBOL_CALC_MODE_CFDLEVERAGE = "SYMBOL_CALC_MODE_CFDLEVERAGE"
}

export const MTradeServerReturnCodes = {
  "10004": "Requote.",
  "10006": "Request rejected.",
  "10007": "Request canceled by trader.",
  "10008": "Order placed.",
  "10009": "Request completed.",
  "10010": "Only part of the request was completed.",
  "10011": "Request processing error.",
  "10012": "Request canceled by timeout.",
  "10013": "Invalid request.",
  "10014": "Invalid volume in the request.",
  "10015": "Invalid price in the request.",
  "10016": "Invalid stops in the request.",
  "10017": "Trade is disabled.",
  "10018": "Market is closed.",
  "10019": "There is not enough money to complete the request.",
  "10020": "Prices changed.",
  "10021": "There are no quotes to process the request.",
  "10022": "Invalid order expiration date in the request.",
  "10023": "Order state changed.",
  "10024": "Too frequent requests.",
  "10025": "No changes in request.",
  "10026": "Autotrading disabled by server.",
  "10027": "Autotrading disabled by client terminal.",
  "10028": "Request locked for processing.",
  "10029": "Order or position frozen.",
  "10030": "Invalid order filling type.",
  "10031": "No connection with the trade server.",
  "10032": "Operation is allowed only for live accounts.",
  "10033": "The number of pending orders has reached the limit.",
  "10034":
    "The volume of orders and positions for the symbol has reached the limit.",
  "10035": "Incorrect or prohibited order type.",
  "10036":
    "Position with the specified POSITION_IDENTIFIER has already been closed.",
  "10038": "A close volume exceeds the current position volume.",
  "10039":
    "A close order already exists for a specified position. This may happen when working in the hedging system.",
  "10040":
    "The number of open positions simultaneously present on an account can be limited by the server settings.",
  "10041":
    "The pending order activation request is rejected, the order is canceled.",
  "10042":
    "The request is rejected, because the 'Only long positions are allowed' rule is set for the symbol (POSITION_TYPE_BUY).",
  "10043":
    "The request is rejected, because the 'Only short positions are allowed' rule is set for the symbol (POSITION_TYPE_SELL).",
  "10044":
    "The request is rejected, because the 'Only position closing is allowed' rule is set for the symbol.",
  "10045":
    "The request is rejected, because 'Position closing is allowed only by FIFO rule' flag is set for the trading account (ACCOUNT_FIFO_CLOSE=true).",
  "10046":
    "The request is rejected, because the 'Opposite positions on a single symbol are disabled' rule is set for the trading account."
};

export enum EconomicCalenderImportance {
  NONE = "CALENDAR_IMPORTANCE_NONE",
  LOW = "CALENDAR_IMPORTANCE_LOW",
  MODERATE = "CALENDAR_IMPORTANCE_MODERATE",
  HIGH = "CALENDAR_IMPORTANCE_HIGH"
}

export enum EUserRole {
  ADMIN = "admin",
  CUSTOMER = "customer",
  DEVELOPER = "developer",
  DEMO = "demo"
}
