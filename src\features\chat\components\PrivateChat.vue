<script setup lang="ts">
import { nextTick,onMounted, ref, watch, computed, onBeforeUnmount } from "vue";
import { useChatResize } from "../composables/use-chat-resize";
import { initializeChatSocket, chatSocket } from "@/socketio/chat";
import { EditIcon, X } from "lucide-vue-next";
const {
  containerRef,
  isRoomsListCollapsed,
  chatAreaStyle,
  roomsAreaStyle,
  startResize
} = useChatResize();
import PrivateRoomsList from "./private/PrivateRoomsList.vue";
import ConfirmDialog from "@/components/ConfirmDialog.vue";

import { Message } from "@/types/chat";
import PrivateMessageInput from "./private/PrivateMessageInput.vue";
import PrivateMessageArea from "./private/PrivateMessageArea.vue";
import { useChatStore } from "@/features/stores/use-chat-store";
import { storeToRefs } from "pinia";
const isExpanded = ref(false);
import { axios, chatAxios } from "@/api";
import { User } from "@/types/user";
import { useUserStore } from '../../../store/userStore';
const chatStore = useChatStore();
const userStore = useUserStore();
interface message{
  _id:string,
  sender:{
    _id:string,
    user_name:string,
    profile_picture:string
  },
  receiver:{
    _id:string,
    user_name:string,
  },
   parentId: string,
   content:string,
    groupId: string,
    upvotes: number,
    downvotes: number,
    replyCount: number,
    timestamp: string,
}
interface PrivateRoom {
  _id: string;
  otherUser: {
    _id: string;
    user_name: string;
    profile_picture: string;
  };
}

// CREATE SEPARATE CURRENT ROOM FOR PRIVATE CHAT
const currentPrivateRoom = ref<PrivateRoom | null>(null);

const userPrivateRooms = ref<PrivateRoom[]>([]);
const fetchingPrivateRooms = ref(false);
const showEditDialog = ref(false);
const searchedUserResults = ref<User[]>([])
const userSearchQuery = ref('');
const ActiveUserForDM = ref<User|null>(null);

// Use shared store for activeTab and messages, but separate currentRoom
const { activeTab, messages } = storeToRefs(chatStore);

const dmError = ref("");
const dmLoading = ref(false);

const toggleEditDialog = () => {
  showEditDialog.value = !showEditDialog.value;
};

let debounceTimeout: ReturnType<typeof setTimeout> | undefined = undefined;

watch(userSearchQuery, async (newUserSearchQuery) => {
  clearTimeout(debounceTimeout);
  
  if (!newUserSearchQuery) {
    searchedUserResults.value = [];
    return;
  }
  
  debounceTimeout = setTimeout(async () => {
    try {
      const { data } = await axios.get(`/user/search?user_name=${newUserSearchQuery}`);
      console.log(data);
      searchedUserResults.value = data.data;
    } catch (error) {
      console.error('Error searching users:', error);
      searchedUserResults.value = [];
    }
  }, 500);
});

const fetchUserPrivateRooms = async () => {
  try {
    fetchingPrivateRooms.value = true;
    const {data} = await chatAxios.get("/api/private/group");
    if(import.meta.env.VITE_APP_ENV === 'development')
      console.log("Private rooms data:", data);
    
    userPrivateRooms.value = data.data;
    fetchingPrivateRooms.value = false;
    
    // Set current private room if we have rooms and are on private tab
    if(data.data.length > 0 && activeTab.value === 'private'){
      currentPrivateRoom.value = data.data[0];
      console.log("Private rooms fetched, set current private room to:", currentPrivateRoom.value);
    }
    
    // Set up socket subscriptions for each room
    userPrivateRooms.value.forEach((room) => {
      if(!chatSocket || !userStore.user) {
        return;
      }
      
      console.log(`Subscribing to room: ${room._id}`);
      chatSocket.emit("subscribe", {
        groupId: room._id,
        userId: userStore.user._id
      });
    });
    
    return data.data;
  } catch (error) {
    fetchingPrivateRooms.value = false;
    console.error("Error fetching private rooms:", error);
    return [];
  }
};

const dmExistsWithUser = (userId: string): boolean => {
  return userPrivateRooms.value.some(room => 
    room && room.otherUser && room.otherUser._id === userId
  );
};

const joinPrivateGroup = async (user: User|null) => {
  dmError.value = "";
  dmLoading.value = true;
  
  if (!user) {
    dmLoading.value = false;
    return;
  }
  
  // Check if DM already exists with this user
  if (dmExistsWithUser(user._id)) {
    const existingRoom = userPrivateRooms.value.find(room => 
      room && room.otherUser && room.otherUser._id === user._id
    );
    
    if (existingRoom) {
      currentPrivateRoom.value = existingRoom;
      await fetchMessagesForCurrentRoom(existingRoom);
    }
    
    dmLoading.value = false;
    toggleEditDialog();
    return;
  }
  
  try {
    const {data} = await chatAxios.post(`/api/private/group/${user._id}`);
    console.log(data);
    dmLoading.value = false;
    searchedUserResults.value = [];
    userSearchQuery.value = '';
    //subscribe to the new room
    if (!chatSocket || !userStore.user) {
      throw new Error("Chat socket or user not initialized");
    }
    chatSocket.emit("subscribe", {
      groupId: data.data._id,
      userId: userStore.user._id
    });
    chatSocket.on(`newMessage_${data.data._id}`, (msg) => {
      console.log(`New message received for room ${data.data._id}:`, msg);
      updateLastMessage(msg);
    });
    const rooms = await fetchUserPrivateRooms();
      
    if (rooms && rooms.length > 0) {
      // Find the newly created room or the first room if not found
      const newRoom = rooms.find(room => 
        room && room.otherUser && room.otherUser._id === user._id
      );
      
      if (newRoom) {

        currentPrivateRoom.value = newRoom;
        //
        await fetchMessagesForCurrentRoom(newRoom);
      } else {
        currentPrivateRoom.value = rooms[0];
        await fetchMessagesForCurrentRoom(rooms[0]);
      }
    }
    
    await refreshChatData();
    toggleEditDialog();
  } catch (error) {
    dmError.value = "Unable to create DM";
    if (import.meta.env.VITE_APP_ENV === 'development')
      console.error(error);
    dmLoading.value = false;
  }
};

const refreshChatData = async () => {
  try {
    await fetchUserPrivateRooms();
    
    if (currentPrivateRoom.value) {
      await fetchMessagesForCurrentRoom(currentPrivateRoom.value);
    }
    
    userSearchQuery.value = '';
    searchedUserResults.value = [];
    ActiveUserForDM.value = null;
    
    console.log("Chat data refreshed successfully");
  } catch (error) {
    console.error("Error refreshing chat data:", error);
  }
};

// WATCH THE PRIVATE ROOM INSTEAD OF SHARED ROOM
watch(currentPrivateRoom, async (newCurrentRoom) => {
  try {
    if (newCurrentRoom) {
      await fetchMessagesForCurrentRoom(newCurrentRoom);
    }
  } catch (error) {
    if(import.meta.env.VITE_APP_ENV === 'development')
      console.log(error, "error fetching currentRoom messages")
  }
});

const fetchMessagesForCurrentRoom = async(room: any) => {
  if (!room || !room._id) {
    console.error("Invalid room object:", room);
    return;
  }
  
  try {
    console.log(`Fetching messages for room ID: ${room._id}`);
    const { data } = await chatAxios.get(`/api/groups/${room._id}/messages?page=1&limit=50`);
    console.log("Fetched messages response:", data);
    
    messages.value[`${room._id}`] = data.data || [];
    
    console.log("Updated messages store:", messages.value);
  } catch (error) {
    console.error("Error fetching messages for room:", error);  
  }
};

const selectRoom = (room: any) => {
  currentPrivateRoom.value = room;
};

const handleRoomsCollapse = (collapsed: boolean) => {
  isExpanded.value = !collapsed;
};

const updateLastMessage = (d: message) => {
  if (d.groupId === currentPrivateRoom.value?._id) {
    const { sender, content, timestamp,_id,parentId } = d;
    currentMessages.value.push({ content, sender, timestamp,_id,parentId });
  }

  const roomIndex = userPrivateRooms.value.findIndex((room) => room._id === d.groupId);

  if (roomIndex === -1) {
    return;
  }

  const updatedRooms = [...userPrivateRooms.value];
  updatedRooms[roomIndex] = {
    ...updatedRooms[roomIndex],
    lastMessage: d
  };

  userPrivateRooms.value = updatedRooms;
};
const selectedUserIndex = ref(0);
const handleKeyDown = (e:KeyboardEvent)=>{
  if(e.key==='ArrowDown'){
    e.preventDefault();
    selectedUserIndex.value=(selectedUserIndex.value+1)% searchedUserResults.value.length;
    scrollToSelectedMention();
  }else if(e.key==='ArrowUp'){
    e.preventDefault(); // Prevent default scrolling
      // Move selection up (with wrap-around)
      selectedUserIndex.value = selectedUserIndex.value <= 0
        ? searchedUserResults.value.length - 1
        : selectedUserIndex.value - 1;
      scrollToSelectedMention();
  }
  else if(e.key==='Enter'){
    e.preventDefault();
    if(searchedUserResults.value[selectedUserIndex.value]){
      joinPrivateGroup(searchedUserResults.value[selectedUserIndex.value]);
    }
  }
  
};

// Watcher to clamp index if results shrink
watch(searchedUserResults, (newResults) => {
  if (selectedUserIndex.value >= newResults.length) {
    selectedUserIndex.value = newResults.length > 0 ? newResults.length - 1 : 0;
  }
});

// Improved scrollToSelectedMention
const scrollToSelectedMention = () => {
  nextTick(() => {
    setTimeout(() => {
      const mentionsList = document.querySelector('.mention-suggestion-list');
      if (!mentionsList) return;
      const selectedItem = mentionsList.querySelector(`[data-index="${selectedUserIndex.value}"]`);
      if (selectedItem) {
        (selectedItem as HTMLElement).scrollIntoView({ block: 'nearest', behavior: 'smooth' });
      }
    }, 10); // Small delay for extra smoothness
  });
};

onMounted(async () => {
  await initializeChatSocket();
  const rooms = await fetchUserPrivateRooms();
  
  if (!chatSocket) {
    throw new Error("Chat socket not initialized");
  }
    chatSocket.removeAllListeners();
chatSocket.on("onDelete", (messagePayload:any) => {
  console.log("Message deleted onDelete event received:",JSON.stringify(messagePayload));
handleMessageDeleted(messagePayload.message._id);
});
  
  if (rooms && rooms.length > 0) {
    //@ts-expect-error fix
    rooms.forEach(room => {
      console.log(`Setting up listener for room: ${room._id}`);
      
      chatSocket?.on(`newMessage_${room._id}`, (msg) => {
        console.log(`New message received for room ${room._id}:`, msg);
        updateLastMessage(msg);
      });
    });
    
    if (currentPrivateRoom.value) {
      await fetchMessagesForCurrentRoom(currentPrivateRoom.value);
    }
  }
chatSocket.on("newPrivateChat", (newRoom:PrivateRoom) => {
  // Add to list if not already present
  console.log("New private chat room received:", newRoom.otherUser.user_name);
    if (!newRoom || !newRoom._id) {
    console.error("Received invalid new private chat room:", newRoom);
    return;
  }
  if (!chatSocket) throw new Error("Chat socket not initialized");
  if (!userPrivateRooms.value.some(r => r._id === newRoom._id)) {
    userPrivateRooms.value.push(newRoom);
  }
    // Subscribe to the new room
    if (userStore.user) {
      chatSocket.emit("subscribe", {
        groupId: newRoom._id,
        userId: userStore.user._id
      });
    }

    // Set up event listener for new messages in this room
    chatSocket.on(`newMessage_${newRoom._id}`, (msg) => {
      updateLastMessage(msg);
    });
    fetchUserPrivateRooms();
  
});
});

const sendMessage = (text: string) => {
  if (!text.trim() || !chatSocket || !currentPrivateRoom.value || !userStore.user) {
    return;
  }

  const body = {
    groupId: currentPrivateRoom.value._id,
    sender: userStore.user._id,
    sender_name: userStore.user.name,
    receiver: currentPrivateRoom.value.otherUser?._id,
    receiver_name: currentPrivateRoom.value.otherUser?.user_name,
    isDirect: true,
    parentId: null,
    content: text,
    profile_picture_sender:userStore.user.profile_picture || '',
    profile_picture_receiver: currentPrivateRoom.value.otherUser?.profile_picture || '',
  };

  chatSocket.emit("sendMessage", body);
};

watch(ActiveUserForDM, (newUser) => {
  if (newUser) {
    userSearchQuery.value = newUser.name;
    searchedUserResults.value = [];
  }
});

const currentMessages = computed<Message[]>(() => {
  if (!currentPrivateRoom.value || !currentPrivateRoom.value._id) {
    return [];
  }
  
  const roomId = currentPrivateRoom.value._id;
  const roomMessages = messages.value[roomId] || [];
  return roomMessages;
});

// RESET PRIVATE ROOM WHEN SWITCHING TABS
watch(activeTab, async (newTab, oldTab) => {
  if (newTab === 'private' && oldTab !== 'private') {
    console.log("Switching to private tab - resetting current room");
    currentPrivateRoom.value = null;
    
    const rooms = await fetchUserPrivateRooms();
    if (rooms && rooms.length > 0) {
      currentPrivateRoom.value = rooms[0];
      console.log("Private tab: Set current private room to:", currentPrivateRoom.value);
      await fetchMessagesForCurrentRoom(rooms[0]);
    }
  } else if (newTab !== 'private' && oldTab === 'private') {
    // Clear private room when leaving private tab
    console.log("Leaving private tab - clearing current private room");
    currentPrivateRoom.value = null;
  }
});

// Add state for confirmation dialog
const showDeleteConfirm = ref(false);
const messageToDelete = ref<string | null>(null);

// Handle message deletion request from child components
const handleDeleteRequest = (messageId: string) => {
  console.log('Delete request received for private message:', messageId);
  messageToDelete.value = messageId;
  showDeleteConfirm.value = true;
};

// Handle confirmation
const confirmDelete = async () => {
  if (!messageToDelete.value) return;
  
  try {
    console.log('Deleting private message with ID:', messageToDelete.value);
    await chatAxios.delete(`/api/messages/${messageToDelete.value}`);
    //no need to emit api call garesi aafai backend bata onDelete emit huncha
    // chatSocket?.emit("onDelete", messageToDelete.value);
    // console.log('Private message deleted successfully on delete event emitted');
    handleMessageDeleted(messageToDelete.value);
    // Reset state
    showDeleteConfirm.value = false;
    messageToDelete.value = null;
  } catch (error) {
    console.error('Error deleting private message:', error);
    showDeleteConfirm.value = false;
    messageToDelete.value = null;
  }
};

// Handle cancellation
const cancelDelete = () => {
  showDeleteConfirm.value = false;
  messageToDelete.value = null;
};

// Fix the handleMessageDeleted function
const handleMessageDeleted = (messageId: string) => {
  console.log("Private message deleted:", messageId);
  
  // Remove from current private messages using the correct references
  if (currentPrivateRoom.value && currentPrivateRoom.value._id) {
    const roomId = currentPrivateRoom.value._id;
    if (messages.value[roomId]) {
      messages.value[roomId] = messages.value[roomId].filter(
        msg => msg._id !== messageId
      );
    }
  }
};

const searchInputRef = ref<HTMLInputElement | null>(null);
const dropdownPosition = ref({ top: 0, left: 0, width: 0 });
const showDropdown = computed(() => 
  showEditDialog.value && searchedUserResults.value.length > 0 && !ActiveUserForDM.value
);

function updateDropdownPosition() {
  if (searchInputRef.value) {
    const rect = searchInputRef.value.getBoundingClientRect();
    dropdownPosition.value = {
      top: rect.bottom + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width
    };
  }
}

function handleInputFocus() {
  updateDropdownPosition();
  window.addEventListener("resize", updateDropdownPosition);
  window.addEventListener("scroll", updateDropdownPosition, true);
}

function handleInputBlur(e: FocusEvent) {
  setTimeout(() => {
    if (document.activeElement !== searchInputRef.value) {
      // Optionally clear results here if desired
    }
  }, 150);
}

onMounted(() => {
  if(!chatSocket) throw new Error("Chat socket not initialized");
  chatSocket.on("onDelete",handleDeleteRequest)
  watch(showEditDialog, (val) => {
    if (val) {
      nextTick(updateDropdownPosition);
    }
  });
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", updateDropdownPosition);
  window.removeEventListener("scroll", updateDropdownPosition, true);
});
</script>

<template>
  <div ref="containerRef" class="flex flex-col h-full">
    <!-- Header -->
    <div class="bg-purple-50 px-4 py-3 flex items-center justify-between border-b border-purple-100">
      <h1 class="text-xl font-semibold">{{ currentPrivateRoom?.otherUser?.user_name }}</h1>
      <button @click="toggleEditDialog">
        <EditIcon class="w-6 h-6 text-purple-700 hover:text-purple-900" />
      </button>
    </div>
    
    <!-- Chat message area -->
    <div class="flex-1 min-h-0 overflow-hidden flex flex-col" :style="chatAreaStyle">
    <PrivateMessageArea 
          class="flex-1 min-h-0"
          :messages="currentMessages" 
          @message-deleted="handleMessageDeleted"
          @delete-request="handleDeleteRequest"
      />
      <PrivateMessageInput @send-message="sendMessage" />
    </div>

    <!-- Keep only ONE resize handle -->
     <div v-if="!isExpanded" class="relative group cursor-row-resize" @mousedown="startResize">
        <div class="h-1 bg-gray-300 group-hover:bg-blue-400 relative"></div>
        <div class="absolute inset-x-0 -top-2 -bottom-2 cursor-row-resize"></div>
    </div>

    <!-- Edit Dialog -->
    <div v-if="showEditDialog" class="fixed inset-0 bg-backdrop/50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden transform transition-all">
        <!-- Dialog Header -->
        <div class="bg-white text-black px-6 pt-4 flex items-center justify-between">
          <h2 class="text-lg font-semibold text-black flex items-center gap-2">
            <!-- <MessageSquarePlus class="w-5 h-5" /> -->
            New Conversation
          </h2>
          <button 
            @click="toggleEditDialog" 
            class="text-black/80 hover:text-black hover:bg-slate-50 rounded-full p-1.5 transition-colors "
          >
            <X class="w-5 h-5" />
          </button>
        </div>
        
        <!-- Dialog Content -->
        <div class="p-6">
          <div class="mb-5">
            <label class="block text-sm font-medium text-gray-700 mb-2">Find someone to chat with</label>
            <div class="relative">
              <!-- Search input with icon -->
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search class="h-5 w-5 text-gray-400" />
                </div>
                <input 
                  ref="searchInputRef"
                  @focus="handleInputFocus"
                  @blur="handleInputBlur"
                  @keydown="handleKeyDown"
                  v-model="userSearchQuery"
                  type="text"
                  placeholder="Search by username..." 
                  class="w-full pl-5 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-accent hover:bg-white transition-colors"
                />
              </div>
              
              <!-- Selected user preview -->
              <!-- <div v-if="ActiveUserForDM" class="mt-4 p-3 bg-accent rounded-lg border border-gray-200 flex items-center justify-between">
                <div class="flex items-center gap-3">
                  <div class="w-10 h-10 rounded-full bg-primary text-white flex items-center justify-center font-semibold">
                    {{ ActiveUserForDM.name.charAt(0).toUpperCase() }}
                  </div>
                  <div>
                    <div class="font-medium">{{ ActiveUserForDM.name }}</div>
                    <div class="text-sm text-gray-500">@{{ ActiveUserForDM.user_name || 'username' }}</div>
                  </div>
                </div>
                <button 
                  @click="ActiveUserForDM = null" 
                  class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-200"
                >
                  <X class="w-4 h-4" />
                </button>
              </div> -->
              
              <!-- Search results dropdown -->
              <!-- DROPDOWN REMOVED FROM HERE -->
            </div>
            
            <!-- Error message -->
            <div v-if="dmError" class="mt-3 text-sm text-danger bg-danger/10 p-2 rounded-md flex items-center gap-2">
              <AlertCircle class="w-4 h-4" />
              {{ dmError }}
            </div>
            
            <!-- Info message for existing DM -->
            <div 
              v-if="ActiveUserForDM && dmExistsWithUser(ActiveUserForDM._id)" 
              class="mt-3 text-sm text-info bg-info/10 p-3 rounded-md flex items-start gap-2"
            >
              <Info class="w-4 h-4 mt-0.5 flex-shrink-0" />
              <span>You already have a conversation with this user. We'll open the existing conversation.</span>
            </div>
          </div>
        </div>
        
        <!-- Dialog Footer -->
        <div class="bg-accent px-6 py-4 flex justify-end gap-3 border-t border-gray-200">
          <button 
            @click="toggleEditDialog"
            class="px-4 py-2.5 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Message Prompt -->
    <!-- <div v-if="isExpanded" class="flex-1 flex items-center justify-center bg-purple-50/30">
      <div class="text-center p-6">
        <p class="text-gray-500 text-sm">Select a user to start a private conversation</p>
      </div>
    </div> -->

    <!-- Rooms List Area -->
    <div 
      v-if="!isRoomsListCollapsed" 
      class="flex flex-col bg-gray-50 overflow-hidden" 
      :style="roomsAreaStyle"
    >
      <div class="flex-1 min-h-0 overflow-hidden">
        <PrivateRoomsList
          :fetchingPrivateRooms="fetchingPrivateRooms"
          :rooms="userPrivateRooms"
          :currentRoom="currentPrivateRoom"
          @select-room="selectRoom"
          @collapse-change="handleRoomsCollapse"
        /> 
      </div>
    </div>

    <!-- Place this after the dialog in the template -->
    <Teleport to="body">
      <div
        v-if="showDropdown"
        class="absolute z-50 bg-white rounded-lg border border-gray-200 shadow-lg overflow-hidden mention-suggestion-list"
        :style="{
          position: 'absolute',
          top: dropdownPosition.top + 'px',
          left: dropdownPosition.left + 'px',
          width: dropdownPosition.width + 'px',
          maxHeight: '15rem',
          overflowY: 'auto'
        }"
      >
        <div>
          <div
            v-for="(user, index) in searchedUserResults.slice(0,8)"
            :data-index="index"
            :key="user._id"
            :class="{
              'bg-gray-100': selectedUserIndex === index,
              'bg-white': selectedUserIndex !== index
            }"
            @click="joinPrivateGroup(user)"
            class="px-4 py-3 hover:bg-accent transition-colors cursor-pointer flex items-center justify-between"
          >
            <div class="flex items-center gap-3">
              <div v-if="user.profile_picture" class="w-8 h-8 rounded-full overflow-hidden">
                <img :src="user.profile_picture" alt="profile" class="w-full h-full object-cover"/>
              </div>
              <div v-else class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center font-semibold">
                {{ user.name.charAt(0).toUpperCase() }}
              </div>
              <div>
                <div class="font-medium">{{ user.name }}</div>
                <div class="text-xs text-gray-500">@{{ user.user_name || 'username' }}</div>
              </div>
            </div>
            <span
              v-if="dmExistsWithUser(user._id)"
              class="text-xs bg-success/10 text-success px-2 py-1 rounded-full font-medium"
            >
              Existing
            </span>
          </div>
        </div>
        <div v-if="searchedUserResults.length === 0 && userSearchQuery" class="p-4 text-center text-gray-500">
          No users found matching your search
        </div>
      </div>
    </Teleport>

    <!-- Confirmation Dialog -->
    <ConfirmDialog
      :isOpen="showDeleteConfirm"
      title="Delete Message"
      message="Are you sure you want to delete this message? This action cannot be undone."
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<style scoped>
/* Additional styles for resize functionality */
.resize-active {
  user-select: none;
}

/* Make the drag handle more visible on hover */
.group:hover .h-2 {
  height: 4px;
  transition: height 0.2s ease;
}
</style>
