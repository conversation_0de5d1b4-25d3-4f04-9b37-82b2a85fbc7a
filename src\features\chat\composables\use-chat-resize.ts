import { computed, onUnmounted, ref } from "vue";

export function useChatResize() {
  const chatHeight = ref(60); // Initial height percentage for chat area
  const isDragging = ref(false);
  const containerRef = ref<HTMLElement>();
  const isRoomsListCollapsed = ref(false);

  const chatAreaStyle = computed(() => ({
    height: `${chatHeight.value}%`
  }));

  const roomsAreaStyle = computed(() => {
    if (isRoomsListCollapsed.value) {
      return { height: "auto", minHeight: "40px", maxHeight: "40px" };
    } 

    return {
      height: `${100 - chatHeight.value}%`
    };
  });

  const stopResize = () => {
    isDragging.value = false;

    document.removeEventListener("mousemove", handleResize);
    document.removeEventListener("mouseup", stopResize);

    document.body.style.cursor = "";
    document.body.style.userSelect = "";
  };

  const handleResize = (e: MouseEvent) => {
    if (!isDragging.value || !containerRef.value) return;

    const containerRect = containerRef.value.getBoundingClientRect();
    const containerHeight = containerRect.height;
    const mouseY = e.clientY - containerRect.top;

    // Calculate the percentage directly - dragging down increases chat area percentage
    // This is more intuitive: drag down = more chat area, less rooms list
    const newChatPercentage = Math.max(
      20,
      Math.min(94, (mouseY / containerHeight) * 100)
    );
    
    // Set th chat height directly (not inverted)
    chatHeight.value = newChatPercentage;
  };

  const startResize = (e: MouseEvent) => {
    isDragging.value = true;
    e.preventDefault();

    document.addEventListener("mousemove", handleResize);
    document.addEventListener("mouseup", stopResize);

    document.body.style.cursor = "row-resize";
    document.body.style.userSelect = "none";
  };

  onUnmounted(() => {
    document.removeEventListener("mousemove", handleResize);
    document.removeEventListener("mouseup", stopResize);
    document.body.style.cursor = "";
    document.body.style.userSelect = "";
  });

  return {
    chatHeight,
    isDragging,
    containerRef,
    isRoomsListCollapsed,
    chatAreaStyle,
    roomsAreaStyle,
    startResize,
    stopResize,
    handleResize
  };
}
