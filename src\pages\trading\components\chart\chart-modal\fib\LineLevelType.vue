<script setup lang="ts">
import { ref } from "vue";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { useChartStore } from "@/store/chartStore";

import Dropdown from "@/components/Dropdown.vue";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"fib-level"> = chartStore.selectedShape!;
const lineProperties = selectedShape.properties.lineProperties;

const lineTypeDropdown = ref(false);
const lineTypes = ref([
  {
    type: "solid",
    name: "Normal"
  },
  {
    type: "dashed",
    name: "Dash<PERSON>"
  },
  {
    type: "dotted",
    name: "Dot<PERSON>"
  }
]);
const selectedLineType = ref(lineProperties.line_type);

type TLineType = typeof lineProperties.line_type;

function handleLineType(lineType: TLineType) {
  selectedLineType.value = lineType;

  selectedShape.setProperty("levelProperties", "level_line_type", lineType);

  chartStore.chart?.update();
}
</script>

<template>
  <Dropdown
    id="chart-modal-line-type-dropdown"
    toggle-id="chart-modal-line-type-toggle-dropdown"
    class="border px-2.5"
    :class="{ 'border-info bg-accent': lineTypeDropdown }"
    :icon="false"
    :offset-skidding="52"
    @show="lineTypeDropdown = true"
    @hide="lineTypeDropdown = false"
  >
    <template #text>
      <div
        class="w-5 border-b-2 border-black"
        :style="{
          borderStyle: selectedLineType
        }"
      ></div>
    </template>

    <template #content="{ close }">
      <div class="my-1 w-36">
        <div
          class="flex items-center gap-x-3 px-3 pb-1.5 pt-2 hover:bg-accent"
          :class="{
            'bg-selected text-white hover:bg-selected':
              selectedLineType === type
          }"
          v-for="{ type, name } in lineTypes"
          :key="type"
          @click="(close(), handleLineType(type as TLineType))"
        >
          <div
            class="w-5 border-b-2 border-black"
            :class="{
              '!border-white': selectedLineType === type
            }"
            :style="{ borderStyle: type }"
          ></div>
          {{ name }} Line
        </div>
      </div>
    </template>
  </Dropdown>
</template>
