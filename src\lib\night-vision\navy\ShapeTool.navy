// Navy ~ 0.2-lite
// <ds>Drawing shapes</ds>

[OVERLAY name=LineTool, ctx=Canvas, version=1.0.0]
let shapeToolInstance;
init() {
    const lib = $core.props.config.lib;
    if(lib)
        {
            shapeToolInstance = new lib.ShapeTool(env);
        }
}
destroy(){
    shapeToolInstance = undefined
}
propagate(event, data){
    if(shapeToolInstance && shapeToolInstance[event]){
        if(Array.isArray(data)){
            shapeToolInstance[event](...data);
        }
        else{
            shapeToolInstance[event](data);
        }
    }
}
draw(...data) {
    propagate('draw',data);
}
drawBotbar(...data) {
    propagate('drawBotbar',data);
}
drawSidebar(...data) {
    propagate('drawSidebar',data);
}
keydown(...data){
    propagate('keydown',data);
}
keypress(...data){
    propagate('keypress',data);
}
keyup(...data){
    propagate('keyup',data);
}
mousedown(...data){
    propagate('mousedown',data);
}
mouseup(...data){
    propagate('mouseup',data)
}
mousemove(...data){
    propagate('mousemove',data)
}
mouseout(...data){
    propagate('mouseout',data)
}
mouseover(...data){
    propagate('mouseover',data)
}
click(...data){
    propagate('click',data)
}
legend(...data){
    propagate('legend',data)
}
meta(...data){
    propagate('meta',data)
}
dataFormat(...data){
    propagate('dataFormat',data)
}
yRange(...data){
    propagate('yRange',data)
}
preSampler(...data){
    propagate('preSampler',data)
}
legendHtml(...data){
    propagate('legendHtml',data)
}
valueTracker(...data){
    return {}
}
ohlc(...data){
    propagate('ohlc',data)
}

