import { isAxiosError } from "axios";
import { ValidationError } from "yup";

/**
 * Get errors from server if it exist.
 */
export function getServerErrors(e: any) {
  if (!isAxiosError(e)) {
    return [];
  }

  const { status, response } = e;

  if (import.meta.env.DEV) {
    console.error(e);
  }

  const errors: string[] = [];

  // Form Validation Errors
  if (status === 422) {
    // @ts-ignore
    for (const [_, value] of Object.entries(response?.data?.details)) {
      // @ts-ignore
      errors.push(value?.constraints[0]);
    }

    return errors;
  }

  // API Rate Limiting
  if (status === 429) {
    errors.push(response?.data);
    return errors;
  }

  // @ts-ignore
  errors.push(response?.data?.message);

  return errors;
}

/**
 * Get client-side validation errors if it exist.
 */
export function getClientValidationErrors(e: any) {
  if (!(e instanceof ValidationError)) {
    return [];
  }

  return e.inner.reduce((acc, error) => {
    // @ts-ignore
    acc[error.path] = error.message;
    return acc;
  }, {}) as any;
}
