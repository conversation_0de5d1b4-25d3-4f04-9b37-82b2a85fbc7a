import { ref } from "vue";

import { defineStore } from "pinia";

import { EventNews } from "@/types";

type RightNavContentAreaTab =
  | "market-watch-tab"
  | "events-and-notifications-tab"
  | "chat-tab";

export const useRightNavDrawerStore = defineStore("right-nav-drawer", () => {
  const rightNavContentArea = ref(false);
  const rightNavContentAreaWidth = ref(400);
  const rightNavContentAreaHeight = ref(400);
  const rightNavContentAreaTab =
    ref<RightNavContentAreaTab>("market-watch-tab");
  const economicCalendar = ref(false);
  const heatmap = ref(false);
  const newsList = ref<EventNews[]>([]);
  const announcementList = ref<EventNews[]>([]);
  const eventAlertStatus = ref(false);

  function toggleRightNavContentArea(toggle: boolean) {
    rightNavContentArea.value = toggle;
  }

  function toggleHeatMap(toggle: boolean) {
    heatmap.value = toggle;
  }

  function toggleEconomicCalendar(toggle: boolean) {
    economicCalendar.value = toggle;
  }

  function handleRightNavContentArea() {
    if (!rightNavContentArea.value) {
      toggleRightNavContentArea(true);
      return;
    }

    if (rightNavContentArea.value) {
      toggleRightNavContentArea(false);
      return;
    }
  }

  function openEventsAndNotifications() {
    if (rightNavContentArea.value) {
      if (rightNavContentAreaTab.value === "events-and-notifications-tab") {
        handleRightNavContentArea();
      } else {
        rightNavContentAreaTab.value = "events-and-notifications-tab";
      }

      eventAlertStatus.value = false;

      return;
    }

    rightNavContentAreaTab.value = "events-and-notifications-tab";

    eventAlertStatus.value = false;

    handleRightNavContentArea();
  }

  function openChat() {
    if (rightNavContentArea.value) {
      if (rightNavContentAreaTab.value === "chat-tab") {
        handleRightNavContentArea();
      } else {
        rightNavContentAreaTab.value = "chat-tab";
      }

      eventAlertStatus.value = false;

      return;
    }

    rightNavContentAreaTab.value = "chat-tab";

    eventAlertStatus.value = false;

    handleRightNavContentArea();
  }

  function openMarketWatch() {
    if (rightNavContentArea.value) {
      if (rightNavContentAreaTab.value === "market-watch-tab") {
        handleRightNavContentArea();
      } else {
        rightNavContentAreaTab.value = "market-watch-tab";
      }

      return;
    }

    rightNavContentAreaTab.value = "market-watch-tab";

    handleRightNavContentArea();
  }

  return {
    rightNavContentArea,
    rightNavContentAreaWidth,
    rightNavContentAreaHeight,
    rightNavContentAreaTab,
    heatmap,
    economicCalendar,
    newsList,
    announcementList,
    eventAlertStatus,
    openChat,
    openEventsAndNotifications,
    openMarketWatch,
    toggleRightNavContentArea,
    toggleEconomicCalendar,
    toggleHeatMap
  };
});
