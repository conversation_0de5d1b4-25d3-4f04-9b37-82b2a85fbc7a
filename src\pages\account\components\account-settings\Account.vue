<!-- <script setup lang="ts">
import { ref } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

const navLinks = ref([
  {
    route: "edit",
    text: "Account Information",
    description:
      "View your basic information such as name, email, gender, location, etc.",
    icon: "fa-solid fa-user"
  },
  {
    route: "change-password",
    text: "Change Password",
    description: "Change your password at any time.",
    icon: "fa-solid fa-key"
  }
]);
</script>

<template>
  <div class="mb-1 px-5 text-lg font-bold">Your Account</div>

  <router-link
    :to="{ name: link.route }"
    v-for="link in navLinks"
    :key="link.route"
  >
    <div
      class="flex items-center justify-between px-5 pb-3 pt-2.5 hover:bg-accent"
    >
      <div class="flex items-center gap-x-4">
        <FontAwesomeIcon :icon="link.icon" />

        <div>
          <div class="font-medium">
            {{ link.text }}
          </div>

          <div class="text-xs text-gray-500">
            {{ link.description }}
          </div>
        </div>
      </div>

      <FontAwesomeIcon size="sm" icon="fa-solid fa-chevron-right" />
    </div>
  </router-link>
</template> -->
<script setup lang="ts">
// import Avatar from "vue3-avatar";
import {  ref } from "vue";
import edit from "@/assets/svg/edit.svg"
import switchON from "@/assets/svg/switch-on.svg"
import switchOFF from "@/assets/svg/switch-off.svg"

const editProfileActive = ref(false);
const changePasswordActive = ref(false);
console.log(editProfileActive.value);
import { useUserStore } from "@/store/userStore";
import PrimaryButton from "@/components/PrimaryButton.vue";
import {axios} from "@/api";
import { toast } from "vue3-toastify";
const userStore = useUserStore();
const toggleEditProfile = () => {
  editProfileActive.value = !editProfileActive.value;
  if (editProfileActive.value) {
    changePasswordActive.value = false; // Ensure password form is closed
  }
};
const toggleChangePassword = () => {
  changePasswordActive.value = !changePasswordActive.value;
  if (changePasswordActive.value) {
    editProfileActive.value = false; // Ensure edit profile form is closed
  }
};
const changePasswordForm = ref({
  old_password: "",
  new_password: "",
  confirm_password: "",
})
const privacy = ref({
  analytics: false,
  openOrders: false,
  history: false,
})

// Fetch user preferences on component mount
const fetchUserPreferences = async () => {
  try {
    
    const response = await axios.get(`/user/${userStore?.user?._id}`);
    const userPreferences = response.data.data.preferences;
    
    privacy.value = {
      analytics: userPreferences.analytics ?? false,
      openOrders: userPreferences.openOrders ?? false,
      history: userPreferences.history ?? false,
    };
  } catch (error) {
    console.error('Error fetching user preferences:', error);
  }
}

// Call fetchUserPreferences when component mounts
fetchUserPreferences();
const err = ref("")
const uploadError = ref("");
const isUploading = ref(false);
const selectedFile = ref<File | null>(null);
const previewUrl = ref<string | null>(null);

const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    selectedFile.value = input.files[0];
    
    // Create preview URL
    previewUrl.value = URL.createObjectURL(input.files[0]);
  }
};

const uploadProfilePicture = async () => {
  if (!selectedFile.value) {
    uploadError.value = "Please select a file first";
    return;
  }

  try {
    isUploading.value = true;
    uploadError.value = "";

    const formData = new FormData();
    formData.append('profile_picture', selectedFile.value);

    const response = await axios.post('/user/update-profile-picture', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log('Profile picture update response:', response.data);

    // Refresh user data to ensure profile picture is persisted
    const userResponse = await axios.get('/user');
    userStore.user = userResponse.data.data;

    // Close the edit form after successful upload
    editProfileActive.value = false;
    selectedFile.value = null;
    previewUrl.value = null;

  } catch (error: any) {
    console.log(error);
    uploadError.value = error.response?.data?.message || "Failed to upload profile picture";
  } finally {
    isUploading.value = false;
  }
};

const submitChangePassword = async () => {
  if(changePasswordForm.value.old_password === ""){
    err.value="Old password is required"
    // alert('Old password is required');
  }
  else if(changePasswordForm.value.new_password !== changePasswordForm.value.confirm_password){
    err.value="Passwords donot match"
    // alert('New password and Confirm password must match');
  }else if(changePasswordForm.value.new_password === ""){
    err.value="New password is required"
  }else{
   
    await axios.post(`/user/reset-password`,{
      token:userStore?.userAccessToken,
      newPassword:changePasswordForm.value.new_password,
      
    })
    .then((res:any)=>{
      console.log(res.data.message)
    }
    )

  }
  
    return;
  }
  const updatePrivacySettings = async () => {
    try {
      await axios.put(`/user/update/${userStore.user?._id}`,{
  "preferences": [
    {
      "name": "analytics",
      "enabled": privacy.value.analytics
    },
    {
      "name": "history",
      "enabled": privacy.value.history
    },
    {
      "name": "opentrade",
      "enabled": privacy.value.openOrders
    }
  ]
})
      .then((res:any)=>{
        toast.success(res.data.message)
      }
      )
    } catch (error: any) {
      console.log(error);
    }
  }
  console.log(userStore?.user)
</script>
<template>
  <div class="max-w-4xl mx-auto p-6">
    <div class="bg-white rounded-lg shadow-md p-6">
      <!-- Header Section -->
      <div class="flex items-center justify-between mb-8">
        <h1 class="text-2xl font-bold text-gray-800">Account Settings</h1>
      </div>

      <!-- Profile Section -->
      <div class="bg-gray-50 rounded-lg p-6 mb-6">
        <div class="flex items-center gap-6">
          <!-- Avatar -->
          <div class="relative">
            <div 
              class="w-20 h-20 rounded-full bg-primary text-white flex items-center justify-center text-2xl font-semibold overflow-hidden"
            >
              <img 
                v-if="previewUrl" 
                :src="previewUrl" 
                class="w-full h-full object-cover"
                alt="Profile preview"
              />

              <template v-else>
                <!-- if user has profile pic display it as avatar else display first name and last name -->
                <img 
                  v-if="userStore.user?.profile_picture" 
                  :src="userStore.user.profile_picture" 
                  class="w-full h-full object-cover rounded-full"
                  alt="Profile picture"
                />
                <template v-else>
                  {{ userStore.user?.name.split(" ")[0].slice(0,1) }}{{ userStore.user?.name.split(" ")[1].slice(0,1).toUpperCase() }}
                </template>
              </template>
            </div>
            <button 
              @click="toggleEditProfile"
              class="absolute -bottom-1 -right-1 bg-white rounded-full p-2 shadow-md hover:bg-gray-100 transition-colors"
            >
              <edit width="16" height="16" class="text-gray-600" />
            </button>
          </div>

          <!-- User Info -->
          <div class="flex-1">
            <h2 class="text-xl font-semibold text-gray-800 mb-1">{{ userStore.user?.name }}</h2>
            <p class="text-gray-600 mb-3">{{ userStore.user?.email }}</p>
            <PrimaryButton 
              @click="toggleChangePassword"
              class="bg-gray-100 text-gray-700 hover:bg-gray-200"
            >
              Change Password
            </PrimaryButton>
          </div>
        </div>

        <!-- Edit Profile Form -->
        <div 
          v-if="editProfileActive"
          class="mt-6 p-4 bg-white rounded-lg border border-gray-200"
        >
          <h3 class="text-lg font-semibold mb-4">Update Profile Picture</h3>
          <div class="space-y-4">
            <div class="flex items-center gap-4">
              <input 
                type="file" 
                accept="image/*"
                @change="handleFileSelect"
                class="block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-full file:border-0
                  file:text-sm file:font-semibold
                  file:bg-primary file:text-white
                  hover:file:bg-primary/90"
              />
            </div>

            <p v-if="uploadError" class="text-red-600 text-sm">{{ uploadError }}</p>

            <div class="flex justify-end gap-3">
              <PrimaryButton 
                type="button"
                @click="()=>{toggleEditProfile();selectedFile=null;previewUrl=null;}"
                class="bg-gray-100 text-gray-700 hover:bg-gray-200"
              >
                Cancel
              </PrimaryButton>
              <PrimaryButton 
                type="button"
                @click="uploadProfilePicture"
                :disabled="isUploading || !selectedFile"
                :loading="isUploading"
              >
                {{ isUploading ? 'Uploading...' : 'Upload Picture' }}
              </PrimaryButton>
            </div>
          </div>
        </div>

        <!-- Change Password Form -->
        <div 
          v-if="changePasswordActive"
          class="mt-6 p-4 bg-white rounded-lg border border-gray-200"
        >
          <h3 class="text-lg font-semibold mb-4">Reset Account Password</h3>
          <p v-if="err" class="text-red-600 mb-4">{{ err }}</p>
          
          <form @submit.prevent="submitChangePassword" class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Old Password</label>
              <input 
                type="password" 
                v-model="changePasswordForm.old_password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
              <input 
                type="password" 
                v-model="changePasswordForm.new_password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
              <input 
                type="password" 
                v-model="changePasswordForm.confirm_password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            <div class="flex justify-end gap-3">
              <PrimaryButton 
                type="button"
                @click="toggleChangePassword"
                class="bg-gray-100 text-gray-700 hover:bg-gray-200"
              >
                Cancel
              </PrimaryButton>
              <PrimaryButton type="submit">
                Save Changes
              </PrimaryButton>
            </div>
          </form>
        </div>
      </div>
      <div class="bg-white rounded-lg shadow-md p-6 mt-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">Privacy Settings</h2>
        
        <div class="space-y-4">
          <!-- Analytics Setting -->
          <div>

          
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
            <div>
              <h3 class="font-medium text-gray-800">Analytics</h3>
              <p class="text-sm text-gray-500">Allow us to collect usage data to improve your experience</p>
            </div>
            <div class="cursor-pointer " @click="updatePrivacySettings">
              <switchOFF 
                @click="privacy.analytics = !privacy.analytics" 
                v-if="privacy.analytics" 
                width="40" 
                height="40" 
                class="text-gray-600 transition-all duration-300" 
              />
              <switchON 
                @click="privacy.analytics = !privacy.analytics" 
                v-if="!privacy.analytics" 
                width="40" 
                height="40" 
                class="text-gray-600 transition-all duration-300" 
              />
            </div>
          </div>
         
        </div>

          <!-- History Setting -->
          <div  class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
            <div>
              <h3 class="font-medium text-gray-800">History</h3>
              <p class="text-sm text-gray-500">Save your browsing and trading history</p>
            </div>
            <div  class="cursor-pointer" @click="updatePrivacySettings">
              <switchOFF
                @click="privacy.history = !privacy.history" 
                v-if="privacy.history" 
                width="40" 
                height="40" 
                class="text-gray-600 transition-all duration-300" 
              />
              <switchON
                @click="privacy.history = !privacy.history" 
                v-if="!privacy.history" 
                width="40" 
                height="40" 
                class="text-gray-600 transition-all duration-300" 
              />
            </div>
          </div>

          <!-- Open Orders Setting -->
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
            <div>
              <h3 class="font-medium text-gray-800">Open Orders</h3>
              <p class="text-sm text-gray-500">Display your active trading orders to other users</p>
            </div>
            <div class="cursor-pointer" @click="updatePrivacySettings">
              <switchOFF
                @click="privacy.openOrders = !privacy.openOrders" 
                v-if="privacy.openOrders" 
                width="40" 
                height="40" 
                class="text-gray-600 transition-all duration-300" 
              />
              <switchON
                @click="privacy.openOrders = !privacy.openOrders" 
                v-if="!privacy.openOrders" 
                width="40" 
                height="40" 
                class="text-gray-600 transition-all duration-300" 
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>