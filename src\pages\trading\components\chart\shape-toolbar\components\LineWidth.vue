<script setup lang="ts">
import { onMounted, ref } from "vue";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";
import { useChartStore } from "@/store/chartStore";

import DropdownTooltip from "@/components/DropdownTooltip.vue";

import MinusSVG from "@/assets/svg/minus.svg";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"trend-line"> | undefined =
  chartStore.selectedShape;

const lineWidthDropdown = ref(false);
const selectedLineWidth = ref(2);

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set shape line width");
  }

  selectedLineWidth.value = selectedShape.properties.lineProperties.line_width;
});

function handleLineWidth(lineWidth: number) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to change shape line width");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to change shape line width");
  }

  selectedLineWidth.value = lineWidth;

  selectedShape.setProperty("lineProperties", "line_width", lineWidth);

  chartStore.chart.update();
}
</script>

<template>
  <DropdownTooltip
    dropdown-id="shape-toolbar-line-width-dropdown"
    dropdown-toggle-id="shape-toolbar-line-width-toggle-dropdown"
    tooltip-id="shape-toolbar-line-width-tooltip"
    tooltip-trigger-id="shape-toolbar-line-width-trigger-tooltip"
    class="flex h-[40px] items-center gap-x-1 rounded pl-1 pr-2 text-sm hover:bg-accent"
    :class="{ 'bg-accent': lineWidthDropdown }"
    :dropdown-offset-distance="2"
    @show="lineWidthDropdown = true"
    @hide="lineWidthDropdown = false"
  >
    <template #text>
      <MinusSVG />
      {{ selectedLineWidth }}px
    </template>

    <template #dropdown-content="{ close }">
      <div class="my-1 w-[60px] cursor-default">
        <div
          class="px-3 py-1.5 hover:bg-accent"
          :class="{
            'bg-selected text-white hover:bg-selected': i === selectedLineWidth
          }"
          v-for="i in 4"
          @click="(close(), handleLineWidth(i))"
        >
          {{ i }}px
        </div>
      </div>
    </template>

    <template #tooltip-content>Width</template>
  </DropdownTooltip>
</template>
