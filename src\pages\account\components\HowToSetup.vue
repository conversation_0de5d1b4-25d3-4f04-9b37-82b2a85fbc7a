<script setup lang="ts">
import { onMounted, ref } from "vue";

import { axios } from "@/api";
import { useWindowSize } from "@vueuse/core";

const { height: cardHeight } = useWindowSize();

const fileName = ref("");
const downloadUrl = ref("");

const domainName =
  import.meta.env.VITE_APP_ENV === "production"
    ? "https://tradeawaay.com"
    : "https://demo.citaal.com";

onMounted(() => {
  axios.get("/file/ea").then((resp) => {
    fileName.value = resp.data.file.originalName;

    downloadUrl.value = `${domainName}/api/file/ea/download/${fileName.value}`;
  });
});

async function downloadEA() {
  try {
    const link = document.createElement("a");

    link.href = `${domainName}/api/file/ea/download/${fileName.value}`;

    link.setAttribute("download", fileName.value);

    document.body.appendChild(link);

    link.click();

    document.body.removeChild(link);
  } catch (e) {
    console.error(e);
  }
}
</script>

<template>
  <div
    class="mx-auto w-10/12 overflow-auto rounded border bg-white p-5 shadow-lg"
    :style="{ height: cardHeight - 55 - 40 + 'px' }"
  >
    <h1 class="text-center text-2xl font-bold">How to Setup</h1>

    <div class="mx-auto mt-4 w-52 border-t-2 border-blue-700"></div>

    <div class="mt-8 px-5">
      <div class="flex gap-x-5">
        <span>1.</span>

        <p>
          Install <span class="font-semibold">MetaTrader5</span> from the
          installation file available in the broker website.
        </p>
      </div>

      <div class="mt-4 flex gap-x-5">
        <span>2.</span>

        <div>
          <p>
            After installation, login into MetaTrader5 using your broker
            credentials.
          </p>
        </div>
      </div>

      <div class="mt-4 flex gap-x-5">
        <span>3.</span>

        <div>
          Now, go to <span class="font-semibold">Expert Advisors</span> tab and
          do the following:

          <ul class="list-disc px-5">
            <li>
              Check
              <span class="font-semibold">Allow algorithmic trading</span>.
            </li>
            <li>
              Uncheck
              <span class="font-semibold">Disable algorithmic trading</span>
              when the account has been changed
            </li>
            <li>
              Uncheck
              <span class="font-semibold">Disable algorithmic trading</span>
              when the profile has been changed.
            </li>
            <li>
              Check
              <span class="font-semibold">Allow WebRequest for listed url</span>
              and add these two urls: <br />
              <span class="font-semibold">
                http://trademarket.tradeawaay.com
              </span>
              and

              <span class="font-semibold">https://tradeawaay.com</span>
            </li>
          </ul>
        </div>
      </div>

      <div class="mt-4 flex gap-x-5">
        <div>4.</div>

        <div>
          <div>Download the latest EA version using the below link.</div>

          <p class="mt-1">
            <span
              class="cursor-pointer text-primary underline"
              @click="downloadEA"
            >
              {{ fileName }}
            </span>
          </p>
        </div>
      </div>

      <div class="mt-4 flex gap-x-5">
        <div>5.</div>

        <p>
          Go to <span class="font-semibold">MetaQuotes Language Editor</span> in
          MetaTrader5.
        </p>
      </div>

      <div class="mt-4 flex gap-x-5">
        <div>6.</div>

        <p>
          In the Navigator section, right-click on
          <span class="font-semibold">Experts</span> and click Open Folder.
        </p>
      </div>

      <div class="mt-4 flex gap-x-5">
        <div>7.</div>

        <p>
          Copy and paste the
          <span class="font-semibold">EA</span> file you have just downloaded
          into Experts folder.
        </p>
      </div>

      <div class="mt-4 flex gap-x-5">
        <div>8.</div>

        <p>
          Close the MetaEditor window and go back to MetaTrader5 application. In
          the Navigator window, right-click and click Refresh. The Expert
          Advisor file that you just added will show up here.
        </p>
      </div>

      <div class="mt-4 flex gap-x-5">
        <div>9.</div>

        <p>
          Now, open up any chart (e.g. BTCUSD) and drag and drop the EA onto the
          chart. You will prompted with username and password. Use your
          Tradeawaay's username and password to log in. You can also save your
          username and password for future log in. Once logged in, you are all
          set to go.
        </p>
      </div>

      <div class="mt-4 flex gap-x-5">
        <div>10.</div>

        <p>
          To check if you have successfully logged in or not, go to the
          <span class="font-semibold">Experts</span>
          located in the bottom panel and look for the message "Authentication
          successful".
        </p>
      </div>
    </div>
  </div>
</template>
