<script setup lang="ts">
import { ref } from "vue";

import Alert from "@/components/Alert.vue";
import PrimaryButton from "@/components/PrimaryButton.vue";

const emit = defineEmits<{
  close: [logout: boolean];
}>();

const countdown = ref(60);

const timer = setInterval(() => {
  countdown.value -= 1;

  if (countdown.value === 0) {
    closeModal(true);
  }
}, 1000);

function closeModal(logout: boolean) {
  clearInterval(timer);

  emit("close", logout);
}
</script>

<template>
  <div
    class="absolute left-0 top-0 z-10 h-full w-full bg-backdrop opacity-30"
  ></div>

  <div
    class="absolute left-1/2 top-1/2 z-20 -translate-x-1/2 -translate-y-1/2 rounded-md border bg-white p-6 shadow-lg"
  >
    <Alert variant="info">
      <div>You've been inactive a long time.</div>

      <div class="text-center">Are you still there?</div>

      <div class="mt-3 text-center text-sm">
        Auto Log Out (In {{ countdown }})
      </div>
    </Alert>

    <div class="mt-3">
      <PrimaryButton class="w-full" @click="closeModal(false)">
        Yes, I'm here.
      </PrimaryButton>
    </div>
  </div>
</template>
