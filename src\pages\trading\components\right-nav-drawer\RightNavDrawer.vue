<script setup lang="ts">
import { MessageCircle } from "lucide-vue-next";

import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";

import Tooltip from "@/components/Tooltip.vue";

import BellOutlineSVG from "@/assets/svg/bell-outline.svg";
import CalenderMonthOutlineSVG from "@/assets/svg/calendar-month-outline.svg";
import FireSVG from "@/assets/svg/fire.svg";
import ViewListOutlineSVG from "@/assets/svg/view-list-outline.svg";

import EconomicCalendar from "./components/EconomicCalendar.vue";
import Heatmap from "./components/Heatmap.vue";

const rightNavDrawerStore = useRightNavDrawerStore();

function openHeatMap() {
  rightNavDrawerStore.toggleHeatMap(true);
}

function openEconomicCalendar() {
  rightNavDrawerStore.toggleEconomicCalendar(true);
}
</script>

<template>
  <Teleport to="body">
    <Heatmap v-if="rightNavDrawerStore.heatmap" />

    <EconomicCalendar v-if="rightNavDrawerStore.economicCalendar" />
  </Teleport>

  <nav
    id="right-nav-drawer"
    class="hidden flex-col items-center gap-y-1 border-l-4 px-1.5 py-2 md:flex"
  >
    <Tooltip
      id="open-market-watch-tooltip"
      trigger-id="open-market-watch-trigger-tooltip"
      class="rounded-md px-1.5 pb-1.5 pt-2 hover:bg-accent"
      :class="{
        'bg-blue-100 text-primary hover:bg-blue-100':
          rightNavDrawerStore.rightNavContentArea &&
          rightNavDrawerStore.rightNavContentAreaTab === 'market-watch-tab'
      }"
      placement="left"
      @click="rightNavDrawerStore.openMarketWatch"
    >
      <template #trigger>
        <ViewListOutlineSVG width="24" height="24" />
      </template>

      <template #content>Market Watch List & Details</template>
    </Tooltip>

    <Tooltip
      id="economic-calendar-tooltip"
      trigger-id="economic-calendar-trigger-tooltip"
      class="rounded-md px-1.5 pb-1.5 pt-2 hover:bg-accent"
      placement="left"
      @click="openEconomicCalendar"
    >
      <template #trigger>
        <CalenderMonthOutlineSVG width="24" height="24" />
      </template>

      <template #content>Economic Calendar</template>
    </Tooltip>

    <Tooltip
      id="heatmap-tooltip"
      trigger-id="heatmap-trigger-tooltip"
      class="rounded-md px-1.5 pb-1.5 pt-2 hover:bg-accent"
      placement="left"
      @click="openHeatMap"
    >
      <template #trigger>
        <FireSVG width="24" height="24" />
      </template>

      <template #content>Heatmap</template>
    </Tooltip>

    <Tooltip
      id="events-and-notifications-tooltip"
      trigger-id="events-and-notifications-trigger-tooltip"
      class="relative rounded-md px-1.5 pb-1.5 pt-2 hover:bg-accent"
      placement="left"
      :class="{
        'bg-blue-100 text-primary hover:bg-blue-100':
          rightNavDrawerStore.rightNavContentArea &&
          rightNavDrawerStore.rightNavContentAreaTab ===
            'events-and-notifications-tab'
      }"
      @click="rightNavDrawerStore.openEventsAndNotifications"
    >
      <template #trigger>
        <span
          class="absolute right-0.5 top-2 rounded-full bg-danger px-1.5 text-[10px] text-white"
          v-show="rightNavDrawerStore.eventAlertStatus"
        >
          !
        </span>

        <BellOutlineSVG width="24" height="24" />
      </template>

      <template #content>Events & Notifications</template>
    </Tooltip>

    <Tooltip
      id="chat-tooltip"
      trigger-id="chat-trigger-tooltip"
      class="relative rounded-md px-1.5 pb-1.5 pt-2 hover:bg-accent"
      placement="left"
      :class="{
        'bg-blue-100 text-primary hover:bg-blue-100':
          rightNavDrawerStore.rightNavContentArea &&
          rightNavDrawerStore.rightNavContentAreaTab === 'chat-tab'
      }"
      @click="rightNavDrawerStore.openChat"
    >
      <template #trigger>
        <span
          class="absolute right-0.5 top-2 rounded-full bg-danger px-1.5 text-[10px] text-white"
          v-show="rightNavDrawerStore.eventAlertStatus"
        >
          !
        </span>

        <MessageCircle class="size-6" />
      </template>

      <template #content>Events & Notifications</template>
    </Tooltip>
  </nav>
</template>
