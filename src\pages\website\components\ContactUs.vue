<script setup lang="ts">
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
</script>

<template>
  <section id="contact-us" class="bg-accent pb-24">
    <div class="container mx-auto px-5">
      <div
        class="grid grid-cols-12 rounded-md border bg-white p-5 shadow-lg sm:p-10"
      >
        <div class="col-span-12">
          <h2 class="mb-14 text-center text-4xl font-semibold">
            <span class="border-b-4 border-primary pb-4">Contact Us</span>
          </h2>
        </div>

        <div
          class="col-span-12 text-gray-600 lg:col-span-6 lg:border-r lg:py-3 lg:pr-10"
        >
          <p>
            At Tradeaway, we specialize in day trading the forex and CFD
            markets. Our team consists of seasoned day traders and software
            developers, each with over 10 years of experience in their
            respective fields. This experience has equipped us with a deep
            understanding of market dynamics. Our goal is to turn this knowledge
            into technical solutions that you can leverage to enhance your
            trading journey.
          </p>

          <p class="mt-2 font-semibold">- Tradeawaay</p>
        </div>

        <div
          class="col-span-12 mt-5 flex justify-between border-t py-3 lg:col-span-6 lg:mt-0 lg:border-l lg:border-t-0 lg:pl-10"
        >
          <div>
            <p class="text-gray-600">
              Need help or just want to ask us something?
            </p>

            <div class="mt-10">
              <a
                href="mailto:<EMAIL>"
                class="rounded-md bg-primary px-8 pb-3 pt-4 text-lg text-white hover:bg-secondary"
              >
                Email Us
              </a>
            </div>
          </div>

          <div class="text-gray-800 sm:pr-8">
            <FontAwesomeIcon
              class="text-6xl lg:text-7xl"
              icon="fa-regular fa-paper-plane"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
