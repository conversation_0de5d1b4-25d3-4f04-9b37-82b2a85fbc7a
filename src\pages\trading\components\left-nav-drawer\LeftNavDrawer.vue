<script setup lang="ts">
import { computed, ref, shallowRef } from "vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import { AvailableShapes } from "@/lib/night-vision/groups/ShapeTool";

import { useChartStore } from "@/store/chartStore";
import { useAppbarStore } from "@/store/appbarStore";
import { useCandleStickStore } from "@/store/candleStickStore";
import { useLeftNavDrawerStore } from "@/store/leftNavDrawerStore";
import { useBottomNavDrawerStore } from "@/store/bottomNavDrawerStore";

import PlaceNewOrder from "./components/PlaceNewOrder.vue";

import NavItem from "@/components/NavItem.vue";
import Dropdown from "@/components/Dropdown.vue";

import PlusSVG from "@/assets/svg/plus.svg";
import VectorLineSVG from "@/assets/svg/vector-line.svg";
import HorizontalLineSVG from "@/assets/svg/horizontal-line.svg";
import VerticalLineSVG from "@/assets/svg/vertical-line.svg";
import VectorRectangleSVG from "@/assets/svg/vector-rectangle.svg";
import CrosshairSVG from "@/assets/svg/crosshair.svg";
import PriceRangeSVG from "@/assets/svg/price-range.svg";
import DateRangeSVG from "@/assets/svg/date-range.svg";
import DatePriceRangeSVG from "@/assets/svg/date-price-range.svg";
import FIBLevelSVG from "@/assets/svg/fib-level.svg";
import FormatTextSVG from "@/assets/svg/format-text.svg";
import TrashCanOutlineSVG from "@/assets/svg/trash-can-outline.svg";

const chartStore = useChartStore();
const appbarStore = useAppbarStore();
const candleStickStore = useCandleStickStore();
const leftNavDrawerStore = useLeftNavDrawerStore();
const bottomNavDrawerStore = useBottomNavDrawerStore();

const drawingToolsDropdown = ref(false);
const drawingTools = shallowRef([
  {
    id: "trend-line",
    name: "Trend Line",
    icon: VectorLineSVG
  },
  {
    id: "horizontal-line",
    name: "Horizontal Line",
    icon: HorizontalLineSVG
  },
  {
    id: "vertical-line",
    name: "Vertical Line",
    icon: VerticalLineSVG
  },
  {
    id: "box",
    name: "Box",
    icon: VectorRectangleSVG
  },
  {
    id: "crosshair",
    name: "Crosshair",
    icon: CrosshairSVG
  },
  {
    id: "price-range",
    name: "Price Range",
    icon: PriceRangeSVG
  },
  {
    id: "date-range",
    name: "Date Range",
    icon: DateRangeSVG
  },
  {
    id: "date-and-price-range",
    name: "Date and Price Range",
    icon: DatePriceRangeSVG
  },
  {
    id: "fib-level",
    name: "FIB Level",
    icon: FIBLevelSVG
  }
]);
const selectedShapeTool = shallowRef({
  id: "trend-line",
  name: "Trend Line",
  icon: VectorLineSVG
});

const disablePlaceNewOrder = computed(() => {
  if (!candleStickStore.marketWatchOnlineStatus) {
    return true;
  }

  if (chartStore.chartLoader) {
    return true;
  }

  if (!candleStickStore.brokerInfo) {
    return;
  }

  let symbol = candleStickStore.marketWatchSymbol;

  if (symbol in candleStickStore.brokerInfo.mapping_symbols) {
    symbol = candleStickStore.brokerInfo.mapping_symbols[symbol].symbol;
  } else {
    symbol =
      candleStickStore.marketWatchSymbol.match(/[A-Z0-9]/g)?.join("") || "";
  }

  if (candleStickStore.symbol !== symbol) {
    return true;
  }

  return false;
});

function handleDrawNewShape(tool: typeof selectedShapeTool.value) {
  chartStore.isShapeToolSelected = true;

  selectedShapeTool.value = tool;

  chartStore.shapeToolInstance?.makeNewShape(tool.id as AvailableShapes);
}

function handleDrawText(id: AvailableShapes) {
  chartStore.shapeToolInstance?.makeNewShape(id);
}

function handlePlaceNewOrderToolbar() {
  bottomNavDrawerStore.toggleEditTradeModal(false);
  appbarStore.closeAllModals();

  leftNavDrawerStore.togglePlaceNewOrderModal(true);
}

function handleRemoveAllShapes() {
  /*
    The handleRemoveAllShapes() function should removes all the shapes from the chart at once.
    **Needs to be discussed.**
  */

  const currentDataName =
    candleStickStore.symbol + "_" + candleStickStore.interval;

  chartStore.selectedShape = undefined;

  chartStore.shapeToolInstance?.deleteAllShapes();

  chartStore.shapeToolInstance?.saveShapes(currentDataName);

  chartStore.chart?.update();
}
</script>

<template>
  <Teleport to="body">
    <template v-if="leftNavDrawerStore.placeNewOrderModal">
      <PlaceNewOrder />
    </template>
  </Teleport>

  <nav
    id="left-nav-drawer"
    class="flex flex-col items-center gap-y-1 border-r-4 px-1.5 py-2"
  >
    <NavItem
      class="relative !px-1.5"
      :class="{
        '!cursor-not-allowed': disablePlaceNewOrder
      }"
      :selected="leftNavDrawerStore.placeNewOrderModal"
      :disabled="disablePlaceNewOrder"
      @click="handlePlaceNewOrderToolbar"
    >
      <FontAwesomeIcon
        size="xs"
        icon="fa-solid fa-minus"
        class="absolute right-1"
        v-show="leftNavDrawerStore.isPlaceNewOrderModalMinimized"
      />

      <PlusSVG width="24" height="24" />
    </NavItem>

    <div class="relative">
      <NavItem
        :class="{
          'bg-accent': chartStore.isShapeToolSelected
        }"
        @click="handleDrawNewShape(selectedShapeTool)"
      >
        <component :is="selectedShapeTool.icon" />
      </NavItem>

      <Dropdown
        id="drawing-tools-dropdown"
        toggle-id="drawing-tools-trigger-dropdown"
        class="absolute -right-2 top-1/2 -translate-y-1/2 rounded-e-none rounded-s-lg !px-0.5 hover:bg-accent"
        :class="{
          'bg-accent': drawingToolsDropdown
        }"
        :icon="false"
        :offset-distance="-84"
        :offset-skidding="136"
        @show="drawingToolsDropdown = true"
        @hide="drawingToolsDropdown = false"
      >
        <template #text>
          <FontAwesomeIcon icon="fa-solid fa-chevron-right" size="sm" />
        </template>

        <template #content="{ close }">
          <div class="my-1 w-64">
            <div
              class="flex px-3 pb-2 pt-3 hover:bg-accent"
              v-for="tool in drawingTools"
              :key="tool.id"
              @click="(close(), handleDrawNewShape(tool))"
            >
              <div class="w-9">
                <component :is="tool.icon" />
              </div>

              {{ tool.name }}
            </div>
          </div>
        </template>
      </Dropdown>
    </div>

    <NavItem class="!px-1.5" @click="handleDrawText('text')">
      <FormatTextSVG width="24" height="24" />
    </NavItem>

    <NavItem class="!px-1.5" @click="handleRemoveAllShapes">
      <TrashCanOutlineSVG width="24" height="24" />
    </NavItem>
  </nav>
</template>
