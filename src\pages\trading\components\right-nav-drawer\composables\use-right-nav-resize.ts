import { computed, onMounted, onUnmounted, ref, watch } from "vue";

import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";

export function useRightNavResize() {
  const rightNavDrawerStore = useRightNavDrawerStore();
  const minWidth = computed(() => {
    return rightNavDrawerStore.rightNavContentAreaTab === "market-watch-tab"
      ? 400
      : 250;
  });
  const maxWidth = 800; // Maximum width constraint
  const isDragging = ref(false);
  const startX = ref(0);
  const startWidth = ref(0);

  // Adjust drawer width when tab is changed
  watch(
    () => rightNavDrawerStore.rightNavContentAreaTab,
    (newTab) => {
      if (newTab === "market-watch-tab") {
        minWidth.value = 400;

        if (rightNavDrawerStore.rightNavContentAreaWidth < 400) {
          rightNavDrawerStore.rightNavContentAreaWidth = 400;
        }
      }
    }
  );

  // Handle mouse down on the resize handle
  const handleMouseDown = (e: MouseEvent) => {
    isDragging.value = true;
    startX.value = e.clientX;
    startWidth.value = rightNavDrawerStore.rightNavContentAreaWidth;
    document.body.style.cursor = "ew-resize";
    document.body.style.userSelect = "none"; // Prevent text selection during resize
  };

  // Handle mouse move for resizing
  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.value) return;

    // Calculate the distance moved and the new width
    const dx = startX.value - e.clientX; // Reversed direction for left side resize
    let newWidth = startWidth.value + dx;

    // Apply constraints
    if (newWidth < minWidth.value) newWidth = minWidth.value;
    if (newWidth > maxWidth) newWidth = maxWidth;

    rightNavDrawerStore.rightNavContentAreaWidth = newWidth;
  };

  // Handle mouse up to stop resizing
  const handleMouseUp = () => {
    isDragging.value = false;
    document.body.style.cursor = "";
    document.body.style.userSelect = "";
  };

  // Add and remove event listeners
  onMounted(() => {
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  });

  onUnmounted(() => {
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
  });

  return { handleMouseDown };
}
