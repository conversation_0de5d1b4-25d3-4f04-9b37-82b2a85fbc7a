<script setup lang="ts">
import { h, onMounted, onUnmounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

import { AxiosError } from "axios";
import { toast } from "vue3-toastify";

import EventNotificationToast from "@/pages/trading/components/right-nav-drawer/components/EventNotificationToast.vue";

import { useCandleStickStore } from "@/store/candleStickStore";
import { useRightNavDrawerStore } from "@/store/rightNavDrawerStore";
import { useUserStore } from "@/store/userStore";

import ElfsightWidget from "@/components/ElfsightWidget.vue";
import NoInternetConnection from "@/components/NoInternetConnection.vue";

import {
  calculateExpectedLoss,
  calculateExpectedProfit,
  calculateMarginForActiveOrders
} from "@/helpers/chartCalculation";

import { EActiveOrders, MTradeServerReturnCodes } from "@/types/enums";

import { closeOHLCSocketConnection, handleOHLCBars } from "./socketio/ohlc";
import { axios } from "@/api";
import {
  eaSocket,
  initializeEASocket,
  initializeSocket,
  ohlcSocket,
  socket
} from "@/socketio";
import { Broker, EventNews, IActiveOrders, MarketWatchSymbol } from "@/types";
import { useOnline } from "@vueuse/core";
import { Users } from "lucide-vue-next";

const route = useRoute();
const router = useRouter();

const userStore = useUserStore();
const candleStickStore = useCandleStickStore();
const rightNavDrawerStore = useRightNavDrawerStore();

const onlineStatus = useOnline();

let selectedBrokerList: Broker[] = [];
let brokerIdx = 0;
let customActiveOrderFields: {
  ticket: number;
  tp1: number;
  tp1_status: string;
  vol1: number;
  vol1_status: string;
  sl_trail: number;
  margin: number;
  ref_margin_currency: number;
}[] = [];

onMounted(() => {
  intializeAxiosInterceptors();

  userStore.onlineStatus = onlineStatus.value;

  // When a user logs out from one tab, force all other tabs to logout.
  window.addEventListener("storage", function (event) {
    if (event.key === "loggedOut" && event.newValue === "false") {
      router.push({ name: "home" });
    }

    if (event.key === "loggedOut" && event.newValue === null) {
      userStore.logOut(false);

      router.push({ name: "login" });
    }
  });
});

onUnmounted(() => {
  closeMarketWatchConnection();
  closeOHLCSocketConnection();
});

watch(
  () => userStore.user,
  (user) => {
    if (user && userStore.userAccessToken) {
      handleOHLCBars(userStore.userAccessToken);
      handleNews();
      return;
    }

    // If user is null, this means user has logged out.
    // Peform necessary clean up here.
    closeMarketWatchConnection();
    closeOHLCSocketConnection();
    closeSocketConnection();
  }
);

watch(
  () => userStore.eaAccessToken,
  (newEAToken) => {
    closeMarketWatchConnection();

    if (newEAToken) {
      handleMarketWatch();
      return;
    }
  }
);

watch(
  () => [
    candleStickStore.symbol,
    candleStickStore.interval,
    candleStickStore.symbolBroker
  ],
  ([newSymbol, newPeriod, newBroker], [oldSymbol, oldPeriod, oldBroker]) => {
    ohlcSocket?.emit("unsubscribe", {
      symbol: oldSymbol,
      period: oldPeriod,
      broker: oldBroker
    });

    ohlcSocket?.emit("subscribe", {
      symbol: newSymbol,
      period: newPeriod,
      broker: newBroker
    });

    // Box Plot
    if (oldSymbol !== newSymbol) {
      ohlcSocket?.emit("unsubscribe", {
        symbol: oldSymbol,
        period: "PERIOD_D1",
        broker: oldBroker
      });

      ohlcSocket?.emit("subscribe", {
        symbol: newSymbol,
        period: "PERIOD_D1",
        broker: newBroker
      });
    }
  }
);

watch(
  () => route.name,
  (newRouteName) => {
    if (newRouteName === "trading") {
      getSymbols();
      getBrokerList();
    }
  }
);

watch(onlineStatus, (status) => {
  if (status) {
    location.reload();
    return;
  }

  userStore.onlineStatus = status;

  closeOHLCSocketConnection();
  closeSocketConnection();
  closeMarketWatchConnection();
});

function intializeAxiosInterceptors() {
  axios.interceptors.response.use(
    function (response) {
      return response;
    },
    async function (error: AxiosError) {
      if (error?.response?.status === 401) {
        userStore.logOut(false);

        router.push({ name: "login" });
      }

      if (error?.response?.status === 503) {
        if (userStore.user) {
          await userStore.logOut();
        }

        await router.push({ name: "renew-subscription" });
      }

      return Promise.reject(error);
    }
  );
}

async function getSymbols() {
  try {
    const resp = await axios.get("/symbols");

    candleStickStore.symbolList = resp.data.data;
  } catch (e) {
    console.error("Cannot fetch symbols:", e);
  }
}

async function getBrokerList() {
  try {
    const resp = await axios.get("/brokers");

    candleStickStore.brokerList = resp.data.data;
  } catch (e) {
    console.error(e);
  }
}

if (userStore.userAccessToken) {
  handleOHLCBars(userStore.userAccessToken);
}
///// while refreshing currency  data 
function handleMarketWatch() {

  initializeEASocket(userStore.eaAccessToken!);

  candleStickStore.createMarketWatchTimer();

  eaSocket?.emit(`subscribe_market_${userStore.eaAccessToken}`);

  eaSocket?.emit(`subscribe_trade_${userStore.eaAccessToken}`);

  eaSocket?.emit("subscribe:order");
// while refreshing currency data from the database
  eaSocket?.on(`market_update_${userStore.eaAccessToken}`, (d) => {
    console.log("market wathc",d);
    candleStickStore.resetMarketWatchTimer();

    const brokerName = d["account_info"]["broker"];

    if (d && d["account_info"]) {
      candleStickStore.eaAccount = {
        ...candleStickStore.eaAccount,
        ...d["account_info"]
      };
    }

    if (d["status"] === "SYMBOL_UPDATED") {
      candleStickStore.marketWatchSymbolList = d["symbol_data"];

      if (candleStickStore.brokerInfo === null) {
        const brokerInfo = candleStickStore.brokerList.find(
          (v) =>
            v.broker === brokerName &&
            v.instruments[0] === d["symbol_data"][0]["symbol"]
        );

        if (brokerInfo) {
          candleStickStore.brokerInfo = {
            broker: brokerInfo.broker,
            broker_type: brokerInfo.type,
            display_name: brokerInfo.display_name,
            forex_chart_broker: brokerInfo.forex_chart_broker,
            mapping_symbols: brokerInfo.mapping_symbols
          };
        }
      }

      const mappedSymbols = candleStickStore.brokerInfo?.mapping_symbols;

      const marketWatchSymbolIdx = d["symbol_data"].findIndex(
        (item: MarketWatchSymbol) => {
          let symbol = item.symbol.match(/[A-Z0-9]/g)?.join("") || "";

          if (mappedSymbols) {
            for (let [k, v] of Object.entries(mappedSymbols)) {
              if (k === item.symbol && v.symbol === candleStickStore.symbol) {
                symbol = v.symbol;
              }
            }
          }

          return symbol === candleStickStore.symbol;
        }
      );

      const item: MarketWatchSymbol = d["symbol_data"][marketWatchSymbolIdx];

      candleStickStore.marketWatchSymbol = item.symbol;
      candleStickStore.bidPrice = item.bid;
      candleStickStore.bidPriceColor = item.bid_color;
      candleStickStore.askPrice = item.ask;
      candleStickStore.askPriceColor = item.ask_color;
      candleStickStore.dailyChange = item.daily_change;
      candleStickStore.dailyChangeColor = item.daily_change_color;
      candleStickStore.spread = item.spread;
      candleStickStore.contractSize = item.symbol_contract_size;
      candleStickStore.bidHighPrice = item.symbol_bid_high;
      candleStickStore.bidLowPrice = item.symbol_bid_low;
      candleStickStore.sessionOpenPrice = item.symbol_session_open;
      candleStickStore.maxVolume = item.symbol_volume_max;
      candleStickStore.minVolume = item.symbol_volume_min;
      candleStickStore.volumeStep = item.symbol_volume_step;
      candleStickStore.digit = item.symbol_digit;

      let dailyChange = "";

      if (item.daily_change > 0) {
        dailyChange = `▲ ${item.daily_change.toFixed(2)}%`;
      } else {
        dailyChange = `▼ ${item.daily_change.toFixed(2)}%`;
      }

      document.title = `${item.symbol} ${item.bid} ${dailyChange}`;
         console.log("instruments list sent to metatrader",selectedBrokerList[brokerIdx])
    } else {
      console.log("else block running  ")
      handleMarketWatchBrokerAccount(brokerName);
    }
  });

  eaSocket?.on(`trade_update_${userStore.eaAccessToken}`, (d) => {
    // console.log(d)

    if (d["retcode"]) {
      handleActiveTradeOrderCustomFields(d);
      return;
    }

    if (d["open_trades"] && d["open_trades"]["account_info"]) {
      candleStickStore.eaAccount = {
        ...candleStickStore.eaAccount,
        ...d["open_trades"]["account_info"][0]
      };
    }

    if (d["open_trades"] && d["open_trades"]["active_orders"]) {
      const activeOrders: IActiveOrders[] = d["open_trades"]["active_orders"];

      candleStickStore.activeOrders = activeOrders.map((order, idx) => {
        const { symbol, type, sl, tp, volume, open_price } = order;

        const tradeType = EActiveOrders[type as keyof typeof EActiveOrders];

        let customOrder = {};

        let tpVolume = volume;
        let tp1Profit = 0;

        if (customActiveOrderFields[idx]) {
          const {
            ref_margin_currency,
            tp1,
            tp1_status,
            vol1,
            vol1_status,
            sl_trail
          } = customActiveOrderFields[idx];

          const margin = calculateMarginForActiveOrders({
            orderType: tradeType,
            refMarginCurrency: ref_margin_currency,
            marketPrice: open_price,
            symbol,
            volume,
            leverage: candleStickStore.eaAccount?.leverage,
            marketWatchSymbolList: candleStickStore.marketWatchSymbolList
          });

          if (vol1 && vol1_status === "not_executed") {
            tpVolume = volume - vol1;

            tp1Profit = calculateExpectedProfit({
              orderType: tradeType,
              price: open_price,
              symbol,
              tp: tp1,
              volume: vol1,
              marketWatchSymbolList: candleStickStore.marketWatchSymbolList,
              accountCurrency: candleStickStore.eaAccount?.currency
            });
          }

          customOrder = {
            tp1,
            tp1_status,
            vol1,
            vol1_status,
            sl_trail,
            margin
          };
        }

        const tpProfit = calculateExpectedProfit({
          orderType: tradeType,
          price: open_price,
          symbol,
          tp,
          volume: tpVolume,
          marketWatchSymbolList: candleStickStore.marketWatchSymbolList,
          accountCurrency: candleStickStore.eaAccount?.currency
        });

        const expProfit = parseFloat((tpProfit + tp1Profit).toFixed(2));

        const expLoss = calculateExpectedLoss({
          orderType: tradeType,
          price: open_price,
          symbol,
          sl,
          volume,
          marketWatchSymbolList: candleStickStore.marketWatchSymbolList,
          accountCurrency: candleStickStore.eaAccount?.currency
        });

        return {
          ...order,
          ...customOrder,
          exp_profit: expProfit,
          exp_loss: expLoss
        };
      });
    } else {
      candleStickStore.activeOrders = [];
    }

    if (d["open_trades"] && d["open_trades"]["placed_order"]) {
      candleStickStore.placedOrders = d["open_trades"]["placed_order"];
    } else {
      candleStickStore.placedOrders = [];
    }
  });
  eaSocket?.on(`order_${userStore.eaAccessToken}`, (d) => {
    // console.log("database order streaming",d)
    customActiveOrderFields = d.filter((v: any) => {
      const type = EActiveOrders[v.type as keyof typeof EActiveOrders];

      if (
        type === EActiveOrders.POSITION_TYPE_SELL ||
        type === EActiveOrders.POSITION_TYPE_BUY
      ) {
        return true;
      }

      return false;
    });
  });
}

function handleMarketWatchBrokerAccount(brokerName: string) {
  // 
  if (selectedBrokerList.length === 0) {
    selectedBrokerList = candleStickStore.brokerList.filter(
      (v) => v.broker === brokerName
    );
  }

  if (!selectedBrokerList[brokerIdx]) {
    return;
  }

  candleStickStore.brokerInfo = {
    broker: selectedBrokerList[brokerIdx].broker,
    broker_type: selectedBrokerList[brokerIdx].type,
    display_name: selectedBrokerList[brokerIdx].display_name,
    forex_chart_broker: selectedBrokerList[brokerIdx].forex_chart_broker,
    mapping_symbols: selectedBrokerList[brokerIdx].mapping_symbols
  };

  eaSocket?.emit(`market_data_${userStore.eaAccessToken}`, {
    symbols: selectedBrokerList[brokerIdx].instruments
  });

  brokerIdx++;
}

function handleActiveTradeOrderCustomFields(d: any) {
  const code = d["retcode"] as keyof typeof MTradeServerReturnCodes;

  const message = MTradeServerReturnCodes[code];

  if (d["retcode"] === "10009") {
    let obj = {
      ref_margin_currency: candleStickStore.refMarginCurrency
    } as any;

    if (candleStickStore.updateTP1) {
      obj = {
        ...obj,
        tp1: candleStickStore.tp1,
        tp1_status: "not_executed",
        vol1: candleStickStore.tp1_vol,
        vol1_status: "not_executed"
      };
    }

    if (candleStickStore.updateSLTrial) {
      obj = {
        ...obj,
        sl_trail: candleStickStore.slTrail
      };
    }

    const mt5Id = candleStickStore.eaAccount?.mt5_id;

    if (
      d["message"] === "Order Placed Successfully." ||
      d["message"] === "Pending Order Created Successfully."
    ) {
      // Here, delay is needed since the trade order that was placed doesn't get instantly executed by EA.
      setTimeout(() => {
        axios.put(`/orders/${d["order"]}/${mt5Id}`, obj);
      }, 2000);
    }
  }

  toast.info(`${d["message"]}\n${message}`);
}

function handleNews() {
  initializeSocket(userStore.userAccessToken!);

  socket?.emit("subscribe:news");
  socket?.emit("subscribe:announcements");

  socket?.on("news", (d: EventNews) => {
    rightNavDrawerStore.eventAlertStatus = true;

    toast.info(() => h(EventNotificationToast, { event: d }));

    rightNavDrawerStore.newsList = [d, ...rightNavDrawerStore.newsList];
  });

  socket?.on("announcements", (d: EventNews) => {
    rightNavDrawerStore.eventAlertStatus = true;

    toast.info(() => h(EventNotificationToast, { event: d }));

    rightNavDrawerStore.announcementList = [
      d,
      ...rightNavDrawerStore.announcementList
    ];
  });

  socket?.on(`announcements_${userStore.userAccessToken}`, (d: EventNews) => {
    rightNavDrawerStore.eventAlertStatus = true;

    toast.info(() => h(EventNotificationToast, { event: d }), {
      autoClose: 1000
    });

    rightNavDrawerStore.announcementList = [
      d,
      ...rightNavDrawerStore.announcementList
    ];
  });
}

function closeMarketWatchConnection() {
  eaSocket?.disconnect();

  candleStickStore.clearMarketWatchTimer();

  selectedBrokerList = [];
  brokerIdx = 0;
}

function closeSocketConnection() {
  socket?.disconnect();
}
</script>

<template>
  <Teleport to="body">
    <NoInternetConnection v-if="!userStore.onlineStatus" />

    <ElfsightWidget />
  </Teleport>

  <template v-if="userStore.onlineStatus"> 
    <router-view></router-view>
  </template>
</template>
