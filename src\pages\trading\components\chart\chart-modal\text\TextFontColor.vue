<script setup lang="ts">
import { onMounted, ref } from "vue";
import { IBaseShapeOptions } from "@/lib/night-vision/shapes/shapes/BaseShape";

import { ChartInstanceError, ShapeInstanceError } from "@/helpers/errors";
import { useChartStore } from "@/store/chartStore";

import Dropdown from "@/components/Dropdown.vue";
import ColorPicker from "@/components/ColorPicker.vue";

import FormatTextSVG from "@/assets/svg/format-text.svg";

const chartStore = useChartStore();

const selectedShape: IBaseShapeOptions<"text"> | undefined =
  chartStore.selectedShape;

const fontColorDropdown = ref(false);
const selectedFontColor = ref("");

onMounted(() => {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to set font color for text shape");
  }

  selectedFontColor.value = selectedShape.properties.textProperties.font_color;
});

function handleFontColor(fontColor: string) {
  if (!selectedShape) {
    throw new ShapeInstanceError("Unable to change font color for text shape");
  }

  if (!chartStore.chart) {
    throw new ChartInstanceError("Unable to change font color for text shape");
  }

  selectedFontColor.value = fontColor;

  selectedShape.setProperty("textProperties", "font_color", fontColor);

  chartStore.chart.update();
}
</script>

<template>
  <Dropdown
    id="chart-modal-text-font-color-dropdown"
    toggle-id="chart-modal-text-font-color-toggle-dropdown"
    class="border px-3"
    :class="{ 'border-info bg-accent': fontColorDropdown }"
    :icon="false"
    :offset-skidding="106"
    @show="fontColorDropdown = true"
    @hide="fontColorDropdown = false"
  >
    <template #text>
      <FormatTextSVG />

      <div
        class="mt-0.5 h-1 w-5"
        :style="{ backgroundColor: selectedFontColor }"
      ></div>
    </template>

    <template #content>
      <ColorPicker :color="selectedFontColor" @update-color="handleFontColor" />
    </template>
  </Dropdown>
</template>
