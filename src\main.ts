import { createApp } from "vue";
import { createRouter, createWebHistory } from "vue-router";

import { createPinia } from "pinia";
import Vue3Toasity, { type ToastContainerOptions } from "vue3-toastify";

import { useAuth } from "@/composables/useAuth";

import App from "./App.vue";
import "./icons";
import "./style.css";
import { routes } from "@/routes";
import "vue3-toastify/dist/index.css";

const pinia = createPinia();

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to) {
    if (to.hash) {
      return { el: to.hash, behavior: "smooth" };
    }

    return { top: 0, behavior: "smooth" };
  }
});

router.beforeEach(async (to, _) => {
  const isUserLoggedIn = await useAuth();

  if (to.meta.requiresAuth && !isUserLoggedIn) {
    return {
      name: "login",
      query: { redirect: to.fullPath }
    };
  } else if (to.meta.requiresGuest && isUserLoggedIn) {
    return {
      name: "home"
    };
  }
});

const toastifyOptions: ToastContainerOptions = {
  autoClose: 2000,
  transition: "slide",
  pauseOnHover: false,
  position: "bottom-right",
  multiple: false
};

const app = createApp(App)
  .use(pinia)
  .use(router)
  .use(Vue3Toasity, toastifyOptions)
  
if (import.meta.env.DEV) {
  app.config.errorHandler = (err, instance, info) => {
    console.error(err);
    console.error(instance);
    console.error(info);

    //TODO: Link to external error handling service like Sentry.
  };
}

function initGoogleAnalytics() {
  if (import.meta.env.VITE_APP_ENV !== "production") {
    return;
  }

  const gaId = import.meta.env.VITE_APP_GOOGLE_ANALYTICS_TAG;

  const script = document.createElement("script");

  script.async = true;

  script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;

  document.head.appendChild(script);

  script.onload = () => {
    // @ts-expect-error
    window.dataLayer = window.dataLayer || [];

    function gtag() {
      // @ts-expect-error
      window.dataLayer.push(arguments);
    }

    // @ts-expect-error
    gtag("js", new Date());

    // @ts-expect-error
    gtag("config", gaId);
  };
}

initGoogleAnalytics();
app.mount("#app");
